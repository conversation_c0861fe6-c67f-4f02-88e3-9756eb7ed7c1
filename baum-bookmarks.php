<?php 
/** 
 * Plugin Name: <PERSON><PERSON>'s Bookmarks 
 * Description: <PERSON><PERSON> bookmarks and saved stories efficiently 
 * Version: 1.0 
 * Author: <PERSON> 
 */ 

// Include functions 
require_once plugin_dir_path(__FILE__) . 'shortcodes.php'; 
require_once plugin_dir_path(__FILE__) . 'customizer.php'; 

// Enqueue scripts and styles 
function baum_bookmarks_enqueue_scripts () { 

  wp_enqueue_style( 
    'baum-bookmarks-css', 
    plugin_dir_url(__FILE__) . 'css/baum-bookmarks.css' 
  ); 

  wp_enqueue_script( 
    'baum-bookmarks-js', 
    plugin_dir_url(__FILE__) . 'js/baum-bookmarks.js', 
    array('jquery'), 
    '1.0', 
    true 
  ); 

  // $bool = is_page_template('page-starred.php') ? true : false; 
  $is_page = is_page_template('page-starred.php') || is_author() ? true : false; 
  $is_user_logged_in = is_user_logged_in(); 


  // Assume you want to pass both IDs
  $author_id = is_author() ? get_author_page_author_id() : 0; 
  $current_user_id = get_current_logged_in_user_id(); 
  $my_bookmarks = get_user_meta($current_user_id, 'baum_bookmarks', true); 

  // Data to pass to script 
  $data = array( 
    'icon'                => get_theme_mod('baum_bookmark_icon', 'star'), 
    'ajax_url'            => admin_url('admin-ajax.php'), 
    'nonce'               => wp_create_nonce('get_baum_bookmarks'), 
    'save_nonce'          => wp_create_nonce('save_baum_bookmarks'),
    'author_id'           => $author_id, 
    'current_user_id'     => $current_user_id, 
    'is_page'             => $is_page, 
    'is_user_logged_in'   => $is_user_logged_in, 
    'my_bookmarks'        => $my_bookmarks,
  ); 

  wp_localize_script('baum-bookmarks-js', 'baum_bookmarks', $data); 
  
  // [ 
  //   'ajax_url' => admin_url('admin-ajax.php'), 
  //   'nonce' => wp_create_nonce('get_baum_bookmarks'), 
  //   // 'story_id' => get_the_ID(), 
  //   'is_page' => $bool, 
  //   'icon' => get_theme_mod('baum_bookmark_icon', 'star') 
  // ]

} 

add_action('wp_enqueue_scripts', 'baum_bookmarks_enqueue_scripts'); 

function save_baum_bookmarks () {
  // Check for logged-in user and verify the nonce for saving bookmarks
  if (isset($_POST['save_nonce']) && wp_verify_nonce($_POST['save_nonce'], 'save_baum_bookmarks')) {
      if (!is_user_logged_in()) {
          wp_die('User must be logged in to save bookmarks.', '', [
            'response' => 403
          ]);
      }

      // Retrieve and sanitize the bookmarks from POST
      $my_bookmarks = isset($_POST['my_bookmarks']) ? sanitize_text_field($_POST['my_bookmarks']) : '';
      error_log('my_bookmarks: ' . $my_bookmarks);

      // Save the bookmarks to user meta
      update_user_meta(get_current_user_id(), 'baum_bookmarks', $my_bookmarks);

      wp_send_json_success([ 'message' => 'Bookmarks saved successfully' ]);
  } else {
      wp_die('Invalid nonce.', '', [ 'response' => 403 ]);
  }
}

add_action('wp_ajax_save_baum_bookmarks', 'save_baum_bookmarks'); 
add_action('wp_ajax_action_save_baum_bookmarks', 'save_baum_bookmarks'); 
add_action('wp_ajax_nopriv_action_save_baum_bookmarks', 'save_baum_bookmarks'); 

function get_baum_bookmarks () {
  if (isset($_POST['nonce'])) { 
    $verify = wp_verify_nonce($_POST['nonce'], 'get_baum_bookmarks'); 
  } else { 
    $verify = 0; 
  } 

  // Check if the nonce is set and valid
  if (!$verify) {
    wp_die(__('Nonce verification ' . $verify, 'baum'), '', [ 
      'response' => 403 
    ]);
  }

  $user_id = isset($_POST['author_id']) 
    ? sanitize_text_field($_POST['author_id']) 
    : get_current_user_id(); 
  error_log('user_id: ' . $user_id);

  // // Retrieve and sanitize the bookmarks from POST
  // $bookmarks = isset($_POST['bookmarks']) 
  //   ? sanitize_text_field($_POST['bookmarks']) 
  //   : get_user_meta($user_id, 'baum_bookmarks', true); 

  $bookmarks = get_user_meta($user_id, 'baum_bookmarks', true); 
  error_log('bookmarks: ' . $bookmarks);

  if (!empty($bookmarks)) {
    // Convert to an array of integers
    $bookmarks = array_map('intval', explode(',', $bookmarks)); 
    $atts = [ 
      'size' => 'small', 
      'txtcolor' => 'standard', 
      'bgcolor' => 'standard', 
      'fav_btn' => true, 
    ];
    echo "<div id='baum-bookmarks' "
      . "class='baum-cards baum-cards-" . esc_attr($atts['size']) . "'>"; 
    $the_query = new WP_Query([ 
      'post_type' => 'post', 
      'posts_per_page' => 100, 
      'post__in' => $bookmarks, 
      'orderby' => 'post__in', 
    ]); 
    while ($the_query->have_posts()) { 
      $the_query->the_post(); 
      get_template_part('parts/baum', 'card', $atts); 
    } 
    wp_reset_postdata(); 
    echo '</div>'; 
  } 
  wp_die(); // this is required to return a proper result 
}

add_action('wp_ajax_action_get_baum_bookmarks', 'get_baum_bookmarks'); 
add_action('wp_ajax_nopriv_action_get_baum_bookmarks', 'get_baum_bookmarks'); 

function get_author_page_author_id() {
  if (is_author()) {
      global $wp_query;
      $author_id = $wp_query->get_queried_object_id();
      return $author_id;
  }
  return null;
}

function get_current_logged_in_user_id() {
  return get_current_user_id();
}
