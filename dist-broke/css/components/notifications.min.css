.menu-item-notifications{position: relative !important}.menu-item-notifications .notifications-trigger{position: relative}.notification-badge{position: absolute;top: -5px;right: -5px;background: #ff4444;color: white;border-radius: 50%;width: 18px;height: 18px;font-size: 10px;display: flex;align-items: center;justify-content: center;font-weight: bold;z-index: 10}.baum-apple-menu .notifications-dropdown{position: absolute;top: 100%;right: 0;background: var(--color-white);border: 1px solid var(--color-septenary);border-radius: var(--border-radius);box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);min-width: 350px;max-width: 400px;min-height: 200px;max-height: 80vh;padding: 0;z-index: 9999;margin-top: 10px;list-style: none;display: flex;flex-direction: column;overflow: hidden;visibility: hidden;opacity: 0;pointer-events: none;transform: translateY(-10px);transition: visibility 0s linear 0.2s, opacity 0.2s ease, transform 0.2s ease, pointer-events 0s linear 0.2s}.baum-apple-menu .notifications-dropdown.show{visibility: visible;opacity: 1;pointer-events: auto;transform: translateY(0);transition: opacity 0.2s ease, transform 0.2s ease}.notifications-header{padding: 15px !important;border-bottom: 1px solid var(--color-septenary);margin: 0 !important;flex-shrink: 0;background: var(--color-white);display: flex !important;justify-content: space-between !important;align-items: center !important}.notifications-header h4{margin: 0 !important;font-size: 16px !important;color: var(--color-accent) !important;font-weight: 600 !important;flex: 1}.notifications-close{background: none;border: none;color: var(--color-senary);cursor: pointer;padding: 4px;border-radius: 4px;transition: all 0.2s ease;width: 24px;height: 24px;display: flex;align-items: center;justify-content: center;flex-shrink: 0}.notifications-close:hover{color: var(--color-accent);background: var(--color-denary)}.notifications-close i{font-size: 12px}.notifications-list-container{padding: 0 !important;margin: 0 !important;flex: 1;overflow: hidden;min-height: 120px;display: flex;flex-direction: column}.notifications-list{flex: 1;overflow-y: auto;overflow-x: hidden;padding: 0;margin: 0;scrollbar-width: thin;scrollbar-color: var(--color-senary) var(--color-nonary)}.notifications-list::-webkit-scrollbar{width: 8px}.notifications-list::-webkit-scrollbar-track{background: var(--color-nonary);border-radius: 4px}.notifications-list::-webkit-scrollbar-thumb{background: var(--color-senary);border-radius: 4px;transition: background 0.2s ease}.notifications-list::-webkit-scrollbar-thumb:hover{background: var(--color-quinary)}.notification-item{padding: 12px 15px;border-bottom: 1px solid var(--color-septenary);display: flex;align-items: flex-start;gap: 12px;transition: background 0.2s ease;cursor: pointer;min-height: 60px}.notification-item:hover{background: var(--color-denary)}.no-notifications{padding: 40px 20px;text-align: center;color: var(--color-senary);font-style: italic;min-height: 100px;display: flex;align-items: center;justify-content: center}.notification-item[data-link]{cursor: pointer}.notification-icon{width: 32px;height: 32px;border-radius: 50%;display: flex;align-items: center;justify-content: center;flex-shrink: 0}.notification-icon i{color: white;font-size: 14px}.notification-content{flex: 1;min-width: 0}.notification-text{font-size: 13px;color: #333;line-height: 1.4;margin-bottom: 4px}.notification-text strong{font-weight: 600}.notification-time{font-size: 11px;color: #666}.notification-delete{background: none;border: none;color: #999;cursor: pointer;padding: 4px;border-radius: 3px;opacity: 0;transition: opacity 0.2s ease;flex-shrink: 0}.notification-item:hover .notification-delete{opacity: 0.5}.notification-delete:hover{opacity: 1 !important;color: #ff4444 !important}.notification-delete i{font-size: 12px}.no-notifications{padding: 20px;text-align: center;color: #999;font-size: 13px}.notifications-footer{padding: 12px 15px !important;border-top: 1px solid var(--color-septenary);text-align: center;margin: 0 !important;flex-shrink: 0;background: var(--color-white);display: flex;justify-content: space-between;align-items: center;gap: 10px}.clear-all-btn, .load-more-btn, .view-all-btn{background: none;border: none;color: var(--color-blue);font-size: 12px;cursor: pointer;text-decoration: underline;padding: 4px 8px;border-radius: 4px;transition: all 0.2s ease}.clear-all-btn:hover, .load-more-btn:hover, .view-all-btn:hover{color: var(--color-indigo);background: var(--color-denary);text-decoration: none}.load-more-btn{width: 100%;padding: 10px;margin: 5px 0;border-top: 1px solid var(--color-septenary);color: var(--color-senary);font-size: 11px}.load-more-btn:hover{background: var(--color-nonary);color: var(--color-accent)}.load-more-btn.loading{opacity: 0.6;cursor: not-allowed}.load-more-btn.loading::after{content: " ⟳";animation: spin 1s linear infinite}@keyframes spin{from{transform: rotate(0deg)}to{transform: rotate(360deg)}}.notification-item.removing{opacity: 0;transform: translateX(100%);transition: opacity 0.3s ease, transform 0.3s ease}.baum-apple-menu .menu-item-notifications{position: relative !important}.baum-apple-menu .notifications-dropdown{list-style: none !important}.baum-apple-menu .notifications-dropdown li{list-style: none !important;margin: 0 !important;padding: 0 !important}.baum-apple-menu .sub-menu.notifications-dropdown{background: var(--color-white) !important;border: 1px solid var(--color-septenary) !important;border-radius: var(--border-radius) !important;box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;min-width: 350px !important;max-width: 400px !important;min-height: 200px !important;max-height: 80vh !important;padding: 0 !important;z-index: 9999 !important;margin-top: 10px !important;position: absolute !important;top: 100% !important;right: 0 !important;display: flex !important;flex-direction: column !important;overflow: hidden !important}.baum-apple-menu .sub-menu.notifications-dropdown li{height: auto !important;min-height: auto !important;padding: 0 !important;margin: 0 !important}.baum-apple-menu .sub-menu.notifications-dropdown .notification-item{height: auto !important;min-height: 60px !important;padding: 12px 15px !important;display: flex !important;align-items: flex-start !important}.baum-apple-menu .sub-menu.notifications-dropdown .notifications-header{height: auto !important;padding: 15px !important;flex-shrink: 0 !important}.baum-apple-menu .sub-menu.notifications-dropdown .notifications-list-container{height: auto !important;flex: 1 !important;min-height: 120px !important;overflow: hidden !important;display: flex !important;flex-direction: column !important}.baum-apple-menu .sub-menu.notifications-dropdown .notifications-list{height: auto !important;flex: 1 !important;overflow-y: auto !important;overflow-x: hidden !important}.baum-apple-menu .sub-menu.notifications-dropdown .notifications-footer{height: auto !important;padding: 12px 15px !important;flex-shrink: 0 !important;display: flex !important;justify-content: space-between !important;align-items: center !important}.baum-apple-menu .sub-menu.notifications-dropdown .no-notifications{height: auto !important;min-height: 100px !important;padding: 40px 20px !important;display: flex !important;align-items: center !important;justify-content: center !important}@media (max-width: 768px){.notifications-dropdown{min-width: 300px !important;max-width: 350px !important;right: -50px !important}.notification-text{font-size: 12px}.notification-time{font-size: 10px}}