#baum-radio-player-bar{position: fixed;bottom: 0;left: 0;right: 0;background: var(--color-background);border-top: 2px solid var(--color-accent);z-index: 9999;transition: transform 0.3s ease, opacity 0.3s ease;height: 80px;display: flex;align-items: center;padding: 0 20px;box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1)}.baum-radio-player-hidden{transform: translateY(100%);opacity: 0;pointer-events: none}.baum-radio-player-minimized{height: 50px}.baum-radio-player-minimized .baum-radio-station-details, .baum-radio-player-minimized .baum-radio-progress-container, .baum-radio-player-minimized .baum-radio-volume-container{display: none}.baum-radio-player-content{display: flex;align-items: center;width: 100%;gap: 20px}.baum-radio-station-info{display: flex;align-items: center;gap: 12px;min-width: 200px;flex-shrink: 0}.baum-radio-station-logo{width: 50px;height: 50px;border-radius: 6px;overflow: hidden;background: var(--color-gray-light);display: flex;align-items: center;justify-content: center}.baum-radio-station-logo img{width: 100%;height: 100%;object-fit: cover}.baum-radio-station-details{flex: 1}.baum-radio-station-name{font-weight: 600;font-size: 14px;color: var(--color-text);margin-bottom: 2px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis}.baum-radio-station-status{font-size: 12px;color: var(--color-text-muted);text-transform: uppercase;letter-spacing: 0.5px}.baum-radio-controls{display: flex;align-items: center;gap: 8px;flex-shrink: 0}.baum-radio-control-btn{background: none;border: none;color: var(--color-text);cursor: pointer;padding: 8px;border-radius: 50%;transition: all 0.2s ease;display: flex;align-items: center;justify-content: center;width: 36px;height: 36px}.baum-radio-control-btn:hover{background: var(--color-gray-light);color: var(--color-accent)}.baum-radio-play-btn-main{background: var(--color-accent);color: white;width: 44px;height: 44px}.baum-radio-play-btn-main:hover{background: var(--color-accent-dark);color: white}.baum-radio-speed-text{font-size: 11px;font-weight: 600}.baum-radio-speed-hidden{display: none}.baum-radio-progress-container{display: flex;align-items: center;gap: 12px;flex: 1;max-width: 400px}.baum-radio-progress-hidden{display: none}.baum-radio-time-display{display: flex;align-items: center;gap: 4px;font-size: 12px;color: var(--color-text-muted);white-space: nowrap;min-width: 80px}.baum-radio-progress-bar-container{flex: 1}.baum-radio-progress-bar{width: 100%;height: 4px;background: var(--color-gray-light);border-radius: 2px;outline: none;cursor: pointer;-webkit-appearance: none;appearance: none}.baum-radio-progress-bar::-webkit-slider-thumb{-webkit-appearance: none;appearance: none;width: 14px;height: 14px;border-radius: 50%;background: var(--color-accent);cursor: pointer;border: 2px solid white;box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2)}.baum-radio-progress-bar::-moz-range-thumb{width: 14px;height: 14px;border-radius: 50%;background: var(--color-accent);cursor: pointer;border: 2px solid white;box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2)}.baum-radio-volume-container{display: flex;align-items: center;gap: 8px;flex-shrink: 0}.baum-radio-volume-slider{width: 80px;height: 4px;background: var(--color-gray-light);border-radius: 2px;outline: none;cursor: pointer;-webkit-appearance: none;appearance: none}.baum-radio-volume-slider::-webkit-slider-thumb{-webkit-appearance: none;appearance: none;width: 12px;height: 12px;border-radius: 50%;background: var(--color-accent);cursor: pointer}.baum-radio-volume-slider::-moz-range-thumb{width: 12px;height: 12px;border-radius: 50%;background: var(--color-accent);cursor: pointer;border: none}.baum-radio-actions{display: flex;align-items: center;gap: 4px;flex-shrink: 0}.baum-radio-loading-indicator{position: absolute;top: 0;left: 0;right: 0;bottom: 0;background: rgba(0, 0, 0, 0.8);display: none;align-items: center;justify-content: center;gap: 12px;color: white;font-size: 14px}.baum-radio-loading-spinner{width: 20px;height: 20px;border: 2px solid rgba(255, 255, 255, 0.3);border-top: 2px solid white;border-radius: 50%;animation: baum-radio-spin 1s linear infinite}@keyframes baum-radio-spin{0%{transform: rotate(0deg)}100%{transform: rotate(360deg)}}.baum-radio-widget-container, .baum-radio-shortcode-container{margin-bottom: 20px}.baum-radio-shortcode-title{margin-bottom: 16px;color: var(--color-text)}.baum-radio-station-item{display: flex;align-items: center;gap: 12px;padding: 12px;border: 1px solid var(--color-gray-light);border-radius: 8px;margin-bottom: 8px;transition: all 0.2s ease;background: var(--color-background)}.baum-radio-station-item:hover{border-color: var(--color-accent);background: var(--color-gray-lightest)}.baum-radio-station-active{border-color: var(--color-accent);background: var(--color-accent-lightest)}.baum-radio-station-content{flex: 1}.baum-radio-station-title{font-size: 16px;font-weight: 600;color: var(--color-text);margin: 0 0 4px 0;display: flex;align-items: center;gap: 8px}.baum-radio-active-indicator{color: var(--color-accent);font-size: 14px}.baum-radio-station-description{font-size: 14px;color: var(--color-text-muted);margin: 0 0 6px 0;line-height: 1.4}.baum-radio-station-category{display: inline-block;background: var(--color-accent);color: white;font-size: 11px;font-weight: 600;padding: 2px 8px;border-radius: 12px;text-transform: uppercase;letter-spacing: 0.5px}.baum-radio-station-controls{flex-shrink: 0}.baum-radio-play-btn{background: var(--color-accent);color: white;border: none;padding: 8px 16px;border-radius: 6px;font-size: 14px;font-weight: 600;cursor: pointer;transition: all 0.2s ease;display: flex;align-items: center;gap: 6px}.baum-radio-play-btn:hover{background: var(--color-accent-dark);transform: translateY(-1px)}.baum-radio-widget-container[data-layout="grid"]{display: grid;grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));gap: 16px}.baum-radio-widget-container[data-layout="grid"] .baum-radio-station-item{flex-direction: column;text-align: center;margin-bottom: 0}.baum-radio-widget-container[data-layout="grid"] .baum-radio-station-logo{width: 80px;height: 80px;margin-bottom: 12px}.baum-radio-widget-container[data-layout="compact"] .baum-radio-station-item{padding: 8px;gap: 8px}.baum-radio-widget-container[data-layout="compact"] .baum-radio-station-title{font-size: 14px}.baum-radio-widget-container[data-layout="compact"] .baum-radio-station-description{display: none}.baum-radio-widget-container[data-layout="compact"] .baum-radio-play-btn{padding: 6px 12px;font-size: 12px}.baum-radio-no-stations{text-align: center;color: var(--color-text-muted);font-style: italic;padding: 20px}@media (max-width: 768px){#baum-radio-player-bar{padding: 0 12px;height: 70px}.baum-radio-player-content{gap: 12px}.baum-radio-station-info{min-width: 140px}.baum-radio-station-logo{width: 40px;height: 40px}.baum-radio-station-name{font-size: 13px}.baum-radio-progress-container{max-width: 200px}.baum-radio-volume-container{display: none}.baum-radio-widget-container[data-layout="grid"]{grid-template-columns: 1fr}.baum-radio-station-item{flex-direction: column;text-align: center;gap: 8px}.baum-radio-station-controls{width: 100%}.baum-radio-play-btn{width: 100%;justify-content: center}}@media (max-width: 480px){#baum-radio-player-bar{height: 60px;padding: 0 8px}.baum-radio-player-minimized{height: 40px}.baum-radio-controls{gap: 4px}.baum-radio-control-btn{width: 32px;height: 32px;padding: 6px}.baum-radio-play-btn-main{width: 38px;height: 38px}.baum-radio-progress-container{display: none}}