.baum-card pre, .baum-card blockquote, .baum-card dl, .baum-card figure, .baum-card table, .baum-card p, .baum-card ul, .baum-card ol, .baum-card form{margin-bottom: 10px}.baum-card .img, .baum-card img{border-radius: var(--border-radius)}.baum-sticky-side .baum-card-small{margin-top: 10px !important}.baum-card{filter: var(--drop-shadow);margin: 0 !important;border-radius: var(--border-radius);background: var(--card-bg)}.baum-card-category, .baum-card-category span a.baum-card-category{font-size: 11px;text-transform: uppercase;margin: 0 0 0 0;color: var(--color-post-category)}.baum-card-full .baum-card-category, .baum-card-full .baum-card-category span a.baum-card-category{margin: 0;display: none}.baum-card-category span img{max-width: 100px;max-height: 20px}.baum-card-large .baum-title{font-size: 16px;font-weight: 700}.baum-card-headline a.baum-title{font-weight: 700}.baum-title-width{margin: 5px}.baum-card .baum-card-headline a{text-decoration: none;color: var(--color-card-headline);text-transform: capitalize;font-weight: 700;line-height: 1.5;font-size: 14px;display: inline-block}.baum-card-bottom{height: 30px}.baum-card-info{padding: 0 10px;height: 30px;overflow: hidden;display: inline-block;width: 100%}.baum-card-info span{font-size: 12px;color: var(--card-info);line-height: 30px;font-weight: 700}.baum-card-more{float: right;margin: 0 10px;text-align: right}.baum-card-more-link{font-size: 30px;line-height: 10px;text-decoration: none;color: var(--card-more-link)}.baum-thumb-overlay{background: rgba(0, 0, 0, 0.5);position: absolute;width: 115px;height: 115px;border-radius: var(--border-radius)}.baum-thumb-overlay a{position: absolute;width: 115px;text-align: center;line-height: 115px;display: block;color: white;font-size: 23px}.baum-card-full .baum-thumb-overlay{width: 1100px;height: 514px}.baum-card-full .baum-thumb-overlay a{width: 100%;height: 100%;line-height: 514px;font-size: 48px}.baum-card-xlarge .baum-thumb-overlay{width: 513px;height: 380px;border-bottom-left-radius: 0;border-bottom-right-radius: 0}.baum-card-xlarge .baum-thumb-overlay a{width: 100%;height: 100%;line-height: 383px;font-size: 36px}.baum-card-large .baum-thumb-overlay{width: 360px;height: 240px;border-bottom-left-radius: 0;border-bottom-right-radius: 0}.baum-card-large .baum-thumb-overlay a{width: 100%;height: 100%;line-height: 228px;font-size: 32px}.baum-card-medium .baum-thumb-overlay{width: 268px;height: 185px;border-bottom-left-radius: 0;border-bottom-right-radius: 0}.baum-card-medium .baum-thumb-overlay a{width: 100%;height: 100%;line-height: 192px;font-size: 28px}.baum-card-wide .baum-thumb-overlay{width: 730px;height: 340px;border-bottom-left-radius: 0;border-bottom-right-radius: 0}.baum-card-wide .baum-thumb-overlay a{width: 100%;height: 100%;font-size: 36px}.baum-card-wide .baum-thumb-overlay a i{line-height: 340px}.baum-card-square .baum-thumb-overlay{width: 100%;width: 175px;height: auto;aspect-ratio: 1 / 1}.baum-card-square .baum-thumb-overlay a{width: 100%;height: 100%;line-height: 100%;line-height: 175px}.baum-card-book .baum-thumb-overlay{width: 100%;height: auto;aspect-ratio: 165 / 250}.baum-card-book .baum-thumb-overlay a{width: 100%;height: 100%;line-height: 100%;aspect-ratio: 165 / 250}.baum-card-1x2 .baum-thumb-overlay{width: 175px;height: auto;aspect-ratio: 175 / 175}.baum-card-1x2 .baum-thumb-overlay a{width: 100%;height: 100%;line-height: 100%;aspect-ratio: 165 / 250;font-size: 24px;line-height: 175px}.baum-card-1x2 .baum-thumb-overlay a i{line-height: 175px}.baum-cards-full{width: 100%;display: grid;grid-template-columns: 1fr}.baum-card-full{width: 1100px;height: 720px;border-radius: var(--border-radius);margin: 0 10px;aspect-ratio: 1040 / 525}.baum-post-content .baum-card-full, .entry-content .baum-card-full{max-width: 100%;width: 100%}.baum-card-full .baum-card-body{padding: 25px;height: 190px;border-radius: var(--border-radius);padding: 0px;margin: auto;margin-left: 0px;border-top-left-radius: 0;border-top-right-radius: 0}.baum-card-full .baum-card-headline{padding: 25px}.baum-card-full .baum-card-headline p{font-size: 14px;margin-bottom: 0;font-weight: 500}.baum-card-full .baum-card-body .baum-card-headline{z-index: 10}.baum-card-full .baum-card-img{border-radius: var(--border-radius);border-bottom-left-radius: 0;border-bottom-right-radius: 0}.baum-card-full .baum-card-full-img{height: 100%;overflow: hidden;border-radius: var(--border-radius);border-bottom-left-radius: 0;border-bottom-right-radius: 0}.baum-card-full .baum-card-full-img img{height: 100%;width: 100%;text-align: center;border-bottom-left-radius: 0;border-bottom-right-radius: 0}.baum-card-full .baum-card-headline a.baum-title{font-size: 36px;font-weight: 900;padding-bottom: 10px}a.baum-title.baum-larger-text{font-size: 20px;font-weight: 800}:not(.baum-card-full) .baum-card-body p, :not(.baum-card-wide) .baum-card-body p{font-size: 16px}.baum-card-full .baum-card-headline a.button, .baum-card-info a.button, .baum-card-headline a.button{font-size: 14px;display: block;color: var(--color-button-text);border: var(--color-button-border);background: var(--color-button-bg);border-radius: var(--border-radius);border: 0;font-weight: 700;text-transform: uppercase;padding: 10px;line-height: 14px;height: 34px;max-width: 250px}.baum-card-full .baum-card-category span a.baum-card-category{margin: 0;color: var(--color-accent)}.baum-cards-wide{width: 100%;display: grid;grid-template-columns: 66.66% 33.33%}.baum-card-wide{width: 100%;height: auto;aspect-ratio: 730 / 515;border-radius: var(--border-radius);margin: 0 10px}.baum-card-wide .baum-card-body{padding: 25px;width: 730px;height: 515px;border-radius: var(--border-radius);padding: 0px}.baum-card-wide .baum-card-headline{}.baum-card-wide .baum-card-headline p{font-size: 14px;margin: 0}.baum-card-wide .baum-card-body .baum-card-headline{z-index: 10}.baum-card-wide .baum-card-img{border-radius: var(--border-radius)}.baum-card-wide .baum-card-wide-img{height: 340px;overflow: hidden;border-radius: var(--border-radius);border-bottom-right-radius: 0;border-bottom-left-radius: 0}.baum-card-wide .baum-card-wide-img img{height:auto;width: 100%;width: 728px;text-align: center;border-bottom-right-radius: 0;border-bottom-left-radius: 0}.baum-card-wide .baum-card-headline a{font-size: 26px;font-weight: 700;padding-bottom: 0px;color: var(--color-black);display: -webkit-box}.baum-card-wide .baum-card-headline a.baum-title{font-weight: 1000}.baum-card-horizontal{width: 100%;height: 170px;border-radius: var(--border-radius);margin: 10px 0;overflow: hidden}.baum-card-horizontal .baum-card-body{display: flex;height: 100%;padding: 0}.baum-card-horizontal .baum-card-horizontal-img{width: 50%;height: 100%;overflow: hidden;position: relative}.baum-card-horizontal .baum-card-horizontal-img img{width: 100%;height: 100%;object-fit: cover;border-radius: 0;border-top-left-radius: var(--border-radius);border-bottom-left-radius: var(--border-radius)}.baum-card-horizontal .baum-card-horizontal-content{width: 50%;padding: 15px;display: flex;flex-direction: column;justify-content: space-between}.baum-card-horizontal .baum-card-headline{flex-grow: 1}.baum-card-horizontal .baum-card-headline a.baum-title{font-size: 18px;line-height: 1.3;margin-bottom: 8px;display: block}.baum-card-horizontal .baum-card-headline p{font-size: 12px;line-height: 1.4;margin: 0;color: var(--color-text-secondary)}.baum-card-horizontal .baum-card-info{padding: 0;height: auto;font-size: 11px;margin-top: 8px}.baum-card-horizontal .baum-card-info span{font-size: 11px;line-height: 1.2}.baum-card-horizontal .baum-thumb-overlay{width: 100%;height: 100%;border-radius: 0;border-top-left-radius: var(--border-radius);border-bottom-left-radius: var(--border-radius)}.baum-card-horizontal .baum-thumb-overlay a{width: 100%;height: 100%;line-height: 120px;font-size: 20px}@media (max-width: 768px){.baum-card-horizontal{max-width: 100%;height: 100px}.baum-card-horizontal .baum-card-horizontal-content{padding: 10px}.baum-card-horizontal .baum-card-headline a.baum-title{font-size: 13px}.baum-card-horizontal .baum-card-headline p{font-size: 11px}.baum-card-horizontal .baum-thumb-overlay a{line-height: 100px;font-size: 18px}}.baum-cards.baum-cards-square-gradient{width: 100% !important;max-width: 1200px;display: grid;grid-template-columns: 1fr 1fr 1fr;margin: 10px auto;overflow: hidden}.baum-grid-1-1-1{width: 100%;display: grid;grid-template-columns: 1fr 1fr 1fr;margin: 10px 0;position: relative;z-index: 1}.baum-grid-1-1{display: grid;grid-template-columns: 1fr 1fr;grid-template-rows: 1fr 1fr;gap: 5px;position: relative}.baum-card-square-gradient{width: 100%;max-width: 360px;height: 360px;aspect-ratio: 1;border-radius: var(--border-radius);margin: 0;overflow: hidden;position: relative;isolation: isolate}.baum-card-square-gradient .baum-card-body{height: 100%;padding: 0}.baum-card-square-gradient .baum-card-square-img{width: 100%;height: 100%;position: relative;overflow: hidden}.baum-card-square-gradient .baum-card-square-img img{width: 100%;height: 100%;object-fit: cover;border-radius: var(--border-radius)}.baum-card-square-gradient .baum-card-gradient-overlay{position: absolute;bottom: 0;left: 0;right: 0;padding: 20px 15px 15px;color: white;border-bottom-left-radius: var(--border-radius);border-bottom-right-radius: var(--border-radius)}.baum-card-square-gradient .baum-card-square-content{position: relative;z-index: 2}.baum-card-square-gradient .baum-card-square-content a.baum-title{color: white;font-size: 16px;font-weight: 700;line-height: 1.3;margin-bottom: 8px;display: block;text-decoration: none}.baum-card-square-gradient .baum-card-square-content a.baum-title:hover{color: var(--color-accent, #ffffff)}.baum-card-square-gradient .baum-card-info{font-size: 12px;color: rgba(255, 255, 255, 0.9);margin-top: 5px}.baum-card-square-gradient .baum-card-info span{font-size: 12px}.baum-card-square-gradient .baum-card-category a{color: rgba(255, 255, 255, 0.8);font-size: 11px;text-transform: uppercase;letter-spacing: 0.5px;margin-bottom: 5px;display: block}.baum-card-square-gradient .baum-thumb-overlay{width: 100%;height: 100%;border-radius: var(--border-radius)}.baum-card-square-gradient .baum-thumb-overlay a{width: 100%;height: 100%;line-height: 200px;font-size: 24px;color: white}.baum-card-full-gradient{width: 100%;height: 300px;border-radius: var(--border-radius);margin: 10px 0;overflow: hidden;position: relative}.baum-card-full-gradient .baum-card-body{height: 100%;padding: 0}.baum-card-full-gradient .baum-card-full-img{width: 100%;height: 100%;position: relative;overflow: hidden}.baum-card-full-gradient .baum-card-full-img img{width: 100%;height: 100%;object-fit: cover;border-radius: var(--border-radius)}.baum-card-full-gradient .baum-card-gradient-overlay{position: absolute;bottom: 0;left: 0;right: 0;background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));padding: 40px 25px 25px;color: white;border-bottom-left-radius: var(--border-radius);border-bottom-right-radius: var(--border-radius)}.baum-card-full-gradient .baum-card-full-content{position: relative;z-index: 2}.baum-card-full-gradient .baum-card-full-content a.baum-title{color: white;font-size: 24px;font-weight: 700;line-height: 1.3;margin-bottom: 12px;display: block;text-decoration: none}.baum-card-full-gradient .baum-card-full-content a.baum-title:hover{color: var(--color-accent, #ffffff)}.baum-card-full-gradient .baum-card-info{display: none;font-size: 14px;color: rgba(255, 255, 255, 0.9);margin-top: 10px}.baum-card-full-gradient .baum-card-info span{font-size: 14px}.baum-card-full-gradient .baum-card-category a{color: rgba(255, 255, 255, 0.8);font-size: 13px;text-transform: uppercase;letter-spacing: 0.5px;margin-bottom: 8px;display: block}.baum-card-full-gradient .baum-thumb-overlay{width: 100%;height: 100%;border-radius: var(--border-radius)}.baum-card-full-gradient .baum-thumb-overlay a{width: 100%;height: 100%;line-height: 300px;font-size: 36px;color: white}@media (max-width: 768px){.baum-cards.baum-cards-square-gradient{grid-template-columns: 1fr 1fr;gap: 5px}.baum-grid-1-1-1{grid-template-columns: 1fr;gap: 10px}.baum-grid-1-1{grid-template-columns: 1fr 1fr;gap: 5px}.baum-card-square-gradient{width: 100%;max-width: none;height: auto;aspect-ratio: 1}.baum-card-square-gradient .baum-card-square-content a.baum-title{font-size: 14px}.baum-card-square-gradient .baum-card-gradient-overlay{padding: 15px 12px 12px}.baum-card-full-gradient{height: 250px}.baum-card-full-gradient .baum-card-full-content a.baum-title{font-size: 20px}.baum-card-full-gradient .baum-card-gradient-overlay{padding: 30px 20px 20px}.baum-card-full-gradient .baum-thumb-overlay a{line-height: 250px;font-size: 30px}}@media (max-width: 480px){.baum-cards.baum-cards-square-gradient{grid-template-columns: 1fr;gap: 10px}.baum-grid-1-1-1{grid-template-columns: 1fr;gap: 10px}.baum-grid-1-1{grid-template-columns: 1fr;gap: 10px}.baum-card-square-gradient{width: 100%;max-width: 360px;margin: 0 auto}}.baum-review-container{margin-bottom: 30px}.baum-review-header{margin: 0px -10px !important}.baum-review-header .baum-heading{color: var(--color-accent);font-size: 24px;font-weight: 700;margin: 0;text-transform: capitalize}.baum-review-card{padding: 20px;margin: -10px !important;margin-top: 10px !important;margin-bottom: 10px !important;background: var(--card-bg);border: var(--card-outline);border-radius: var(--border-radius);box-shadow: 0 4px 12px rgba(0,0,0,0.1);transition: all 0.3s ease}.baum-review-card:hover{transform: translateY(-2px);box-shadow: 0 6px 20px rgba(0,0,0,0.15)}.baum-review-rating-box{text-align: center;background: var(--color-secondary);padding: 15px;border-radius: var(--border-radius)}.baum-review-rating-box .rating-label{color: var(--color-octonary);text-transform: uppercase;font-size: 12px;font-weight: 600;display: block;margin-bottom: 8px}.baum-review-rating-box .rating-score{text-align: center;color: var(--color-denary);font-size: 48px;font-weight: 700;margin: 0;line-height: 1}.baum-review-rating-box .rating-scale{color: var(--color-nonary);opacity: 0.5;font-size: 12px;font-weight: 400}.baum-review-rating-box .rating-stars{text-align: center;color: var(--color-nonary);margin-top: 12px;font-size: 16px}.baum-review-rating-box .rating-stars i{margin: 0 2px}.baum-review-card .review-title{font-size: 28px;font-weight: 900;margin: 20px 0px 20px 0;line-height: 1.2}.baum-review-card .review-summary{margin-bottom: 25px;font-size: 12px;line-height: 1.6;color: var(--color-secondary)}.baum-review-card .review-summary p{margin: 0}.baum-review-card .review-categories{margin-top: 25px}.baum-review-card .review-category-item{border-top: 1px dashed var(--color-septenary);padding: 12px 0}.baum-review-card .review-category-item:first-child{border-top: 1px dashed var(--color-septenary)}.baum-review-card .category-label{font-size: 14px;font-weight: 700;color: var(--color-secondary);display: inline-block}.baum-review-card .category-rating{font-size: 16px;float: right}.baum-review-card .category-rating i{margin: 0 1px}.baum-review-footer{margin: -10px;margin-top: -10px;margin-bottom: 10px}.baum-review-footer .baum-card-info{display: flex;justify-content: space-between;align-items: center}.baum-review-footer .review-attribution{color: var(--color-quinary);font-weight: bold;text-transform: uppercase;font-size: 12px}.baum-review-footer .review-info-link{color: var(--color-senary);text-decoration: none;font-size: 16px;float: right;transition: color 0.3s ease}.baum-review-footer .review-info-link:hover{color: var(--color-accent)}.baum-review-card .review-title{clear: both}.baum-review-card .orange, .baum-review-rating-box .orange{color: var(--color-orange)}.baum-review-card .senary{color: var(--color-senary)}@media (max-width: 768px){.baum-review-rating-box{float: none;margin-left: 0 !important;margin-bottom: 20px;padding: 15px}.baum-review-rating-box .rating-score{font-size: 36px}.baum-review-card .review-title{font-size: 24px;clear: none}.baum-review-card .review-summary{font-size: 14px}.baum-review-header{margin: 0 !important}.baum-review-card{margin: 0 !important}.baum-review-footer{margin: 0}}.baum-card-wide .baum-card-bottom{display: block}.baum-card-info{padding: 0 10px;height: 30px;overflow: hidden;display: inline-block;width: 100%;z-index: 99}.baum-card-wide .baum-card-headline{padding: 20px 20px;font-weight: 500;height: 175px;display: inline-block;color: var(--color-black);border-radius: var(--border-radius);margin: 0;margin-top: 0;border-top-right-radius: 0;border-top-left-radius: 0}.baum-card-wide .baum-card-headline a.baum-title{font-weight: 1000;font-size: 28px;line-height: 36px;margin-bottom: 10px}.baum-card-wide .baum-card-headline a.button{font-size: 12px;display: block;background: black;line-height: 28px;font-weight: 900;text-transform: uppercase;color: var(--color-denary);padding: 2.5px;max-width: 200px;margin-top: 20px;border-radius: var(--border-radius-small);background: var(--color-secondary) !important;height: 32px}.baum-card-wide .baum-card-container{margin-bottom: 10px}.baum-card-wide-fix .baum-card-container:not(:last-child){margin-bottom: 10px}.baum-card-wide-1-4-fix{display: grid;grid-template-columns: 1fr 1fr}.baum-cards-1x2{width: 100%;display: grid;grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr}.baum-card-1x2{width: 100%;border-radius: var(--border-radius);aspect-ratio: 1 / 1}.baum-card-1x2 .baum-card-body{padding: 5px;width: 175px;height: 330px;border-radius: var(--border-radius);padding: 0px}.baum-card-1x2 .baum-card-headline{padding: 5px 10px;height: 150px}.baum-card-1x2 .baum-card-body .baum-card-headline{z-index: 10}.baum-card-1x2 .baum-card-img{border-radius: var(--border-radius-small)}.baum-card-1x2 .baum-card-1x2-img{width: 100%;overflow: hidden;border-radius: var(--border-radius);border-bottom-left-radius: 0;border-bottom-right-radius: 0}.baum-card-1x2 .baum-card-1x2-img img{height:100%;width: 100%;text-align: center;border-bottom-left-radius: 0;border-bottom-right-radius: 0}.baum-card-1x2 .baum-card-headline a{font-size: 14px;font-weight: 700;word-wrap: break-word;margin: 10px 0}.baum-card-1x2 .baum-card-container{margin-bottom: 10px}.baum-card-1x2-fix .baum-card-container:first-child{margin-bottom: 10px}.baum-thumb-gradient::before{content: '';z-index: 10;width: 175px;height: 175px;display: block;position: absolute;background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%,rgba(0, 0, 0, 0.01) 50%, rgba(0, 0, 0, 10%) 0%);border-radius: var(--border-radius);margin-bottom: -2px}.baum-cards-square{width: 100%;display: grid;grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr}.baum-card-square{width: 100%;border-radius: var(--border-radius);aspect-ratio: 1}.baum-card-square .baum-card-body{padding: 5px;width: 175px;height: 175px;position: absolute;border-radius: var(--border-radius);padding: 0px}.baum-card-square .baum-card-headline{padding: 0 5px;position: relative;top: 125px}.baum-card-square .baum-card-headline{padding: 0 10px;position: relative;top: 120px;z-index: 50}.baum-card-square .baum-card-body .baum-card-headline{z-index: 10}.baum-card-square .baum-card-img{border-radius: var(--border-radius)}.baum-card-square .baum-card-square-img{height: 100%;overflow: hidden;border-radius: var(--border-radius)}.baum-card-square .baum-card-square-img img{height:100%;width: 100%;text-align: center}.baum-card-square .baum-card-square-gradient-overlay{position: absolute;bottom: 0;left: 0;right: 0;height: 50%;border-bottom-left-radius: var(--border-radius);border-bottom-right-radius: var(--border-radius);pointer-events: none}.baum-card-headline a.baum-title:visited{text-shadow: -1px -1px 10px var(--color-accent)}.baum-card-square .baum-card-headline a{display: -webkit-box;padding: 0px;font-size: 14px;line-height: 16px;font-weight: 800;position: absolute;color: white;width: 173px;margin: 0;z-index: 5;padding-right: 15px}.baum-card-square .baum-card-container{margin-bottom: 10px}.baum-card-square .baum-card-category span a{display: none}.baum-card-square-fix .baum-card-container:first-child{margin-bottom: 10px}.baum-cards-book{width: 100%;display: grid;grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr}.baum-post-content .baum-cards-book, .entry-content .baum-cards-book{grid-template-columns: 1fr 1fr 1fr 1fr}.baum-card-book{width: auto;height: auto;border-radius: var(--border-radius);margin: 0 10px;overflow: hidden;aspect-ratio: 165 / 250}div.baum-card-book{border-bottom-left-radius: var(--border-radius);border-bottom-right-radius: var(--border-radius);border-bottom: var(--card-outline)}.baum-card-book .baum-card-body{padding: 5px;width: 174px;aspect-ratio: 165 / 250;position: absolute;background: linear-gradient(transparent, transparent, #000);border-radius: var(--border-radius);padding: 0px}.baum-card-book .baum-card-headline{padding: 0 5px;position: absolute;bottom: 0}.baum-card-book .baum-card-body .baum-card-headline{z-index: 10}.baum-card-book .baum-card-img{border-radius: var(--border-radius)}.baum-card-book .baum-card-book-img{height: 100%;overflow: hidden;border-radius: var(--border-radius)}.baum-card-book .baum-card-book-img img{width: 173px;height: 100%;text-align: center}.baum-card-book .baum-card-headline a{padding: 10px;font-size: 16px;font-weight: 800;display: inline-block;color: white;height: 74px;overflow: hidden}.baum-card-book .baum-card-container{margin-bottom: 10px}.baum-card-book .baum-card-category span a{display: none}.baum-card-book-fix .baum-card-container:first-child{margin-bottom: 10px}.baum-cards-xlarge{width: 100%;display: grid;grid-template-columns: 1fr 1fr}.baum-card-xlarge{width: 100%;height: 475px;aspect-ratio: 540 / 475;border-radius: var(--border-radius);margin: 0 10px}.baum-card-xlarge .baum-card-body{padding: 0px;height: 414px}.baum-card-xlarge .baum-card-headline{padding: 10px;height: 95px;padding: 5px 10px}.baum-card-xlarge .baum-card-img{width: 100%;overflow: hidden;border-radius: var(--border-radius);border-bottom-right-radius: 0px;border-bottom-left-radius: 0px}.baum-card-medium .baum-card-img{width: 267px;height: 183px;text-align: center;border-bottom-left-radius: 0 !important;border-bottom-right-radius: 0 !important;overflow: hidden}.baum-card-xlarge .baum-card-img img{height: auto;width: 100%;text-align: center;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px}.baum-card-xlarge .baum-card-headline a{font-size: 22px;font-weight: 700}.baum-cards-large{width: 100%;display: grid;grid-template-columns: 1fr 1fr 1fr}.baum-post-content .baum-cards-large, .entry-content .baum-cards-large{grid-template-columns: 1fr 1fr}.baum-card-large .baum-card-img{width: 100%;overflow: hidden;border-radius: var(--border-radius);border-bottom-right-radius: 0px;border-bottom-left-radius: 0px}.baum-card-large .baum-card-img img{height: auto;width: 100%;text-align: center;border-bottom-right-radius: 0;border-bottom-left-radius: 0}.baum-card-large{width: 100%;height: 375px;aspect-ratio: 357 / 375;border-radius: var(--border-radius);margin: 0 10px}.baum-card-large .baum-card-body{padding: 0px;height: 310px}.baum-card-large .baum-card-headline{padding: 10px;height: 130px;padding: 5px 10px}.baum-card-medium .baum-card-img{overflow: hidden;border-radius: var(--border-radius);border-bottom-right-radius: 0px;border-bottom-left-radius: 0px}.baum-card-medium .baum-card-img img{height: auto;width: 100%;text-align: center;border-bottom-left-radius: 0;border-bottom-right-radius: 0}.baum-cards-medium{width: 100%;display: grid;grid-template-columns: 1fr 1fr 1fr 1fr}.baum-post-content .baum-cards-medium, .entry-content .baum-cards-medium{grid-template-columns: 1fr 1fr 1fr}.baum-card-medium{max-width: 300px;width: 100%;height: 325px;aspect-ratio: 265 / 325;border-radius: var(--border-radius)}.baum-card-medium .baum-card-body{padding: 0px;height: 260px}.baum-card-medium .baum-card-headline{padding: 10px;padding: 5px 10px}.baum-cards-small{width: 100%;display: grid;grid-template-columns: 1fr 1fr 1fr}.baum-post-content .baum-cards-small, .entry-content .baum-cards-small{grid-template-columns: 1fr 1fr}.baum-cards-small .baum-cards-container{aspect-ratio: 360 / 175}.baum-card-small{min-width: 360px;max-width: 360px;width: 100%;border-radius: var(--border-radius);margin: 0 10px;height: 145px}.baum-cards-small-cover{width: 100%;display: grid;grid-template-columns: repeat(3, 1fr)}.baum-post-content .baum-cards-small-cover, .entry-content .baum-cards-small-cover{grid-template-columns: repeat(2, 1fr)}.baum-card-small-cover{width: 360px;height: 175px;aspect-ratio: 360 / 175;border-radius: var(--border-radius);margin: 0 5px;overflow: hidden;position: relative}.baum-card-small-cover-container{width: 100%;height: 100%;position: relative}.baum-card-small-cover-img{width: 100%;height: 100%;position: relative;overflow: hidden;border-radius: var(--border-radius)}.baum-card-small-cover-img img{width: 100%;height: 100%;object-fit: cover;border-radius: var(--border-radius)}.baum-card-small-cover-gradient{position: absolute;bottom: 0;left: 0;right: 0;height: 60%;display: flex;align-items: flex-end;padding: 15px;border-radius: var(--border-radius);border-top-left-radius: 0;border-top-right-radius: 0}.baum-card-small-cover-content{width: 100%}.baum-card-small-cover-content a.baum-title{color: #ffffff;font-size: 16px;font-weight: 700;line-height: 1.3;text-decoration: none;text-shadow: 1px 1px 3px rgba(0,0,0,0.8);display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;text-overflow: ellipsis}.baum-card-small-cover-content a.baum-title:hover{color: #ffffff;text-decoration: underline}.baum-card-small-cover .baum-thumb-overlay{position: absolute;top: 0;left: 0;right: 0;bottom: 0;display: flex;align-items: center;justify-content: center;background: rgba(0,0,0,0.3);z-index: 5;border-radius: var(--border-radius)}.baum-card-small-cover .baum-thumb-overlay a{color: #ffffff;font-size: 24px;text-decoration: none;background: rgba(0,0,0,0.6);width: 50px;height: 50px;border-radius: 50%;display: flex;align-items: center;justify-content: center;transition: all 0.3s ease}.baum-card-small-cover .baum-thumb-overlay a:hover{background: rgba(0,0,0,0.8);transform: scale(1.1)}.baum-card-small .baum-card-body{padding: 10px;display: grid;display: inline-grid;grid-template-columns: 1fr 125px;height: 145px;overflow: hidden}.baum-card-small .baum-card-body:not(:has(.baum-card-img)){grid-template-columns: 1fr }.baum-card-small .baum-card-headline{margin-right: 10px}.baum-card-small .baum-card-headline a{font-size: 14px}.baum-card-small .baum-card-img{width: 125px;height: 125px;overflow: hidden;border-radius: var(--border-radius)}.baum-card-small .baum-card-img img{height: 100%;width: auto;text-align: center}.baum-card-container{margin: 5px}.post-img{max-width: 100%}.no-mobile{display: grid}.mobile-only{display: none}.baum-card{filter: var(--drop-shadow);margin: 0 !important;border-radius: var(--border-radius);background: var(--card-bg);border: var(--card-outline);border-bottom-left-radius: 0;border-bottom-right-radius: 0;border-bottom: 0;border-left: var(--card-outline);border-right: var(--card-outline)}.baum-card-bottom{border: var(--card-outline);background: var(--card-bg);border-radius: var(--border-radius);border-top-left-radius: 0;border-top-right-radius: 0}.baum-card{filter: none}.baum-comment-body .baum-comment-content{border-top: 0px;border-bottom: var(--card-outline)}.baum-card-body .post-excerpt, .baum-card-small .post-excerpt, .baum-card-mini .post-excerpt{font-size: 14px;line-height: 1.5;color: var(--color-quinary)}.baum-card-small .baum-card-body{display: inline-grid;grid-template-columns: 1fr 125px}.baum-card-mini .post-excerpt{display: -webkit-box;-webkit-box-orient: vertical;overflow: hidden;text-overflow: ellipsis;word-break: break-word !important;-webkit-line-clamp: 2}.baum-card-mini{list-style-type: none;border-radius: var(--border-radius);padding: 10px;border: var(--card-outline);min-height: 84px;background: var(--card-bg);filter: var(--drop-shadow);line-height: 1.5;margin: 5px 0}.baum-card-mini{min-height: 84px;max-width: 360px;aspect-ratio: 360 / 84}.baum-card-mini a{text-decoration: none;font-size: 14px;word-wrap: break-word;color: var(--color-card-headline);text-transform: capitalize;font-weight: 700}.baum-card-mini .baum-card-img{position: relative}.baum-card-mini img{float: right;margin-left: 10px;overflow: hidden;border-radius: var(--border-radius-small)}.baum-cards-mini{display: grid;grid-template-columns: 1fr 1fr 1fr}.two-thirds .baum-cards-mini{grid-template-columns: 1fr 1fr}.single-footer .baum-cards-mini, .single-footer .baum-story-collection, .single-footer .baum_collection_widget .baum-story-collection{grid-template-columns: 1fr 1fr;margin: -5px;margin-top: 5px;margin-bottom: 5px}.baum-widget-home .baum-cards-mini, .baum-widget-home .baum-story-collection, .baum-widget-home .baum_collection_widget .baum-story-collection{margin: 0px;margin-top: 0px;grid-template-columns: 1fr 1fr 1fr}.baum-post-content .baum-cards-mini, .entry-content .baum-cards-mini{grid-template-columns: 1fr 1fr}.entry-content .baum-cards-mini{grid-template-columns: 1fr 1fr}.baum-card-medium .baum-card-headline a{-webkit-line-clamp: 3}.baum-card-large .baum-card-headline a{-webkit-line-clamp: 3}.baum-cards{width: 1110px}:not(.baum-card-full) .baum-card-body p, :not(.baum-card-wide) .baum-card-body p{margin-top: 5px;font-size: 14px;line-height: 1.75}.baum-cards-1x2{width: 100%;display: grid;grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr}.baum-post-content .baum-cards-1x2, .entry-content .baum-cards-1x2{grid-template-columns: 1fr 1fr 1fr 1fr}.baum-card-1x2{width: 100%;aspect-ratio: 1 / 1}.baum-card-1x2 .baum-card-body{padding: 5px;width: 173px;height: 330px;border-radius: var(--border-radius);padding: 0px}.baum-card-1x2 .baum-card-headline{padding: 5px 10px;height: 150px}.baum-card-1x2 .baum-card-body .baum-card-headline{z-index: 10}.baum-card-1x2 .baum-card-img{border-radius: var(--border-radius)}.baum-card-1x2 .baum-card-1x2-img{width: 100%;overflow: hidden;border-radius: var(--border-radius);border-bottom-left-radius: 0;border-bottom-right-radius: 0}.baum-card-1x2 .baum-card-1x2-img img{height: 175px;width: auto;text-align: center;border-bottom-left-radius: 0;border-bottom-right-radius: 0}.baum-card-1x2 .baum-card-headline a{font-size: 14px;line-height: 18px;font-weight: 700;word-wrap: break-word;margin: 10px 0;margin: 0}.baum-card-1x2 .baum-card-container{margin-bottom: 10px}.baum-card-1x2-fix .baum-card-container:first-child{margin-bottom: 10px}.baum-card-headline a{text-decoration: none;color: var(--color-card-headline);text-transform: capitalize;font-weight: 700}.baum-card-category span img{max-width: 100px;max-height: 20px}.baum-card-headline a.baum-title{font-weight: 800}.baum-card-bottom{height: 30px}.baum-card-bottom .baum-card-info small{font-size: 9px}.baum-card-info{padding: 0 10px;height: 30px;overflow: hidden;display: inline-block;width: 100%}.baum-card-info span{font-size: 12px;color: var(--card-info);line-height: 30px;font-weight: 700}.baum-rotating-card{display: flex;background: var(--color-white);border-radius: var(--border-radius);overflow: hidden;box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);transition: box-shadow 0.3s ease;min-height: 400px;max-height: 500px;margin: 5px}.baum-rotating-card:hover{box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15)}.baum-rotating-card-image{flex: 2;position: relative;overflow: hidden}.baum-rotating-card-image-container{position: relative;width: 100%;height: 100%;overflow: hidden}.baum-rotating-card-featured-img{width: 100%;height: 100%;object-fit: cover;object-position: center;display: block;min-height: 100%;aspect-ratio: 16 / 9}.baum-rotating-card-overlay{position: absolute;bottom: 0;left: 0;right: 0;background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));padding: 40px 30px 30px;color: var(--color-white)}.baum-rotating-card-overlay-content{max-width: 100%}.baum-rotating-card-category{display: inline-block;background: var(--color-accent);color: var(--color-white);padding: 4px 12px;border-radius: var(--border-radius-small);font-size: 12px;font-weight: 600;text-transform: uppercase;letter-spacing: 0.5px;margin-bottom: 10px}.baum-rotating-card-title{margin: 0 0 10px 0;font-size: 24px;line-height: 1.3;font-weight: 700}.baum-rotating-card-title a{color: var(--color-white);text-decoration: none;transition: color 0.3s ease}.baum-rotating-card-title a:hover{color: var(--color-accent)}.baum-rotating-card-excerpt{margin: 0 0 15px 0;font-size: 14px;line-height: 1.5;opacity: 0.9;display: -webkit-box;-webkit-line-clamp: 2;line-clamp: 2;-webkit-box-orient: vertical;overflow: hidden}.baum-rotating-card-meta{display: flex;gap: 15px;font-size: 12px;opacity: 0.8}.baum-rotating-card-author, .baum-rotating-card-date{position: relative}.baum-rotating-card-author::after{content: "•";position: absolute;right: -10px;opacity: 0.6}.baum-rotating-card-list{flex: 1;background: var(--color-white);display: flex;flex-direction: column;border-left: 1px solid var(--color-octonary)}.baum-rotating-card-list-header{padding: 20px 20px 15px;border-bottom: 1px solid var(--color-septenary)}.baum-rotating-card-list-header h3{margin: 0;font-size: 16px;font-weight: 700;color: var(--color-body-text);text-transform: uppercase;letter-spacing: 0.5px}.baum-rotating-card-list-items{flex: 1;overflow-y: auto}.baum-rotating-card-list-item{position: relative;padding: 15px 20px;border-bottom: 1px solid var(--color-septenary);cursor: pointer;display: flex;align-items: center;gap: 15px}.baum-rotating-card-list-item.active{background: transparent}.baum-rotating-card-list-item.active .baum-rotating-card-list-title a{color: var(--color-accent)}.baum-rotating-card-list-item-content{flex: 1}.baum-rotating-card-list-title{margin: 0 0 5px 0;font-size: 14px;line-height: 1.4;font-weight: 700}.baum-rotating-card-list-title a{color: var(--color-body-text);text-decoration: none;transition: color 0.3s ease;display: -webkit-box;-webkit-line-clamp: 2;line-clamp: 2;-webkit-box-orient: vertical;overflow: hidden;font-weight: inherit}.baum-rotating-card-list-title a:hover{color: var(--color-accent)}.baum-rotating-card-list-meta{display: none}.baum-rotating-card-list-indicator{width: 4px;height: 30px;background: transparent;border-radius: 2px;transition: background 0.3s ease}.baum-rotating-card-list-item.active .baum-rotating-card-list-indicator{background: var(--color-accent)}.baum-rotating-card-loading{opacity: 0.6;pointer-events: none}.baum-rotating-card-featured-img.loading{opacity: 0.5}@media (max-width: 768px){.baum-rotating-card{flex-direction: column;max-height: none}.baum-rotating-card-image{flex: none;height: 250px}.baum-rotating-card-list{flex: none}.baum-rotating-card-overlay{padding: 20px 20px 20px}.baum-rotating-card-title{font-size: 20px}}@media (max-width: 480px){.baum-rotating-card-overlay{padding: 15px}.baum-rotating-card-title{font-size: 18px}.baum-rotating-card-list-header, .baum-rotating-card-list-item{padding: 12px 15px}}.baum-rotating-card-empty{display: flex;align-items: center;justify-content: center;min-height: 200px;background: var(--color-denary);border-radius: var(--border-radius);color: var(--color-senary);font-style: italic}@media print{.baum-rotating-card{box-shadow: none;border: 1px solid #ccc}.baum-rotating-card-overlay{background: rgba(0, 0, 0, 0.1);color: #000}}