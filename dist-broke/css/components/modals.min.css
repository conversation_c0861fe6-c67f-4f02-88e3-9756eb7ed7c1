.baum-modal{display: none;position: fixed;top: 0;left: 0;width: 100%;height: 100%;background: rgba(0,0,0,0.5);z-index: 9999;backdrop-filter: blur(4px)}.baum-modal-content{position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);background: white;border-radius: var(--border-radius);box-shadow: 0 20px 40px rgba(0,0,0,0.15);overflow: hidden;max-height: 90vh;width: 90%;max-width: 800px;min-height: 500px}.baum-search-modal .baum-modal-content{top: 10vh;width: 90%;max-width: 800px;background: var(--color-background);border-radius: var(--border-radius);box-shadow: 0 20px 60px rgba(0,0,0,0.3);transform: translateX(-50%);max-height: 80vh;overflow-y: auto}.baum-search-header{padding: 20px 20px 0 20px;position: relative}.baum-search-input-wrapper{position: relative;display: flex;align-items: center}.baum-search-icon{position: absolute;left: 16px;top: 50%;transform: translateY(-50%);color: var(--color-gray);font-size: 18px}.baum-search-input{width: 100%;padding: 16px 50px 16px 50px;border: 2px solid #f0f0f0;border-radius: var(--border-radius);font-size: 16px;background: white;transition: all 0.3s ease}.baum-search-input:focus{outline: none;border-color: var(--color-accent);box-shadow: 0 0 0 3px rgba(var(--color-accent-rgb), 0.1)}.baum-search-close{position: absolute;right: 16px;top: 50%;transform: translateY(-50%);background: none;border: none;color: var(--color-gray);font-size: 20px;cursor: pointer;width: 24px;height: 24px;display: flex;align-items: center;justify-content: center;border-radius: 50%;transition: all 0.2s ease}.baum-search-close:hover{background: #f0f0f0;color: var(--color-body-text)}.baum-search-results{max-height: 400px;overflow-y: auto;padding: 20px}.baum-search-section-title{font-size: 12px;font-weight: 600;color: var(--color-gray);text-transform: uppercase;letter-spacing: 0.5px;margin-bottom: 8px}.baum-suggestion-item{display: flex;align-items: center;padding: 12px 16px;border-radius: var(--border-radius);cursor: pointer;transition: all 0.2s ease;margin-bottom: 4px}.baum-suggestion-item:hover{background: #f8f9fa}.baum-suggestion-icon{width: 20px;margin-right: 12px;color: var(--color-secondary)}.baum-suggestion-text{color: var(--color-body-text);font-size: 14px}.baum-posts-modal .baum-modal-content{width: 90%;max-width: 800px;max-height: 90vh;display: block;flex-direction: column}.baum-posts-header{padding: 20px 30px;border-bottom: 1px solid #e0e0e0;display: flex;align-items: center;justify-content: space-between;background: #f8f9fa}.baum-posts-title{margin: 0;color: var(--color-body-text);font-size: 24px;font-weight: 700}.baum-posts-subtitle{margin: 4px 0 0 0;color: var(--color-gray);font-size: 14px}.baum-posts-close{background: none;border: none;font-size: 24px;color: var(--color-gray);cursor: pointer;width: 32px;height: 32px;display: flex;align-items: center;justify-content: center;border-radius: 50%;transition: all 0.2s ease}.baum-posts-close:hover{background: #f0f0f0;color: var(--color-body-text)}.baum-new-post-modal .baum-modal-content{width: 90%;max-width: 800px;padding: 0}.baum-new-post-header{padding: 30px 40px 20px 40px;border-bottom: 1px solid #f0f0f0}.baum-new-post-title{margin: 0;color: var(--color-body-text);font-size: 24px;font-weight: 700}.baum-new-post-subtitle{margin: 4px 0 0 0;color: var(--color-gray);font-size: 14px}.baum-post-types-grid{display: grid;grid-template-columns: repeat(3, 1fr);gap: 16px;padding: 30px 40px}.baum-post-type-option{display: flex;flex-direction: column;align-items: center;padding: 20px 16px;border: 2px solid #f0f0f0;border-radius: 12px;cursor: pointer;transition: all 0.3s ease;text-decoration: none;color: var(--color-body-text);background: white}.baum-post-type-option:hover{border-color: var(--color-accent);background: #f8f9fa;transform: translateY(-2px);box-shadow: 0 8px 25px rgba(0,0,0,0.1)}.baum-post-type-icon{width: 48px;height: 48px;background: var(--color-accent);border-radius: var(--border-radius);display: flex;align-items: center;justify-content: center;color: white;font-size: 20px;margin-bottom: 12px}.baum-post-type-name{font-size: 16px;font-weight: 600;margin-bottom: 4px}.baum-post-type-desc{font-size: 12px;color: var(--color-gray);text-align: center;line-height: 1.4}.baum-post-form-modal .baum-modal-content{width: 90%;max-width: 800px;max-height: 90vh;display: flex;flex-direction: column}.baum-post-form-header{padding: 30px 40px 20px 40px;border-bottom: 1px solid #f0f0f0;background: #f8f9fa;padding: 0px;width: 100%}.baum-selected-post-type-icon{width: 40px;height: 40px;background: var(--color-accent);border-radius: var(--border-radius);display: flex;align-items: center;justify-content: center;color: white;font-size: 18px}.baum-iframe-container{flex: 1;display: flex;flex-direction: column;min-height: 500px}.baum-iframe-controls{display: flex;gap: 8px;padding: 12px 20px;background: #f8f9fa;border-bottom: 1px solid #e0e0e0}.baum-iframe-btn{padding: 6px 12px;border: 1px solid #ddd;background: white;border-radius: 6px;cursor: pointer;font-size: 12px;transition: all 0.2s ease}.baum-iframe-btn:hover{background: #f0f0f0;border-color: #ccc}.baum-post-iframe{flex: 1;border: none;width: 100%;min-height: 500px;border-radius: var(--border-radius)}.baum-modal .baum-btn{padding: 8px 16px;border: 1px solid transparent;border-radius: var(--border-radius);font-size: 14px;font-weight: 600;cursor: pointer;transition: all 0.2s ease;display: inline-flex;align-items: center;gap: 8px}.baum-modal .baum-btn-primary{background: var(--color-accent);color: white;border-color: var(--color-accent)}.baum-modal .baum-btn-primary:hover{background: var(--color-secondary);border-color: var(--color-secondary)}.baum-modal .baum-btn-secondary{background: transparent;color: var(--color-body-text);border-color: var(--color-border)}.baum-modal .baum-btn-secondary:hover{background: var(--color-background-alt);border-color: var(--color-accent)}.baum-user-dropdown{font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;line-height: 1.3}.baum-user-trigger:hover{background: rgba(0,0,0,0.05)}.baum-user-dropdown-menu a:hover{background: var(--color-background-alt)}.baum-user-dropdown-menu a:last-child:hover{background: rgba(var(--color-danger-rgb), 0.1)}.baum-search-modal .baum-user-dropdown{position: absolute;right: 60px;top: 50%;transform: translateY(-50%)}.baum-modal-content.fullscreen{border-radius: var(--border-radius) !important;overflow: hidden}.baum-modal-content.fullscreen .baum-post-iframe{border-radius: 0}.baum-iframe-loading{position: absolute;top: 0;left: 0;width: 100%;height: 100%;background: rgba(255, 255, 255, 1);display: flex;align-items: center;justify-content: center;z-index: 1000;border-radius: var(--border-radius)}.baum-iframe-loading .fa-spinner{animation: spin 1s linear infinite}@keyframes spin{0%{transform: rotate(0deg)}100%{transform: rotate(360deg)}}@media (max-width: 768px){.baum-modal-content{width: 95%;max-height: 95vh}.baum-post-types-grid{grid-template-columns: repeat(2, 1fr);gap: 12px;padding: 20px}.baum-post-type-option{padding: 16px 12px}.baum-post-type-icon{width: 40px;height: 40px;font-size: 18px}}@media (max-width: 480px){.baum-post-types-grid{grid-template-columns: 1fr}.baum-search-modal .baum-modal-content{top: 10%;width: 95%}.baum-modal-nav{display: none !important}}.baum-modal-nav .baum-modal-trigger:hover{background: rgba(0,0,0,0.1) !important;transform: scale(1.05) !important}.baum-modal-nav .baum-modal-trigger:active{transform: scale(0.95) !important}.baum-modal-header-content{display: flex;align-items: center;justify-content: space-between}.baum-modal-footer-content{display: flex;justify-content: space-between;align-items: center}.baum-modal-footer-info{font-size: 12px;color: var(--color-gray)}.baum-modal-footer-actions{display: flex;gap: 10px}.baum-post-form-modal .baum-modal-content{max-width: 90vw;max-height: 90vh;width: 1000px;display: flex;flex-direction: column}.baum-post-form-content{flex: 1;padding: 20px;overflow: hidden}.baum-post-iframe{width: 100%;height: 600px;border: 1px solid var(--color-border);border-radius: var(--border-radius);background: white;transition: opacity 0.3s ease}.baum-post-iframe[src=""]{opacity: 0.5;pointer-events: none}.baum-post-form-footer{padding: 20px;border-top: 1px solid var(--color-border);background: var(--color-bg)}@media (max-width: 768px){.baum-post-form-modal .baum-modal-content{width: 95vw;height: 95vh}.baum-post-iframe{height: 500px}}.baum-search-modal .baum-modal-content{max-width: 600px;max-height: 80vh;display: flex;flex-direction: column}.baum-search-input-container{padding: 20px 30px}.baum-search-input-wrapper{position: relative}.baum-search-input{width: 100%;padding: 16px 20px 16px 50px;border: 2px solid var(--color-border);border-radius: var(--border-radius);font-size: 16px;outline: none;transition: border-color 0.2s ease}.baum-search-input:focus{border-color: var(--color-accent);box-shadow: 0 0 0 3px rgba(var(--color-accent-rgb), 0.1)}.baum-search-icon{position: absolute;left: 18px;top: 50%;transform: translateY(-50%);color: var(--color-gray);font-size: 16px}.baum-search-results{flex: 1;overflow-y: auto;max-height: 400px}.baum-search-results-list{padding: 0 30px 20px}.baum-search-placeholder{text-align: center;padding: 40px;color: var(--color-gray)}.baum-search-placeholder-icon{font-size: 48px;margin-bottom: 16px;opacity: 0.3}.baum-search-placeholder-text{margin: 0;font-size: 16px}.baum-search-results-item{padding: 12px 0;border-bottom: 1px solid var(--color-border);cursor: pointer;transition: background 0.2s ease}.baum-search-results-item:hover{background: var(--color-bg)}.baum-search-results-item:last-child{border-bottom: none}.baum-search-item-title{font-weight: 600;color: var(--color-body-text);margin-bottom: 4px}.baum-search-item-excerpt{font-size: 14px;color: var(--color-gray);line-height: 1.4}.baum-search-item-meta{font-size: 12px;color: var(--color-gray);margin-top: 4px}.baum-search-footer{padding: 20px 30px;border-top: 1px solid var(--color-border);background: var(--color-bg)}.baum-search-footer-content{display: flex;justify-content: space-between;align-items: center;font-size: 12px;color: var(--color-gray)}kbd{background: var(--color-border);border: 1px solid var(--color-gray);border-radius: 3px;padding: 2px 6px;font-size: 11px;font-family: monospace}.baum-topics-modal .baum-modal-content{max-width: 800px;max-height: 80vh;display: flex;flex-direction: column}.baum-topics-content{flex: 1;padding: 20px 30px}.baum-topics-grid{display: grid;grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));gap: 16px}.baum-topic-card{display: block;padding: 20px;background: var(--color-bg);border: 1px solid var(--color-border);border-radius: var(--border-radius);text-decoration: none;transition: all 0.2s ease}.baum-topic-card:hover{transform: translateY(-2px);box-shadow: 0 4px 12px rgba(0,0,0,0.1);border-color: var(--color-accent)}.baum-topic-name{font-weight: 600;color: var(--color-body-text);margin-bottom: 8px;font-size: 16px}.baum-topic-card:hover .baum-topic-name{color: var(--color-accent)}.baum-topic-description{font-size: 14px;color: var(--color-gray);line-height: 1.4;margin-bottom: 12px}.baum-topic-meta{font-size: 12px;color: var(--color-gray);display: flex;align-items: center;gap: 8px}.baum-topics-empty{grid-column: 1 / -1;text-align: center;padding: 40px;color: var(--color-gray)}.baum-topics-empty-icon{font-size: 48px;margin-bottom: 16px;opacity: 0.3}.baum-topics-empty-text{margin: 0;font-size: 16px}.baum-topics-footer{padding: 20px 30px;border-top: 1px solid var(--color-border);background: var(--color-bg)}@media (max-width: 768px){.baum-topics-grid{grid-template-columns: 1fr}}.baum-donation-modal .baum-modal-content{max-width: 500px;padding: 0}.baum-donation-header{padding: 20px 30px;border-bottom: 1px solid var(--color-border)}.baum-donation-content{padding: 20px 30px 30px}.baum-donation-form{background: white;border-radius: 12px}.baum-donation-icon{text-align: center;margin-bottom: 24px}.baum-donation-icon-circle{width: 60px;height: 60px;background: var(--color-secondary, #333);border-radius: 12px;display: inline-flex;align-items: center;justify-content: center;color: white;font-size: 24px}.baum-form-label{display: block;margin-bottom: 6px;color: var(--color-body-text);font-size: 14px;font-weight: 500}.baum-amount-selection{margin-bottom: 20px}.baum-amount-buttons{display: grid;grid-template-columns: repeat(3, 1fr);gap: 8px;margin-bottom: 12px}.baum-amount-btn{padding: 12px;border: 1px solid var(--color-border);border-radius: var(--border-radius);background: var(--color-bg);color: var(--color-body-text);font-weight: 500;cursor: pointer;transition: all 0.2s ease}.baum-amount-btn:hover, .baum-amount-btn.selected{background: var(--color-accent);color: white;border-color: var(--color-accent)}.baum-custom-amount{position: relative}.baum-currency-symbol{position: absolute;left: 12px;top: 50%;transform: translateY(-50%);color: var(--color-gray);font-weight: 500;font-size: 18px}.baum-custom-amount-input{width: 100%;padding: 12px 12px 12px 32px;border: 1px solid var(--color-border);border-radius: var(--border-radius);font-size: 16px;outline: none;transition: border-color 0.2s ease}.baum-custom-amount-input:focus{border-color: var(--color-accent)}.baum-donation-type{margin-bottom: 20px}.baum-donation-type-options{display: grid;grid-template-columns: 1fr 1fr;gap: 8px}.baum-radio-option{display: flex;align-items: center;padding: 12px;border: 1px solid var(--color-border);border-radius: var(--border-radius);cursor: pointer;transition: all 0.2s ease;background: var(--color-bg)}.baum-radio-option:hover, .baum-radio-option.baum-radio-selected{background: var(--color-accent);color: white;border-color: var(--color-accent)}.baum-radio-option input[type="radio"]{margin-right: 8px}.baum-radio-text{font-weight: 500}.baum-payment-info{margin-bottom: 20px}.baum-form-group{margin-bottom: 12px}.baum-form-row{display: grid;grid-template-columns: 1fr 1fr;gap: 12px}.baum-form-input{width: 100%;padding: 12px;border: 1px solid var(--color-border);border-radius: var(--border-radius);font-size: 14px;outline: none;transition: border-color 0.2s ease}.baum-form-input:focus{border-color: var(--color-accent)}.baum-donate-btn{width: 100%;background: var(--color-secondary, #333);color: white;border: none;border-radius: var(--border-radius);font-size: 18px;font-weight: 600;padding: 14px;cursor: pointer;transition: all 0.2s ease;margin-top: 8px;display: flex;align-items: center;justify-content: center;gap: 8px}.baum-donate-btn:hover{background: var(--color-accent)}.baum-security-badge{text-align: center;border-top: 1px solid var(--color-border);margin-top: 12px;padding-top: 12px}.baum-security-text{display: flex;align-items: center;justify-content: center;gap: 4px;color: var(--color-gray);font-size: 12px;margin: 0}.baum-security-text strong{color: var(--color-gray)}.baum-subscription-modal .baum-modal-content{max-width: 500px;padding: 0}.baum-subscription-header{padding: 20px 30px;border-bottom: 1px solid var(--color-border)}.baum-subscription-content{padding: 20px 30px 30px}.baum-subscription-form{background: white;border-radius: 12px}.baum-subscription-icon{text-align: center;margin-bottom: 24px}.baum-subscription-icon-circle{width: 60px;height: 60px;background: var(--color-secondary, #333);border-radius: 12px;display: inline-flex;align-items: center;justify-content: center;color: white;font-size: 24px}.baum-plan-selection{margin-bottom: 20px}.baum-plan-options{display: flex;flex-direction: column;gap: 12px}.baum-plan-option{display: flex;align-items: flex-start;padding: 16px;border: 1px solid var(--color-border);border-radius: var(--border-radius);cursor: pointer;transition: all 0.2s ease;background: var(--color-bg)}.baum-plan-option:hover, .baum-plan-option.baum-plan-selected{background: var(--color-accent);color: white;border-color: var(--color-accent)}.baum-plan-option input[type="radio"]{margin-right: 12px;margin-top: 2px}.baum-plan-content{flex: 1}.baum-plan-name{font-weight: 600;font-size: 16px;margin-bottom: 4px}.baum-plan-price{font-size: 18px;font-weight: 700;margin-bottom: 8px}.baum-plan-features{display: flex;flex-direction: column;gap: 4px;font-size: 14px;opacity: 0.9}.baum-subscribe-btn{width: 100%;background: var(--color-secondary, #333);color: white;border: none;border-radius: var(--border-radius);font-size: 18px;font-weight: 600;padding: 14px;cursor: pointer;transition: all 0.2s ease;margin-top: 8px;display: flex;align-items: center;justify-content: center;gap: 8px}.baum-subscribe-btn:hover{background: var(--color-accent)}@media (max-width: 768px){.baum-donation-modal .baum-modal-content, .baum-subscription-modal .baum-modal-content{width: 95%;max-width: none}.baum-donation-content, .baum-subscription-content{padding: 15px 20px 20px}.baum-donation-header, .baum-subscription-header{padding: 15px 20px}.baum-amount-buttons{grid-template-columns: 1fr;gap: 8px}.baum-donation-type-options{grid-template-columns: 1fr}.baum-form-row{grid-template-columns: 1fr}.baum-plan-options{gap: 8px}.baum-plan-option{padding: 12px}}@media (max-width: 480px){.baum-modal-header-content{flex-direction: column;align-items: flex-start;gap: 10px}.baum-donation-close, .baum-subscription-close, .baum-search-close, .baum-topics-close{align-self: flex-end}}.baum-subscription-tab{max-width: 100%}.baum-subscription-status{display: flex;align-items: flex-start;gap: 16px;padding: 20px;border-radius: var(--border-radius);margin-bottom: 20px}.baum-subscription-status.baum-status-active{background: #f0f9ff;border: 1px solid #0ea5e9}.baum-subscription-status.baum-status-inactive{background: #fef2f2;border: 1px solid #ef4444}.baum-status-icon{font-size: 24px;margin-top: 4px}.baum-status-active .baum-status-icon{color: #0ea5e9}.baum-status-inactive .baum-status-icon{color: #ef4444}.baum-status-content h5{margin: 0 0 8px 0;font-size: 18px;font-weight: 600;color: var(--color-body-text)}.baum-status-content p{margin: 0 0 8px 0;color: var(--color-gray);line-height: 1.5}.baum-billing-date{display: flex;align-items: center;gap: 8px;font-size: 14px;color: var(--color-gray);margin: 0}.baum-plans-grid{display: grid;grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));gap: 20px;margin-top: 16px}.baum-plan-card{background: white;border: 2px solid var(--color-border);border-radius: var(--border-radius);padding: 24px;position: relative;transition: all 0.3s ease}.baum-plan-card:hover{border-color: var(--color-accent);box-shadow: 0 4px 12px rgba(0,0,0,0.1)}.baum-plan-card.popular{border-color: var(--color-accent);box-shadow: 0 4px 12px rgba(0,0,0,0.1)}.baum-plan-card.current-plan{border-color: var(--color-secondary);background: #f8f9fa}.baum-plan-badge{position: absolute;top: -10px;left: 50%;transform: translateX(-50%);background: var(--color-accent);color: white;padding: 4px 12px;border-radius: 12px;font-size: 12px;font-weight: 600}.baum-plan-header{text-align: center;margin-bottom: 20px}.baum-plan-header h5{margin: 0 0 8px 0;font-size: 20px;font-weight: 600;color: var(--color-body-text)}.baum-plan-price{font-size: 32px;font-weight: 700;color: var(--color-accent)}.baum-plan-price span{font-size: 16px;font-weight: 400;color: var(--color-gray)}.baum-plan-features ul{list-style: none;padding: 0;margin: 0 0 24px 0}.baum-plan-features li{display: flex;align-items: center;gap: 12px;padding: 8px 0;color: var(--color-body-text)}.baum-plan-features li i{color: var(--color-accent);font-size: 14px;width: 16px}.baum-plan-btn{width: 100%;padding: 12px 24px;background: var(--color-accent);color: white;border: none;border-radius: var(--border-radius);font-size: 16px;font-weight: 600;cursor: pointer;transition: all 0.2s ease}.baum-plan-btn:hover{background: var(--color-secondary)}.baum-plan-btn.current{background: var(--color-gray);cursor: not-allowed}.baum-billing-history{background: #f8f9fa;border-radius: var(--border-radius);overflow: hidden;border: 1px solid var(--color-border)}.baum-billing-item{display: grid;grid-template-columns: 100px 1fr 80px 80px;gap: 16px;padding: 16px;border-bottom: 1px solid var(--color-border);align-items: center}.baum-billing-item:last-child{border-bottom: none}.baum-billing-date{font-size: 14px;color: var(--color-gray)}.baum-billing-description{font-weight: 500;color: var(--color-body-text)}.baum-billing-amount{font-weight: 600;color: var(--color-body-text);text-align: right}.baum-billing-status{padding: 4px 8px;border-radius: 12px;font-size: 12px;font-weight: 600;text-align: center}.baum-billing-status.success{background: #dcfce7;color: #166534}@media (max-width: 768px){.baum-plans-grid{grid-template-columns: 1fr}.baum-billing-item{grid-template-columns: 1fr;gap: 8px;text-align: left}.baum-billing-amount{text-align: left}}.baum-iframe-container{display: flex;flex-direction: column;height: 600px;width: 100%}.baum-iframe-controls{display: flex;gap: 8px;padding: 12px 16px;background: #f8f9fa;border-bottom: 1px solid #e0e0e0;align-items: center}.baum-iframe-btn{padding: 6px 12px;border: 1px solid #ddd;background: white;border-radius: 4px;cursor: pointer;font-size: 12px;transition: all 0.2s ease;display: flex;align-items: center;gap: 4px}.baum-iframe-btn:hover{background: #f0f0f0;border-color: #ccc}.baum-iframe-btn.baum-btn-primary{background: var(--color-accent);color: white;border-color: var(--color-accent)}.baum-iframe-btn.baum-btn-primary:hover{background: var(--color-secondary)}.baum-post-iframe{flex: 1;border: none;width: 100%;height: 100%}.baum-search-trigger{display: inline-block;text-decoration: none;color: inherit;cursor: pointer}.baum-search-trigger:hover{text-decoration: none;color: inherit}.baum-search-trigger .menu-search{display: flex;align-items: center;gap: 8px;padding: 8px 12px;border-radius: var(--border-radius-small, 4px);transition: all 0.2s ease}.baum-search-trigger:hover .menu-search{background: rgba(0,0,0,0.05)}.baum-search-trigger .search-icon{display: flex;align-items: center;justify-content: center;width: 20px;height: 20px}.baum-search-trigger .search-text{font-size: 14px;font-weight: 500}.baum-topics-modal{background: rgba(0, 0, 0, 0.9)}.baum-topics-modal-content{position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 90%;max-width: 800px;max-height: 90vh;background: #1a1a1a;border-radius: var(--border-radius);overflow: hidden;display: flex;flex-direction: column}.baum-topics-header{position: relative;padding: 40px 40px 20px;text-align: center;background: #1a1a1a}.baum-topics-close{position: absolute;top: 20px;right: 20px;background: none;border: none;color: #888;font-size: 20px;cursor: pointer;width: 32px;height: 32px;display: flex;align-items: center;justify-content: center;border-radius: 50%;transition: all 0.2s ease}.baum-topics-close:hover{background: rgba(255, 255, 255, 0.1);color: #fff}.baum-topics-title{font-size: 32px;font-weight: 700;color: #fff;margin: 0 0 12px 0;line-height: 1.2}.baum-topics-subtitle{font-size: 16px;color: #888;margin: 0;line-height: 1.4}.baum-topics-content{flex: 1;padding: 20px 40px;overflow-y: auto}.baum-topics-grid{display: grid;grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));gap: 12px;margin-bottom: 20px}.baum-topic-button{background: #2a2a2a;border: 2px solid #333;color: #fff;padding: 16px 20px;border-radius: 12px;font-size: 16px;font-weight: 500;cursor: pointer;transition: all 0.2s ease;text-align: center;position: relative;min-height: 60px;display: flex;align-items: center;justify-content: center;flex-direction: column;gap: 4px}.baum-topic-button:hover{background: #333;border-color: #444;transform: translateY(-1px)}.baum-topic-button.selected{background: #ff6b35;border-color: #ff6b35;color: #fff}.baum-topic-button.selected:hover{background: #e55a2b;border-color: #e55a2b}.baum-topic-count{font-size: 12px;opacity: 0.7;font-weight: 400}.baum-topics-footer{padding: 30px 40px;background: #1a1a1a;border-top: 1px solid #333;display: flex;align-items: center;justify-content: space-between}.baum-topics-actions{display: flex;gap: 16px;align-items: center}.baum-topics-skip{background: none;border: none;color: #888;font-size: 16px;cursor: pointer;padding: 12px 20px;border-radius: 8px;transition: all 0.2s ease}.baum-topics-skip:hover{background: rgba(255, 255, 255, 0.05);color: #fff}.baum-topics-continue{background: #ff6b35;border: none;color: #fff;font-size: 16px;font-weight: 600;cursor: pointer;padding: 16px 32px;border-radius: 12px;transition: all 0.2s ease;min-width: 120px}.baum-topics-continue:hover{background: #e55a2b;transform: translateY(-1px)}.baum-topics-continue:disabled{background: #444;color: #888;cursor: not-allowed;transform: none}.baum-topics-selected-count{color: #888;font-size: 14px}.baum-no-topics{text-align: center;padding: 60px 20px;color: #888}@media (max-width: 768px){.baum-topics-modal-content{width: 95%;max-height: 95vh}.baum-topics-header{padding: 30px 20px 15px}.baum-topics-title{font-size: 24px}.baum-topics-subtitle{font-size: 14px}.baum-topics-content{padding: 15px 20px}.baum-topics-grid{grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));gap: 10px}.baum-topic-button{padding: 12px 16px;font-size: 14px;min-height: 50px}.baum-topics-footer{padding: 20px;flex-direction: column;gap: 16px}.baum-topics-actions{order: 2;width: 100%;justify-content: center}.baum-topics-selected-count{order: 1}}.style-guide-modal{display: none;position: fixed;z-index: 1000;left: 0;top: 0;width: 100%;height: 100%;background-color: rgba(0,0,0,0.5)}.style-guide-modal-content{background-color: white;margin: 15% auto;padding: 20px;border-radius: var(--border-radius);width: 80%;max-width: 500px;position: relative}.style-guide-modal-close{color: #aaa;float: right;font-size: 28px;font-weight: bold;cursor: pointer}.style-guide-modal-close:hover{color: #000}.style-guide-alert-container{display: flex;flex-direction: column;gap: 15px}.style-guide-breaking-news{background: #dc3545;color: white;padding: 15px;border-radius: var(--border-radius);margin: 20px 0;display: flex;align-items: center;gap: 10px}.style-guide-breaking-badge{background: white;color: #dc3545;padding: 4px 8px;border-radius: 4px;font-weight: bold;font-size: 12px}.baum-modal{display: none;position: fixed;z-index: 1000;left: 0;top: 0;width: 100%;height: 100%;overflow: auto;background-color: rgba(0, 0, 0, 0.8);justify-content: center;align-items: center}.baum-modal-content{background-color: var(--color-white);margin: auto;padding: 20px;border: 1px solid var(--color-septenary);height: auto;width: auto;max-width: 90%;max-height: 90%;width: fit-content;overflow: hidden;border-radius: var(--border-radius);position: relative;display: flex;flex-direction: column;justify-content: center;align-items: center;top: 5%;box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15)}.baum-modal-content.no-image{min-width: 400px;max-width: 600px;padding: 30px}.baum-modal-content.has-image{padding: 10px;background-color: var(--color-secondary)}.baum-modal-title{text-align: center;color: var(--color-secondary);font-size: 18px;font-weight: 600;margin-bottom: 15px}.baum-modal-content.has-image .baum-modal-title{color: #fff;position: absolute;top: 20px;left: 20px;z-index: 10;background: rgba(0, 0, 0, 0.7);padding: 8px 12px;border-radius: var(--border-radius-small);font-size: 14px;margin-bottom: 0}.baum-modal-body{color: var(--color-secondary);display: flex;justify-content: center;align-items: center;max-width: 100%;max-height: 100%;border-radius: var(--border-radius);line-height: 1.6}.baum-modal-content.no-image .baum-modal-body{text-align: left;padding: 0}.baum-modal-content.has-image .baum-modal-body{color: #fff;padding: 0}.baum-modal-img{max-width: 100%;width: auto;margin-bottom: 0px;border-radius: var(--border-radius, 10px);aspect-ratio: auto;max-height: 85vh}.baum-modal-close{color: var(--color-quaternary);float: right;font-size: 24px;font-weight: bold;position: absolute;top: 15px;right: 15px;cursor: pointer;display: flex;align-items: center;justify-content: center;background: var(--color-denary);width: 32px;height: 32px;border-radius: 50%;border: none;transition: all 0.2s ease;z-index: 10}.baum-modal-content.has-image .baum-modal-close{color: #f1f1f1;background: rgba(0, 0, 0, 0.7);font-size: 20px}.baum-modal-img-info:hover{color: #ffffff;opacity: 100%}.baum-modal-img-info{color: #ffffff;float: right;font-size: 28px;font-weight: bold;position: absolute;top: 20px;right: 80px;cursor: pointer;display: block;background: rgba(0, 0, 0, 0.75);line-height: 0px;border-radius: var(--border-radius, 10px);padding: 10px;padding-top: 10px;opacity: 75%}.baum-modal-close:hover, .baum-modal-close:focus{background: var(--color-accent);color: white;transform: scale(1.1)}.baum-modal-content.has-image .baum-modal-close:hover, .baum-modal-content.has-image .baum-modal-close:focus{background: rgba(0, 0, 0, 0.9);color: #ffffff;transform: scale(1.1)}.baum-modal-caption{color: #f1f1f1;background: rgba(0, 0, 0, 0.6);padding: 10px 20px;text-align: center;border-bottom-right-radius: var(--border-radius, 10px);border-bottom-left-radius: var(--border-radius, 10px);width: 100%;position: absolute;bottom: 10px;text-align: left;font-size: 16px}.baum-modal-caption:before, .baum-modal-caption:before{font-family: "Font Awesome 5 Free";content: "\f030";font-weight: 900;margin-right: 5px}.slick-prev, .slick-next{color: #fff;font-size: 24px}.slick-prev:before, .slick-next:before{color: black}.slick-dots li button:before{font-size: 12px;color: black}.slick-dots li.slick-active button:before{color: blue}.baum-account-trigger img{border-radius: 50%}.baum-account-trigger:hover img{opacity: 0.8}.baum-account-menu{padding: 0;margin: 0;list-style: none;margin-top: 10px}.account-menu-header{padding: 15px;border-bottom: 1px solid var(--color-septenary)}.account-user-info{display: flex;align-items: center;gap: 12px}.account-user-info img{border-radius: 50%}.account-user-name{font-weight: 600;color: var(--color-secondary);font-size: 14px}.account-user-email{font-size: 12px;color: var(--color-quaternary)}.account-menu-item{margin: 0;padding: 0}.account-menu-item a{display: flex;align-items: center;gap: 10px;padding: 10px 15px;color: var(--color-secondary);text-decoration: none;font-size: 14px}.account-menu-item a:hover{background: var(--color-denary);color: var(--color-primary)}.account-menu-item a i{width: 16px;text-align: center;color: var(--color-quaternary)}.account-menu-divider{height: 1px;background: var(--color-septenary);margin: 5px 0}.dark-mode-item{padding: 10px 15px;display: flex;align-items: center;justify-content: space-between}.dark-mode-label{display: flex;align-items: center;gap: 10px;color: var(--color-secondary);font-size: 14px}.dark-mode-label i{width: 16px;text-align: center;color: var(--color-quaternary)}.dark-mode-toggle-wrapper .baum-dark-toggle{margin: 0;scale: 0.8}