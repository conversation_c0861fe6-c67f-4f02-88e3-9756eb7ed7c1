<?php
/*
Plugin Name: <PERSON><PERSON>'s Lunar Cycle
Description: Provides a shortcode to display moon information with a 3D visual.
Version: 1.0
Author: Your Name
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

function baum_lunar_cycle_enqueue_scripts () {
  wp_enqueue_script(
    'suncalc-js', 
    'https://cdnjs.cloudflare.com/ajax/libs/suncalc/1.8.0/suncalc.min.js', 
    array(), 
    '1.8.0', 
    true
  );
  wp_enqueue_script(
    'baum-lunar-cycle-js', 
    plugin_dir_url(__FILE__) . 'js/baum-lunar-cycle.js', 
    array('jquery', 'suncalc-js'), 
    '1.0', 
    true
  );
  // wp_enqueue_script(
  //   'baum-solar-system-js', 
  //   plugin_dir_url(__FILE__) . 'js/baum-solar-system.js', 
  //   array('jquery'), 
  //   '1.0', 
  //   true
  // );

  // Use wp_add_inline_script to add the plugin URL as a JS variable
  $plugin_url = plugin_dir_url(__FILE__);
  $inline_script = "var plugin_url = '" . esc_js($plugin_url) . "';";
  wp_add_inline_script('baum-lunar-cycle-js', $inline_script);

  wp_enqueue_style(
    'baum-lunar-cycle-css', 
    plugin_dir_url(__FILE__) . 'css/baum-lunar-cycle.css'
  );
}

add_action('wp_enqueue_scripts', 'baum_lunar_cycle_enqueue_scripts');



if (function_exists('acf_add_local_field_group')) {

  $widget_keys = array(
      'baum_lunar_cycle_widget',
      'baum_numerology_widget',
      'baum_solar_system_widget',
      'baum_astrology_widget',
      'baum_chinese_astrology_widget',
      'baum_astrology_table_widget',
      'baum_natal_chart_widget',
      'baum_planetary_aspects_widget',
      'baum_transit_predictions_widget',
  );

  foreach ($widget_keys as $widget_key) {
      acf_add_local_field_group(array(
          'key' => 'group_' . $widget_key,
          'title' => 'Settings for ' . ucwords(str_replace('_', ' ', $widget_key)),
          'fields' => array(
              array(
                  'key' => 'field_selection_mode_' . $widget_key,
                  'label' => 'Data Source',
                  'name' => 'data_source',
                  'type' => 'select',
                  'instructions' => 'Choose the source for determining the data.',
                  'choices' => array(
                      'current_post' => 'Current Post',
                      'choose_post' => 'Choose Post',
                      'choose_date' => 'Choose Date',
                      'enter_variable' => 'Enter Window Variable',
                  ),
                  'default_value' => 'current_post',
                  'ui' => 1,
              ),
              array(
                  'key' => 'field_moon_post_' . $widget_key,
                  'label' => 'Choose Post',
                  'name' => 'moon_post',
                  'type' => 'post_object',
                  'instructions' => 'Select a post to use its publish date for the data.',
                  'post_type' => array('post'), // Modify this as needed for custom post types
                  'return_format' => 'object',
                  'conditional_logic' => array(
                      array(
                          array(
                              'field' => 'field_selection_mode_' . $widget_key,
                              'operator' => '==',
                              'value' => 'choose_post',
                          ),
                      ),
                  ),
              ),
              array(
                  'key' => 'field_moon_date_' . $widget_key,
                  'label' => 'Choose Date',
                  'name' => 'moon_date',
                  'type' => 'date_picker',
                  'instructions' => 'Select a custom date for the data.',
                  'display_format' => 'Y-m-d',
                  'return_format' => 'Y-m-d',
                  'conditional_logic' => array(
                      array(
                          array(
                              'field' => 'field_selection_mode_' . $widget_key,
                              'operator' => '==',
                              'value' => 'choose_date',
                          ),
                      ),
                  ),
              ),
              array(
                  'key' => 'field_variable_name_' . $widget_key,
                  'label' => 'Window Variable',
                  'name' => 'window_variable',
                  'type' => 'text',
                  'instructions' => 'Enter the name of the JavaScript variable on the window object.',
                  'conditional_logic' => array(
                      array(
                          array(
                              'field' => 'field_selection_mode_' . $widget_key,
                              'operator' => '==',
                              'value' => 'enter_variable',
                          ),
                      ),
                  ),
              ),
          ),
          'location' => array(
              array(
                  array(
                      'param' => 'widget',
                      'operator' => '==',
                      'value' => $widget_key,
                  ),
              ),
          ),
      ));
  }
} else {
  // Fallback if ACF is not enabled
  echo '<p>Will use the date of the current post. Enable the ACF plugin for more options.</p>';
}


function baum_lunar_cycle_shortcode ($atts) {
  global $post;

  // Parse shortcode attributes
  $atts = shortcode_atts([
    'date' => 'today',
    'current_post' => '0'
  ], $atts);

  $current_post = $atts['current_post'] ? true : false;

  // Determine the date to use
  $date = $atts['date'] === 'today' ? current_time('Y-m-d H:i:s') : $atts['date'];

  // Generate a unique ID for each instance of the shortcode
  static $counter = 0;
  $counter++;
  $unique_id = 'moon-visual-' . $counter;

  // Output the HTML container with a unique ID and pass the date to JavaScript
  ob_start();
  ?>
  <div class='baum-card-container' style='margin:5px;display:inline-block;width:355px;'>
    <div class='baum-card baum-lunar-cycle'>
      <div id="<?php echo esc_attr($unique_id); ?>" class='moon-visual'></div>
      <div class='moon-info'>
        <h6 class='small phase tertiary-bg' id="phase-<?php echo esc_attr($unique_id); ?>"></h6> 
        <!-- <small class='uppercase bolder'>Age:</small>  -->
        <br>
        <div class='grid-2'>
          <div> 
            <small class='uppercase bolder'>Illuminati:</small> 
            <span class='illumination' id="illumination-<?php echo esc_attr($unique_id); ?>"></span> 
          </div> 
          <div style='margin-left:10px;'> 
            <small class='uppercase bolder'>Age:</small> 
            <span class='age' id='age-<?php echo esc_attr($unique_id); ?>'></span>
          </div> 
          <div> 
            <small class='uppercase bolder'>Distance:</small> 
            <span class='distance' id='distance-<?php echo esc_attr($unique_id); ?>'></span>
          </div> 
          <div> </div> 
        </div>
      </div>
    </div> 
    <div class='baum-card-bottom' stylle='line-height:23px;'>
      <div class='baum-card-info'>
        <small class='gray smaller'>
          <?php if ($current_post) { ?>
            The lunar phase at the time of publishing: <strong><?php echo $date; ?></strong>
          <?php } else { ?>
            <?php echo $date; ?>
          <?php } ?>
        </small>
      </div>
    </div>
  </div>
  <script>
    setTimeout(function () {
      jQuery(document).ready(function ($) {
        const date = "<?php echo esc_js($date); ?>";
        initMoonVisual(date, "<?php echo esc_js($unique_id); ?>");
      }); 
    }, 350)

    // document.addEventListener('DOMContentLoaded', function () {
    //   const date = "<?php // echo esc_js($date); ?>";
    //   window.initMoonVisual(date, "<?php // echo esc_js($unique_id); ?>");
    // });
  </script>
  <?php
  return ob_get_clean();
}

add_shortcode('baum_lunar_cycle', 'baum_lunar_cycle_shortcode');




// okay, i would like to change the plugin name to Baum's Astrology and include the following code as a shortcode. The shortcode should allow me to choose 


class Baum_Lunar_Cycle_Widget extends WP_Widget {

  public function __construct() {
      parent::__construct(
          'baum_lunar_cycle_widget',
          'Baum’s Lunar Cycle',
          array('description' => 'Displays the moon phase for the published date of the current post.')
      );
  }

  // Output the widget content
  public function widget($args, $instance) {
      echo $args['before_widget'];

      // Get the widget title
      $title = !empty($instance['title']) ? $instance['title'] : false;
      if ($title) {
        echo $args['before_title'] . apply_filters('widget_title', $title) . $args['after_title'];
      }

      // Get the current post publish date
      if (is_singular()) {
          $post_id = get_the_ID();
          $date = get_the_date('Y-m-d', $post_id);
      } else {
          // Fallback if there's no current post
          $date = current_time('Y-m-d');
      }

      // Output the moon phase using the publish date
      echo do_shortcode('[baum_lunar_cycle date="' . esc_attr($date) . '" current_post="1"]');

      // Add a note below the moon-info box with a custom class
      // echo '<div class="center"><small class="gray smaller">The lunar phase at the time of publishing</small></div>';

      echo $args['after_widget'];
  }

  // Admin form with a note about the widget
  public function form($instance) {
      $title = !empty($instance['title']) ? $instance['title'] : '';
      ?>
      <p>
          <label for="<?php echo esc_attr($this->get_field_id('title')); ?>">Title:</label>
          <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" 
                 name="<?php echo esc_attr($this->get_field_name('title')); ?>" 
                 type="text" value="<?php echo esc_attr($title); ?>">
      </p>
      <p><em>This widget displays the moon phase for the published date of the current post.</em></p>
      <?php
      /*
      // Commented-out code for adding multiple moon phases
      <div class="moon-phases-wrapper">
          <h4>Moon Phases (for future use)</h4>
          <p>Multiple moon phase options coming soon.</p>
      </div>
      */
  }

  // Save the widget form data
  public function update($new_instance, $old_instance) {
      $instance = array();
      $instance['title'] = (!empty($new_instance['title'])) ? strip_tags($new_instance['title']) : '';
      return $instance;
  }
}

// Register the widget
function baum_register_lunar_cycle_widget() {
  register_widget('Baum_Lunar_Cycle_Widget');
}

add_action('widgets_init', 'baum_register_lunar_cycle_widget');

/**
 * Shortcode for Baum's Solar System
 *
 * Displays a solar system map based on the provided date or other sources.
 *
 * @param array $atts Shortcode attributes.
 * @return string The HTML and JavaScript to render the solar system map.
 */

 function baum_solar_system_shortcode($atts) {
  // Define default attributes
  $atts = shortcode_atts(
      array(
          'date' => 'today',         // Default date is 'today'
          'post_id' => '',           // Post ID for 'choose_post' mode
          'window_variable' => '',   // Variable name for 'enter_variable' mode
      ),
      $atts,
      'baum_solar_system'
  );

  // Enqueue JavaScript
  wp_enqueue_script('baum-solar-system-script', plugin_dir_url(__FILE__) . '/js/baum-solar-system.js', array('jquery'), null, true);

  // Determine the date value
  $date = '';
  $mode_description = '';

  if ($atts['date'] === 'current_post') {
      // Use current post date
      if (is_single() && $post = get_post()) {
          $date = get_the_date('Y-m-d', $post);
          $mode_description = 'Using the current post\'s publish date.';
      } else {
          return '<p>Error: The "current_post" mode requires the shortcode to be used within a post context.</p>';
      }
  } elseif ($atts['date'] === 'choose_post') {
      // Use the post date of a specific post
      if (!empty($atts['post_id']) && $post = get_post($atts['post_id'])) {
          $date = get_the_date('Y-m-d', $post);
          $mode_description = 'Using the publish date of the selected post.';
      } else {
          return '<p>Error: The "choose_post" mode requires a valid "post_id" attribute. Please provide a valid post ID.</p>';
      }
  } elseif ($atts['date'] === 'choose_date') {
      // Use the specified date
      $date = date('Y-m-d', strtotime($atts['date']));
      $mode_description = 'Using the specified date: ' . esc_html($date) . '.';
  } elseif ($atts['date'] === 'enter_variable') {
      // Use a JavaScript variable
      if (!empty($atts['window_variable'])) {
          $date = '<script>document.write(window["' . esc_js($atts['window_variable']) . '"] || "");</script>';
          $mode_description = 'Using the JavaScript variable: ' . esc_html($atts['window_variable']) . '.';
      } else {
          return '<p>Error: The "enter_variable" mode requires a "window_variable" attribute. Please provide a valid JavaScript variable name.</p>';
      }
  } else {
      // Default to 'today'
      $date = date('Y-m-d');
      $mode_description = 'Using today\'s date.';
  }

  // Generate the solar system map HTML and JS
  ob_start();
  ?>
  <div id="solar-system">
      <canvas id="solar-system-canvas"></canvas>
      <p class='center'><small class='gray smaller'><?php echo esc_html($mode_description); ?></small></p>
  </div>
  <script>
    setTimeout(function () {
      jQuery(document).ready(function ($) {
        initSolarSystem('<?php echo $date; ?>', 'solar-system-canvas'); 
        // setTimeout(function () {
        //   $('.tooltip-planet[data-index="2"]').click();
        //   setTimeout(function () {
        //     $('.tooltip-planet[data-index="3"]').click();
        //     setTimeout(function () {
        //       $('.tooltip-planet[data-index="4"]').click();
        //       setTimeout(function () {
        //         $('.tooltip-planet[data-index="5"]').click();
        //         setTimeout(function () {
        //           $('.tooltip-planet[data-index="6"]').click();
        //           setTimeout(function () {
        //             $('.tooltip-planet[data-index="7"]').click();
        //             setTimeout(function () {
        //               $('.tooltip-planet[data-index="0"]').click();
        //               setTimeout(function () {
        //                 $('.tooltip-planet[data-index="1"]').click();
        //               }, 2000);
        //             }, 2000);
        //           }, 2000);
        //         }, 2000);
        //       }, 2000);
        //     }, 2000);
        //   }, 2000);          
        // }, 1350);
      });
    }, 350);

  </script>
  <?php
  return ob_get_clean();
}

add_shortcode('baum_solar_system', 'baum_solar_system_shortcode');

/**
 * Class Baum_Solar_System_Widget
 *
 * A widget to display the Baum Solar System map using the baum_solar_system shortcode.
 */
class Baum_Solar_System_Widget extends WP_Widget {

  /**
   * Constructor for the widget.
   */
  public function __construct() {
      parent::__construct(
          'baum_solar_system_widget', // Base ID
          'Baum Solar System Widget', // Name
          array('description' => 'A widget to display the Baum Solar System map.')
      );
  }

  /**
   * Output the widget on the frontend.
   *
   * @param array $args Widget arguments.
   * @param array $instance Saved values from the widget form.
   */
  public function widget($args, $instance) {
      echo $args['before_widget'];

      // Widget Title
      if (!empty($instance['title'])) {
          echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
      }

      // Default date if ACF is not available
      $date = 'current_post';
      $mode_description = 'Using the current post\'s date as the default.';

      // Check for ACF and get custom field values
      if (function_exists('get_field')) {
          $data_source = get_field('data_source', 'widget_' . $args['widget_id']);

          switch ($data_source) {
              case 'choose_post':
                  $post = get_field('moon_post', 'widget_' . $args['widget_id']);
                  if ($post) {
                      $date = get_the_date('Y-m-d', $post);
                      // $mode_description = 'Using the selected post\'s date.';
                  } else {
                      echo '<p>Error: No post selected for "Choose Post" mode.</p>';
                      return;
                  }
                  break;
              case 'choose_date':
                  $custom_date = get_field('moon_date', 'widget_' . $args['widget_id']);
                  if ($custom_date) {
                      $date = $custom_date;
                      // $mode_description = 'Using the selected custom date.';
                  } else {
                      echo '<p>Error: No custom date selected for "Choose Date" mode.</p>';
                      return;
                  }
                  break;
              case 'enter_variable':
                  $window_variable = get_field('window_variable', 'widget_' . $args['widget_id']);
                  if ($window_variable) {
                      $date = '<script>document.write(window["' . esc_js($window_variable) . '"] || "");</script>';
                      // $mode_description = 'Using the JavaScript variable: ' . esc_html($window_variable) . '.';
                  } else {
                      echo '<p>Error: No JavaScript variable specified for "Enter Variable" mode.</p>';
                      return;
                  }
                  break;
              case 'current_post':
              default:
                  if (is_single() && $post = get_post()) {
                      $date = 'current_post'; // get_the_date('Y-m-d', $post);
                      // $mode_description = 'Using the current post\'s publish date.';
                  } else {
                      echo '<p>Error: Current post date not available. Ensure this widget is used within a post context.</p>';
                      return;
                  }
                  break;
          }
      }

      // Render the shortcode
      // echo '<p>' . esc_html($mode_description) . '</p>';
      echo do_shortcode('[baum_solar_system date="' . esc_attr($date) . '"]');

      echo $args['after_widget'];
  }

  /**
   * Output the widget settings form in the admin.
   *
   * @param array $instance Previously saved values from the database.
   */
  public function form($instance) {
      // Widget Title
      $title = !empty($instance['title']) ? $instance['title'] : 'Baum Solar System';
      ?>
      <p>
          <label for="<?php echo $this->get_field_id('title'); ?>">Title:</label>
          <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
      </p>
      <p><em>The data source for the widget is managed through Advanced Custom Fields (ACF).</em></p>
      <?php
  }

  /**
   * Process widget options to be saved.
   *
   * @param array $new_instance New values from the widget form.
   * @param array $old_instance Previously saved values.
   * @return array Updated values to be saved.
   */
  public function update($new_instance, $old_instance) {
      $instance = array();
      $instance['title'] = sanitize_text_field($new_instance['title']);
      return $instance;
  }
}

/**
* Register Baum Solar System Widget.
*/
function register_baum_solar_system_widget() {
  register_widget('Baum_Solar_System_Widget');
}
add_action('widgets_init', 'register_baum_solar_system_widget');




// 
// Numerology (root number) 
// 
// Register the numerology shortcode
function baum_numerology_shortcode($atts) {
  global $post;

  // Generate a unique ID for each instance
  $unique_id = uniqid('numerologyResult_');

  // Get the date from shortcode attributes or default to the post date
  $atts = shortcode_atts(array(
      'date' => isset($post->post_date) ? $post->post_date : date('Y-m-d'),
  ), $atts, 'baum_numerology');

  // Ensure date is in 'YYYY-MM-DD' format
  $date = date('Y-m-d', strtotime($atts['date']));

  // Enqueue JavaScript
  wp_enqueue_script('baum-numerology-script', plugin_dir_url(__FILE__) . '/js/baum-numerology.js', array('jquery'), null, true);
  
  // Output container with unique ID for the result and embed the date as a data attribute
  return "<div id='$unique_id' class='numerologyResult' data-date='$date'><p class='center'>Loading Numerology Report...</p></div><div class='center'>
      <span class='black small'>
        Numerology Report for the date: <strong>$date</strong> 
      </span>
    </div>";
}
add_shortcode('baum_numerology', 'baum_numerology_shortcode');

// Register the numerology widget
function baum_register_numerology_widget() {
  register_widget('Baum_Numerology_Widget');
}
add_action('widgets_init', 'baum_register_numerology_widget');

class Baum_Numerology_Widget extends WP_Widget {
  function __construct() {
      parent::__construct(
          'baum_numerology_widget',
          __('Baum\'s Numerology Widget', 'text_domain'),
          array('description' => __('Displays numerology report based on date', 'text_domain'))
      );
  }

  // Display the widget output
  public function widget($args, $instance) {
      global $post;

      echo $args['before_widget'];
      $title = !empty($instance['title']) ? $instance['title'] : 'Numerology Report';
      echo $args['before_title'] . apply_filters('widget_title', $title) . $args['after_title'];

      // Generate a unique ID for each widget instance
      $unique_id = uniqid('numerologyResult_');
      
      // Default to post date if available
      $date = isset($post->post_date) ? date('Y-m-d', strtotime($post->post_date)) : date('Y-m-d');

      // Output container for the result with a unique ID and data-date attribute
      echo "<div id='$unique_id' class='numerologyResult' data-date='$date'>Loading Numerology Report...</div><div class='center'>
      <span class='black small'>
        Numerology Report for the date: <strong>$date</strong> 
      </span>
      </div>";

      // Enqueue JavaScript
      wp_enqueue_script('baum-numerology-script', plugin_dir_url(__FILE__) . '/js/baum-numerology.js', array('jquery'), null, true);

      echo $args['after_widget'];
  }

  // Widget settings form in the admin area
  public function form($instance) {
      $title = !empty($instance['title']) ? $instance['title'] : 'Numerology Report';
      ?>
      <p>
          <label for="<?php echo $this->get_field_id('title'); ?>"><?php _e('Title:'); ?></label> 
          <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
      </p>
      <?php 
  }

  // Save widget settings
  public function update($new_instance, $old_instance) {
      $instance = array();
      $instance['title'] = (!empty($new_instance['title'])) ? strip_tags($new_instance['title']) : '';
      return $instance;
  }
}

