<?php
/**
 * BaumPress Theme Functions
 *
 * This file contains the main theme functionality including theme setup,
 * custom post types, taxonomies, enqueue scripts, and various theme features.
 * It serves as the central hub for all theme-related functionality.
 *
 * @package BaumPress
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
  exit;
}

// Load AJAX handlers for post editor
require_once get_template_directory() . '/inc/editor-ajax.php';

// Load rendition demo functionality
require_once get_template_directory() . '/functions-rendition-demo.php';

// Load Activity Content Types system (handled by baum-groups plugin)
// require_once get_template_directory() . '/includes/activity-content-types.php';
// require_once get_template_directory() . '/includes/activity-content-types-helpers.php';

/**
 * Theme Configuration Constants and Color Schemes
 *
 * Action Network Color Scheme:
 * Primary: #fd570d
 * Secondary: #cc70eb
 * Tertiary: #1781fa
 * Dark: #140817
 * Background: #211d22
 *
 * USA Patriot Color Scheme:
 * Primary: #bc0c0c
 * Secondary: #042b5b
 * Tertiary: #00397f
 * Quaternary: #2d2d2d
 */

// add_action('init', function () {
//   error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_USER_NOTICE & ~E_USER_DEPRECATED);
//   ini_set('log_errors', '1');
//   ini_set('display_errors', '0');
//   ini_set('error_log', ABSPATH . 'wp-content/debug.log');
// });

// add_filter('wp_debug_log', function ($log_entry) {
//   // Suppress notices and deprecated warnings from being logged
//   if (strpos($log_entry, 'Notice:') !== false || strpos($log_entry, 'Deprecated:') !== false) {
//       return false; // Skip logging this entry
//   }
//   return $log_entry; // Log all other entries
// });

function get_all_registered_shortcodes () {
  global $shortcode_tags;
  return array_keys($shortcode_tags);
}

global $YOUR_ASSEMBLYAI_API_KEY;
$YOUR_ASSEMBLYAI_API_KEY = '********************************';
// https://www.assemblyai.com/pricing
//

//
// Summary of the magic:
// Email: From site’s admin email (Settings > General) or builds a
// <NAME_EMAIL>.
// Name: From site title.
// Fallback safety net: <EMAIL> and 'WordPress' if all else fails.
//

/**
 * Sets the "From" email address for all WordPress emails
 *
 * This filter ensures that all emails sent by WordPress use the site's admin email
 * as the sender, or falls back to a noreply@ address using the site's domain if
 * the admin email is not valid.
 *
 * @param string $email The default email address
 * @return string The modified email address
 *
 * @since 1.0.0
 */
add_filter('wp_mail_from', function($email) {
  $admin_email = get_option('admin_email');
  return is_email($admin_email) ? $admin_email : 'noreply@' . parse_url(home_url(), PHP_URL_HOST);
});

/**
 * Sets the "From" name for all WordPress emails
 *
 * This filter ensures that all emails sent by WordPress use the site's name
 * as the sender name, or falls back to 'WordPress' if the site name is not set.
 *
 * @param string $name The default sender name
 * @return string The modified sender name
 *
 * @since 1.0.0
 */
add_filter('wp_mail_from_name', function ($name) {
  $site_name = get_option('blogname');
  return $site_name ?: 'WordPress';
});

/**
 * Fixes PHPMailer configuration for WordPress emails
 *
 * This action ensures that the PHPMailer object is properly configured with
 * valid "From" email and name values. It specifically fixes the common issue
 * of emails being sent from "wordpress@localhost" by replacing it with the
 * site's admin email or a noreply@ address.
 *
 * @param PHPMailer $phpmailer The PHPMailer instance
 * @return void
 *
 * @since 1.0.0
 */
add_action('phpmailer_init', function ($phpmailer) {
  $admin_email = get_option('admin_email');
  $site_name = get_option('blogname');
  $fallback_email = 'noreply@' . parse_url(home_url(), PHP_URL_HOST);

  if (empty($phpmailer->From) || $phpmailer->From === 'wordpress@localhost') {
      error_log('[Bit SMTP Fix] Forcing fallback From address via Site Settings.');
      $phpmailer->From = is_email($admin_email) ? $admin_email : $fallback_email;
      $phpmailer->FromName = $site_name ?: 'WordPress';
  }
});





///////////////////////////////////// 
// Replace Howdy in the admin with Hallo, Hello, Hi, Shalom, Konnichiwa, etc 
///////////////////////////////////// 

function replace_greeting ($translated, $text, $domain) {
  $custom_greeting = get_theme_mod('baum_greeting', 'Howdy, %s');
  if ('Howdy, %s' === $text) { 
      return str_replace('Howdy, %s', $custom_greeting, $translated); 
  } 
  return $translated; 
}

add_filter('gettext', 'replace_greeting', 10, 3); 

///////////////////////////////////// 
// Replace Howdy in all emails with Hallo, Hello, Hi, Shalom, Kinnichiwa, etc 
///////////////////////////////////// 

function replace_mail_greeting ($content) {
  $custom_greeting = get_theme_mod('baum_greeting', 'Howdy, %s');
  return str_replace('Howdy, %s', $custom_greeting, $content);
}

add_filter('wp_mail', 'replace_mail_greeting'); 





add_filter('wp_img_tag_add_auto_sizes', '__return_false');

//
// Custom Logo Settings
//

/**
 * Adds logo customization options to the WordPress Customizer
 *
 * This function registers a new section in the WordPress Customizer
 * for logo settings, allowing users to adjust the size of logos
 * in different positions (center, default, home, footer).
 *
 * @param WP_Customize_Manager $wp_customize The WordPress customizer object
 * @return void
 *
 * @since 1.0.0
 */
function baum_customize_logo ($wp_customize) {

  //
  // Add Section for Logo Settings
  //

  $wp_customize->add_section('baum_logo_section', [
    'title'    => __('Logo Settings', 'baum'),
    'priority' => 30,
  ]);

  //
  // Add Setting for Center Logo Size
  //

  $wp_customize->add_setting('baum_center_logo_size', [
    'default'           => 40,
    'sanitize_callback' => 'absint',
    'transport'         => 'postMessage',
  ]);

  //
  // Add Control for Center Logo Size (Range Slider)
  //

  $wp_customize->add_control('baum_center_logo_size_control', [
    'label'       => __('Center Logo Size', 'baum'),
    'section'     => 'baum_logo_section',
    'settings'    => 'baum_center_logo_size',
    'type'        => 'range',
    'input_attrs' => [
      'min'  => 10,
      'max'  => 60,
      'step' => 1,
    ],
  ]);

  //
  // Add Setting for Logo Size
  //

  $wp_customize->add_setting('baum_logo_size', [
    'default'           => 40,
    'sanitize_callback' => 'absint',
    'transport'         => 'postMessage',
  ]);

  //
  // Add Control for Logo Size (Range Slider)
  //

  $wp_customize->add_control('baum_logo_size_control', [
    'label'       => __('Logo Size', 'baum'),
    'section'     => 'baum_logo_section',
    'settings'    => 'baum_logo_size',
    'type'        => 'range',
    'input_attrs' => [
      'min'  => 10,
      'max'  => 60,
      'step' => 1,
    ],
  ]);

  //
  // Add Setting for Home Logo Size
  //

  $wp_customize->add_setting('baum_home_logo_size', [
    'default'           => 40,
    'sanitize_callback' => 'absint',
    'transport'         => 'postMessage',
  ]);

  //
  // Add Control for Home Logo Size (Range Slider)
  //

  $wp_customize->add_control('baum_home_logo_size_control', [
    'label'       => __('Home Logo Size', 'baum'),
    'section'     => 'baum_logo_section',
    'settings'    => 'baum_home_logo_size',
    'type'        => 'range',
    'input_attrs' => [
      'min'  => 10,
      'max'  => 60,
      'step' => 1,
    ],
  ]);

  //
  // Add Setting for Footer Logo Size
  //

  $wp_customize->add_setting('baum_footer_logo_size', [
    'default'           => 40,
    'sanitize_callback' => 'absint',
    'transport'         => 'postMessage',
  ]);

  //
  // Add Control for Footer Logo Size (Range Slider)
  //

  $wp_customize->add_control('baum_footer_logo_size_control', [
    'label'       => __('Footer Logo Size', 'baum'),
    'section'     => 'baum_logo_section',
    'settings'    => 'baum_footer_logo_size',
    'type'        => 'range',
    'input_attrs' => [
      'min'  => 10,
      'max'  => 60,
      'step' => 1,
    ],
  ]);

}

add_action('customize_register', 'baum_customize_logo');

//
// Output the CSS for the Custom Logo Size
//

/**
 * Outputs custom CSS for logo sizes
 *
 * This function generates and outputs inline CSS to control the size
 * of logos in different positions (footer, home, center, left) based
 * on the values set in the WordPress Customizer.
 *
 * @return void Outputs inline CSS
 *
 * @since 1.0.0
 */
function baum_custom_logo_size_css () {
  ?>
  <?php //echo get_theme_mod('baum_footer_logo_size', 40); ?>
  <style>
    .baum-footer-logo img {
      height: <?php echo get_theme_mod('baum_footer_logo_size', 40); ?>px;
    }
    .navbar-home .logo img {
      height: <?php echo get_theme_mod('baum_home_logo_size', 40); ?>px;
    }
    .navbar-center .logo img {
      height: <?php echo get_theme_mod('baum_center_logo_size', 40); ?>px;
    }
    .navbar-left-logo .logo img {
      height: <?php echo get_theme_mod('baum_logo_size', 40); ?>px;
    }
  </style>
  <?php
}

add_action('wp_head', 'baum_custom_logo_size_css');

/**
 * Sets JPEG quality to maximum (100%)
 *
 * This filter ensures that all JPEG images generated by WordPress
 * are saved at maximum quality to preserve image fidelity.
 *
 * @param int $arg The current JPEG quality setting
 * @return int Always returns 100 for maximum quality
 *
 * @since 1.0.0
 */
add_filter('jpeg_quality', function ($arg) { return 100; });

/////////////////////////////////////
// Load Plugins
/////////////////////////////////////

require_once get_template_directory() . '/class-tgm-plugin-activation.php';

/**
 * Registers required and recommended plugins for the theme
 *
 * This function uses the TGM Plugin Activation library to register
 * plugins that are required or recommended for the theme to function
 * properly. It defines which plugins should be installed and their
 * installation requirements.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_load_plugins () {
  $plugins = [
    [
      'name'              => 'Advanced Custom Fields',
      'slug'              => 'advanced-custom-fields',
      'required'          => true,
      'force_activation'  => false,
    ],
    [
      'name'              => 'Classic Widgets',
      'slug'              => 'classic-widgets',
      'required'          => true,
      'force_activation'  => true,
    ],
    [
      'name'              => 'Iframely',
      'slug'              => 'iframely',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'Post Views Counter',
      'slug'              => 'post-views-counter',
      'required'          => true,
      'force_activation'  => true,
    ],
    [
      'name'              => 'Regenerate Thumbnails',
      'slug'              => 'regenerate-thumbnails',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'Sharpen Resized Images',
      'slug'              => 'sharpen-resized-images',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'Simple History',
      'slug'              => 'simple-history',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'User Menus – Nav Menu Visibility',
      'slug'              => 'user-menus',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'User Switching',
      'slug'              => 'user-switching',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'WordPress Persistent Login',
      'slug'              => 'wp-persistent-login',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'WP Crontrol',
      'slug'              => 'wp-crontrol',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'WP SmartCrop',
      'slug'              => 'wp-smartcrop',
      'required'          => false,
      'force_activation'  => false,
    ],
    // array(
    //   'name'              => 'Default Featured Image',
    //   'slug'              => 'default-featured-image',
    //   'required'          => false,
    //   'force_activation'  => false,
    // ),
    // array(
    //   'name'              => 'Email Templates Customizer',
    //   'slug'              => 'email-templates',
    //   'required'          => false,
    //   'force_activation'  => false,
    // ),
    // array(
    //   'name'              => 'Git it Write',
    //   'slug'              => 'git-it-write',
    //   'required'          => false,
    //   'force_activation'  => false,
    // ),
    // array(
    //   'name'              => 'No Category Base',
    //   'slug'              => 'no-category-base-wpml',
    //   'required'          => false,
    //   'force_activation'  => false,
    // ),
    // array(
    //   'name'              => 'Remove Dashboard Access',
    //   'slug'              => 'remove-dashboard-access-for-non-admins',
    //   'required'          => false,
    //   'force_activation'  => false,
    // ),
  ];
  $config = [
    'id'           => 'baumpress',             // Unique ID for hashing notices
    'menu'         => 'tgmpa-install-plugins', // Menu slug
    'has_notices'  => true,   // Show admin notices or not
    'dismissable'  => true,   // User can dismiss the nag message
    'dismiss_msg'  => '',     // Message at top of nag (if dismissable == false)
    'is_automatic' => false,  // Auto-activate plugins post install
    'message'      => '',     // Message right before plugins table
  ];
  tgmpa($plugins, $config);
}

add_action('tgmpa_register', 'baum_load_plugins'); // Pass plugins to TGMPA



















/**
 * Initialize BaumPress Plugin Loader
 *
 * Load the plugin loader class and initialize the plugin management system.
 * This replaces the simple plugin loading with a comprehensive management system.
 *
 * @return void
 * @since 1.0.0
 */
function baum_init_plugin_loader() {
  // Load the plugin loader class
  $plugin_loader_file = get_template_directory() . '/includes/class-baum-plugin-loader.php';

  if (file_exists($plugin_loader_file)) {
    require_once $plugin_loader_file;

    // Initialize the plugin loader
    BaumPluginLoader::get_instance();
  } else {
    error_log('BaumPress: Plugin loader class file not found: ' . $plugin_loader_file);
  }
}
add_action('after_setup_theme', 'baum_init_plugin_loader', 1);

/**
 * Temporary debug function to test plugin loader
 * Add ?test_plugin_loader=1 to any admin page to see status
 */
function baum_test_plugin_loader() {
  if (!isset($_GET['test_plugin_loader']) || !current_user_can('manage_options')) {
    return;
  }

  echo '<div style="background: #fff; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
  echo '<h3>Plugin Loader Test</h3>';

  if (class_exists('BaumPluginLoader')) {
    echo '<p style="color: green;">✅ BaumPluginLoader class loaded successfully</p>';

    $loader = BaumPluginLoader::get_instance();
    $plugins = $loader->discover_plugins();
    $active = $loader->get_active_plugins();

    echo '<p>Discovered plugins: ' . count($plugins) . '</p>';
    echo '<p>Active plugins: ' . count($active) . '</p>';
    echo '<p>Plugin loader file: ' . get_template_directory() . '/includes/class-baum-plugin-loader.php</p>';

    if (!empty($plugins)) {
      echo '<h4>Available Plugins:</h4><ul>';
      foreach ($plugins as $slug => $data) {
        $status = in_array($slug, $active) ? 'ACTIVE' : 'INACTIVE';
        echo '<li>' . $slug . ' - ' . $data['Name'] . ' [' . $status . ']</li>';
      }
      echo '</ul>';
    }
  } else {
    echo '<p style="color: red;">❌ BaumPluginLoader class not found</p>';
  }

  echo '</div>';
}
add_action('admin_notices', 'baum_test_plugin_loader');





















/**
 * Theme font choices configuration
 *
 * This array defines all available font options for the theme's heading fonts.
 * It includes system fonts and custom font files with their display names.
 *
 * @since 1.0.0
 */
$baum_uri = get_template_directory_uri();

/**
 * Array of available font choices for theme headings
 *
 * Maps font file paths to their display names for use in the theme customizer.
 * Includes system fonts and various custom font files.
 *
 * @var array $baum_font_choices Associative array of font paths and names
 * @since 1.0.0
 */
$baum_font_choices = [
  'system' => 'System Font',
  $baum_uri . '/css/font/aldrich-regular.ttf' => 'aldrich regular',
  $baum_uri . '/css/font/alexandria-variable.ttf' => 'alexandria',
  $baum_uri . '/css/font/bakbak-one-regular.ttf' => 'bakbak one regular',
  $baum_uri . '/css/font/changa-one-regular.ttf' => 'changa one regular',
  $baum_uri . '/css/font/changa-one-italic.ttf' => 'changa one italic',
  $baum_uri . '/css/font/chelsea-market-regular.ttf' => 'chelsea market regular',
  $baum_uri . '/css/font/chirp-bold.woff2' => 'chirp bold',
  $baum_uri . '/css/font/chirp-extended-heavy.woff2' => 'chirp extended heavy',
  $baum_uri . '/css/font/chirp-heavy.woff2' => 'chirp heavy',
  $baum_uri . '/css/font/chirp-medium.woff2' => 'chirp medium',
  $baum_uri . '/css/font/chirp-regular.woff2' => 'chirp regular',
  $baum_uri . '/css/font/clash-display-variable.woff2' => 'clash display',
  $baum_uri . '/css/font/clash-grotesk-variable.woff2' => 'clash grotesk',
  $baum_uri . '/css/font/days-one-regular.ttf' => 'days one regular',
  $baum_uri . '/css/font/dela-gothic-one-regular.ttf' => 'dela gothic one regular',
  $baum_uri . '/css/font/eb-garamond-variable.ttf' => 'eb garamond',
  $baum_uri . '/css/font/erbaum-bold.woff2' => 'erbaum bold',
  $baum_uri . '/css/font/erbaum-light.woff2' => 'erbaum light',
  $baum_uri . '/css/font/finger-paint-regular.ttf' => 'finger paint regular',
  $baum_uri . '/css/font/freeman-regular.ttf' => 'freeman regular',
  $baum_uri . '/css/font/germania-one-regular.ttf' => 'germania one regular',
  $baum_uri . '/css/font/goldman-regular.ttf' => 'goldman regular',
  $baum_uri . '/css/font/goldman-bold.ttf' => 'goldman bold',
  $baum_uri . '/css/font/hammersmith-one-regular.ttf' => 'hammersmith one regular',
  $baum_uri . '/css/font/hind-regular.ttf' => 'hind regular',
  $baum_uri . '/css/font/jockey-one-regular.ttf' => 'jockey one regular',
  $baum_uri . '/css/font/lalezar-regular.ttf' => 'lalezar regular',
  $baum_uri . '/css/font/mitr-regular.ttf' => 'mitr regular',
  $baum_uri . '/css/font/mitr-bold.ttf' => 'mitr bold',
  $baum_uri . '/css/font/mitr-light.ttf' => 'mitr light',
  $baum_uri . '/css/font/mitr-medium.ttf' => 'mitr medium',
  $baum_uri . '/css/font/odibee-sans-regular.ttf' => 'odibee sans regular',
  $baum_uri . '/css/font/passion-one-regular.ttf' => 'passion one regular',
  $baum_uri . '/css/font/rubik-dirt-regular.ttf' => 'rubik dirt regular',
  $baum_uri . '/css/font/russo-one-regular.ttf' => 'russo one regular',
  $baum_uri . '/css/font/secular-one-regular.ttf' => 'secular one regular',
  $baum_uri . '/css/font/signika-variable.ttf' => 'signika',
  $baum_uri . '/css/font/space-mono-bold.woff2' => 'space mono bold',
  $baum_uri . '/css/font/space-mono-regular.woff2' => 'space mono regular',
  $baum_uri . '/css/font/squada-one-regular.ttf' => 'squada one regular',
  $baum_uri . '/css/font/teko-semi-bold.ttf' => 'teko semi bold',
  $baum_uri . '/css/font/tilt-warp-regular.ttf' => 'tilt warp regular',
  $baum_uri . '/css/font/viga-regular.ttf' => 'viga',
  $baum_uri . '/css/font/work-sans-italic-variable.ttf' => 'work sans italic',
  $baum_uri . '/css/font/WorkSans-VariableFont_wght.ttf' => 'work sans',
];

/////////////////////////////////////
// Baum's Font Selection
/////////////////////////////////////

/**
 * Enqueues the selected font and outputs custom CSS for font styling
 *
 * This function gets the font selection from theme customizer settings
 * and either loads the custom font file or applies system font styling.
 * It also handles text transformation and letter spacing for headings.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_enqueue_font_selection () {
  global $baum_font_choices;
  $selected_font = get_theme_mod('baum_heading_font', 'system');
  $text_transform = get_theme_mod('baum_heading_text_transform', 'Uppercase');
  $letter_spacing = get_theme_mod('baum_heading_letter_spacing', 0);
  $font_display_name = $baum_font_choices[$selected_font]; // Value of name

  if ($selected_font === 'system') {
    error_log('$selected_font: system');

    //
    // CSS for system font
    //

    // .tag-listicle .baum-post-content li:before,
    // .baum_most_viewed_widget ol li::before,
    // .widget_post_views_counter_list_widget ol li::before,
    // .widget_categories ul li a,
    // .has-drop-cap:not(:focus):first-letter,
    // table.ics-calendar-month-grid ul.events span.title,
    // .page-title-action,
    // .ui-widget,
    // #adminmenu .wp-submenu-head,
    // #adminmenu a.menu-top,
    // .baum-widget-side-title,
    // .baum-author-box-name a,
    // input[type='submit'],
    // .btn,
    // .button,
    // button,
    // .wp-element-button,
    // label,
    // .wp-calendar-table caption,
    // #reply-title,
      // .baum-heading,

    $custom_css = "
      .wrap h1,
      .wrap h2,
      .menu-title,
      .baum-post-title,
      h1, h2, h3, h4, h5, h6 {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
        text-transform: $text_transform !important;
        letter-spacing: {$letter_spacing}em;
        font-style: normal;
        font-stretch: normal;
      }
    ";
  } else {

    // //
    // // Enqueue the selected custom font
    // //
    // wp_enqueue_style(
    //   'baum-font-selection',
    //   $selected_font,
    //   [],
    //   null,
    //   'all'
    // );

    //
    // Font-face for custom font
    //

    // .tag-listicle .baum-post-content li:before,
    // .baum_most_viewed_widget ol li::before,
    // .widget_post_views_counter_list_widget ol li::before,
    // .widget_categories ul li a,
    // .has-drop-cap:not(:focus):first-letter,
    // table.ics-calendar-month-grid ul.events span.title,
    // .page-title-action,
    // .ui-widget,
    // #adminmenu .wp-submenu-head,
    // #adminmenu a.menu-top,
    // label,
    // .wp-calendar-table caption,
    // .menu-title,
    // #reply-title,
    // .baum-widget-side-title,
    // .baum-author-box-name a,
    // input[type='submit'],
    // .btn,
    // .button,
    // button,
    // .wp-element-button,
    // h2, h3, h4, h5, h6
      // .baum-heading,

    $custom_css = "
      @font-face {
        font-family: '$font_display_name';
        src: url('$selected_font') format('truetype');
      }
      .wrap h1,
      .wrap h2,
      .baum-post-title,
      h1 {
        font-family: '$font_display_name', sans-serif;
        text-transform: $text_transform !important;
        letter-spacing: {$letter_spacing}em;
        font-display: auto;
        font-style: normal;
        font-weight: bold;
        font-stretch: normal;
      }
    ";
  }
  wp_register_style('baum-inline-font-selection', false);
  wp_enqueue_style('baum-inline-font-selection');
  wp_add_inline_style('baum-inline-font-selection', $custom_css);
}

add_action('wp_enqueue_scripts', 'baum_enqueue_font_selection');

/**
 * Gets the path to the selected heading font
 *
 * This function retrieves the path to the font file selected in the theme
 * customizer for headings. If no font is selected or the system font is
 * chosen, it returns the path to the default Noto Sans font.
 *
 * @return string Path to the font file
 *
 * @since 1.0.0
 */
function baum_get_heading_font_path() {
  global $baum_font_choices;

  // Get the selected font from theme mods
  $selected_font = get_theme_mod('baum_heading_font', 'system');

  // Define the default font path
  $default_font_path = $baum_uri . '/css/font/NotoSans-VariableFont_wdth,wght.ttf';

  // If the selected font is empty or 'system', return the default font path
  if (empty($selected_font) || $selected_font === 'system') {
      return $default_font_path;
  }

  return $selected_font;
}

/////////////////////////////////////
// Customizer Additions
/////////////////////////////////////

require get_template_directory() . '/inc/customizer.php';

//
//
//

// echo get_theme_color('red'); // Returns hex,  #9c0404
// echo get_theme_color('primary', 'rgb'); // Returns RGB, "188, 12, 12"
// echo get_theme_color('secondary', 'rgba'); // Returns RGBA, "0, 0, 0, 1.0"

//
//
//

/**
 * Gets a theme color in the specified format
 *
 * This function retrieves colors from the theme's color configuration based on
 * the selected theme and dark mode settings. It supports multiple output formats
 * and automatically handles light/dark mode switching.
 *
 * @param string $color The color name to retrieve (e.g., 'primary', 'secondary')
 * @param string $format The output format: 'hex', 'rgb', or 'rgba'
 * @return string|null The color value in the requested format, or null if not found
 *
 * @since 1.0.0
 */
function get_theme_color ($color, $format = 'hex') {
  // Get the selected theme and dark mode setting
  $theme = get_theme_mod('baum_custom_theme_selector', 'default');
  $dark_mode = get_theme_mod('baum_darkmode', 'auto'); // 'auto', 'on', or 'off'

  // Include the themes configuration
  $color_themes = include get_stylesheet_directory() . '/inc/baum-color-themes.php';

  // Get the theme data, fallback to 'default' theme
  $theme_data = $color_themes['themes'][$theme] ?? $color_themes['themes']['default'];

  // Determine whether to use light or dark colors
  $mode = ($dark_mode === 'on' || ($dark_mode === 'auto' && isset($_COOKIE['dark_mode']) && $_COOKIE['dark_mode'] === '1')) ? 'dark' : 'light';

  // Retrieve the color value
  $color_value = $theme_data[$mode][$color] ?? $theme_data[$mode]['color'][$color] ?? null;

  // If the color is not found, return null
  if (!$color_value) {
      return null;
  }

  // Convert to the requested format
  switch ($format) {
      case 'rgba':
          return convert_hex_to_rgba($color_value);
      case 'rgb':
          return convert_hex_to_rgb($color_value);
      case 'hex':
      default:
          return $color_value;
  }
}

/**
 * Converts a hex color to RGBA format
 *
 * This helper function takes a hex color value and converts it to RGBA format
 * with the specified alpha transparency value.
 *
 * @param string $hex The hex color value (with or without #)
 * @param float $alpha The alpha transparency value (0.0 to 1.0)
 * @return string The RGBA color string
 *
 * @since 1.0.0
 */
function convert_hex_to_rgba ($hex, $alpha = 1.0) {
  $rgb = convert_hex_to_rgb($hex);
  return "rgba({$rgb}, {$alpha})";
}

/**
 * Converts a hex color to RGB format
 *
 * This helper function takes a hex color value and converts it to RGB format.
 * It handles both 3-digit and 6-digit hex values.
 *
 * @param string $hex The hex color value (with or without #)
 * @return string The RGB color values as comma-separated string
 *
 * @since 1.0.0
 */
function convert_hex_to_rgb ($hex) {
  $hex = ltrim($hex, '#');

  if (strlen($hex) === 3) {
    $hex = "{$hex[0]}{$hex[0]}{$hex[1]}{$hex[1]}{$hex[2]}{$hex[2]}";
  }

  $r = hexdec(substr($hex, 0, 2));
  $g = hexdec(substr($hex, 2, 2));
  $b = hexdec(substr($hex, 4, 2));

  return "{$r}, {$g}, {$b}";
}


add_action('after_setup_theme', 'baumpress_load_textdomain');

function baumpress_load_textdomain() {
    load_theme_textdomain(
        'baum', 
        get_template_directory() . '/languages/baum-' . determine_locale() . '.json'
    );
}


/////////////////////////////////////
// Baum Press Theme Setup
/////////////////////////////////////

/**
 * Sets up theme defaults and registers support for various WordPress features
 *
 * This function is hooked into the 'after_setup_theme' action and is used to
 * initialize various theme features, register support for WordPress functionality,
 * and set up theme defaults like menus and sidebars.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_setup () {

  //
  // Enable Comments on Media File Attachment Pages
  //

  add_post_type_support('attachment', 'comments');
  add_theme_support('editor-styles');
  // add_theme_support('post-thumbnails'); // Disabled - using Baum Featured Images system
  add_theme_support('responsive-embeds');
  add_theme_support('title-tag');
  add_theme_support('html5', [
    'comment-list',
    'comment-form',
    'search-form',
    'gallery',
    'caption',
    'style',
    'script'
  ]);
  add_theme_support('dark-editor-style');
  add_theme_support('menus');
  add_theme_support('editor-styles'); // Styles in Gutenberg

  set_post_thumbnail_size(1000, 1000, true);

  add_image_size('baum-headline-thumb', 1100, 500, true);
  add_image_size('baum-full-thumb', 1100, 500, true);
  add_image_size('baum-full-mobile-thumb', 382, 325, true);
  add_image_size('baum-wide-thumb', 730, 340, true);
  add_image_size('baum-xlarge-thumb', 540, 300, true);
  add_image_size('baum-large-thumb', 357, 238, true);
  add_image_size('baum-medium-thumb', 265, 183, true);
  add_image_size('baum-square-thumb', 175, 175, true);
  add_image_size('baum-square-gradient-thumb', 360, 360, true);
  add_image_size('baum-small-thumb', 128, 128, true);
  add_image_size('baum-small-cover-thumb', 360, 175, true);
  add_image_size('baum-book-thumb', 165, 250, true);
  add_image_size('baum-mini-thumb', 64, 64, true);

  add_image_size('baum-facebook-thumb', 1200, 630, true);
  add_image_size('baum-x-thumb', 1600, 900, true);
  add_image_size('baum-instagram-thumb', 1080, 1080, true);
  add_image_size('baum-linkedin-thumb', 1200, 630, true);

  add_image_size('baum-ico-thumb', 12, 12, true);
  add_image_size('baum-32-thumb', 32, 32, true);
  add_image_size('baum-48-thumb', 48, 48, true);
  add_image_size('baum-64-thumb', 64, 64, true);
  add_image_size('baum-128-thumb', 128, 128, true);
  add_image_size('baum-256-thumb', 256, 256, true);
  add_image_size('baum-512-thumb', 512, 512, true);
  add_image_size('baum-1024-thumb', 1024, 1024, true);

  add_image_size('baum-16-9-thumb', 1100, 619, true);
  add_image_size('baum-5-3-thumb', 1100, 660, true);
  add_image_size('baum-3-2-thumb', 1100, 733, true);
  add_image_size('baum-4-3-thumb', 1100, 825, true);
  add_image_size('baum-5-4-thumb', 1100, 880, true);
  add_image_size('baum-1-1-thumb', 1000, 1000, true);
  add_image_size('baum-4-5-thumb', 880, 1100, true);
  add_image_size('baum-3-4-thumb', 825, 1100, true);
  add_image_size('baum-2-3-thumb', 733, 1100, true);
  add_image_size('baum-3-5-thumb', 660, 1100, true);
  add_image_size('baum-9-16-thumb', 619, 1100, true);

  // Rendition-specific thumbnail sizes for slim/slender images
  add_image_size('baum-rendition-slim-tall', 400, 600, true);      // 2:3 ratio
  add_image_size('baum-rendition-very-tall', 300, 533, true);     // 9:16 ratio
  add_image_size('baum-rendition-slim-wide', 600, 400, true);     // 3:2 ratio
  add_image_size('baum-rendition-very-wide', 700, 300, true);     // 21:9 ratio
  add_image_size('baum-rendition-strip-v', 200, 600, true);       // Vertical strips
  add_image_size('baum-rendition-strip-h', 600, 200, true);       // Horizontal strips
  add_image_size('baum-rendition-ultra-wide', 800, 200, true);    // Ultra-wide strips
  add_image_size('baum-rendition-ultra-tall', 200, 800, true);    // Ultra-tall strips
  add_image_size('baum-rendition-3-1-wide', 600, 200, true);     // 3:1 ratio for rendition-6
  add_image_size('baum-rendition-tall-slim', 300, 450, true);    // Extra tall for rendition-5

  // The recommended size for Open Graph (OG) images on Facebook is
  // 1200 x 630 pixels, adhering to an aspect ratio of approximately
  // 1.91:1. This ensures that images display optimally across both
  // desktop and mobile devices, providing a clear and engaging visual
  // when your content is shared.
  add_image_size('baum-social-thumb', 1200, 630, true);


  // add_theme_support('yoast-seo-breadcrumbs');
  // add_theme_support('post-formats', ['link', 'gallery', 'video']);
  // Add support for various features
  // add_theme_support('custom-logo');
  // add_theme_support('custom-header');
  // add_theme_support('custom-background');
  // add_theme_support('post-thumbnails'); // Featured images
  // add_theme_support('title-tag'); // Dynamic title tags
  // add_theme_support('automatic-feed-links'); // RSS feed links in head
  // add_theme_support('html5', ['search-form', 'comment-form', 'comment-list', 'gallery', 'caption']); // HTML5 support for specific elements
  // add_theme_support('align-wide'); // Wide alignment option for Gutenberg blocks
  // // add_theme_support('admin-bar');
  // add_theme_support('align-wide');
  // add_theme_support('appearance-tools');
  // // add_theme_support('automatic-feed-links');
  // add_theme_support('block-templates');
  // add_theme_support('block-template-parts');
  // add_theme_support('border');
  // add_theme_support('core-block-patterns');
  // // add_theme_support('custom-background');
  // add_theme_support('custom-header');
  // add_theme_support('custom-line-height');
  // add_theme_support('custom-logo', array(
  //   'height'               => 50,
  //   'width'                => 200,
  //   'flex-height'          => true,
  //   'flex-width'           => true,
  //   'header-text'          => array( 'site-title', 'site-description' ),
  //   'unlink-homepage-logo' => true,
  //   ));
  // add_theme_support('customize-selective-refresh-widgets');
  // add_theme_support('custom-spacing');
  // add_theme_support('custom-units');
  // add_theme_support('dark-editor-style');
  // add_theme_support('disable-custom-colors');
  // add_theme_support('disable-custom-font-sizes');
  // add_theme_support('disable-custom-gradients');
  // add_theme_support('disable-layout-styles');
  // add_theme_support('editor-color-palette');
  // add_theme_support('editor-gradient-presets');
  // // add_theme_support('editor-font-sizes');
  // add_theme_support('editor-spacing-sizes');
  // add_theme_support('featured-content');
  // add_theme_support('html5', [
  //   'comment-list',
  //   'comment-form',
  //   'search-form',
  //   'gallery',
  //   'caption',
  //   'style',
  //   'script'
  // ]);
  // add_theme_support('link-color');
  // add_theme_support('menus');
  // add_theme_support('post-formats');
  // add_theme_support('post-thumbnails');
  // add_theme_support('responsive-embeds');
  // add_theme_support('starter-content');
  // add_theme_support('title-tag');
  // add_theme_support('widgets');
  // add_theme_support('widgets-block-editor');
  // add_theme_support('wp-block-styles');
}

add_action('after_setup_theme', 'baum_setup');

/**
 * Enqueues additional scripts for BaumPress functionality
 *
 * This function is currently used for future script enqueuing and contains
 * commented code for voting functionality that can be enabled as needed.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baumpress_enqueue_scripts() {
  // wp_enqueue_script('baumpress-vote', get_template_directory_uri() . '/js/baumpress-vote.js', array('jquery'), null, true);
  // wp_localize_script('baumpress-vote', 'baumpress_ajax_url', admin_url('admin-ajax.php'));
}

add_action('wp_enqueue_scripts', 'baumpress_enqueue_scripts');

/**
 * Enqueues lightweight frontend editor assets
 *
 * This function loads a lightweight rich text editor for the frontend
 * that's much more resource-efficient than full Gutenberg.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_enqueue_gutenberg_frontend() {
  // Only load on style guide page and for users with edit_posts capability
  if (!is_page('style-guide') || !current_user_can('edit_posts')) {
    return;
  }

  // Enqueue our lightweight frontend editor script (no heavy dependencies)
  wp_enqueue_script(
    'baum-frontend-gutenberg',
    get_template_directory_uri() . '/js/frontend-gutenberg.js',
    ['jquery'], // Only jQuery dependency
    wp_get_theme()->get('Version'),
    true
  );

  // Localize script with necessary data
  wp_localize_script('baum-frontend-gutenberg', 'baumGutenberg', [
    'apiUrl' => home_url('/wp-json/wp/v2/'),
    'nonce' => wp_create_nonce('wp_rest'),
    'currentUser' => get_current_user_id(),
    'canPublish' => current_user_can('publish_posts'),
    'restNonce' => wp_create_nonce('wp_rest'),
    'allowedBlocks' => baum_get_allowed_frontend_blocks(),
  ]);
}
add_action('wp_enqueue_scripts', 'baum_enqueue_gutenberg_frontend');

/**
 * Get allowed blocks for frontend Gutenberg editor
 *
 * This function returns an array of block types that are allowed
 * in the frontend editor. This provides control over which blocks
 * users can use when creating content from the frontend.
 *
 * @return array Array of allowed block type names
 *
 * @since 1.0.0
 */
function baum_get_allowed_frontend_blocks() {
  $allowed_blocks = [
    // Text blocks
    'core/paragraph',
    'core/heading',
    'core/list',
    'core/quote',
    'core/pullquote',
    'core/verse',
    'core/preformatted',

    // Media blocks
    'core/image',
    'core/gallery',
    'core/video',
    'core/audio',
    'core/file',

    // Design blocks
    'core/separator',
    'core/spacer',
    'core/columns',
    'core/column',
    'core/group',
    'core/cover',

    // Widgets
    'core/shortcode',
    'core/html',
    'core/code',
    'core/table',

    // Embeds
    'core/embed',
    'core-embed/youtube',
    'core-embed/twitter',
    'core-embed/instagram',
    'core-embed/vimeo',
    'core-embed/spotify',

    // Theme blocks (if any custom blocks exist)
    'baum/cards',
    'baum/inline-embed',
  ];

  // Allow filtering of allowed blocks
  return apply_filters('baum_frontend_allowed_blocks', $allowed_blocks);
}


// //
// // Setup Activity for Bomber Timelines
// //
// function register_activity_post_type () {

//   //
//   // Register the Activity Post Type
//   //
//   register_post_type('activity', [
//     'labels' => [
//       'name' => 'Activities',
//       'singular_name' => 'Activity',
//     ],
//     'public' => true,
//     'hierarchical' => false,
//     'has_archive' => true,
//     'supports' => [
//       'title', 'editor', 'comments', 'author', 'thumbnail', 'custom-fields'
//     ],
//     'rewrite' => ['slug' => 'activity'],
//   ]);

//   //
//   // Register the Content Type Taxonomy
//   //
//   register_taxonomy('content_type', 'activity', [
//     'labels' => [
//       'name' => 'Content Types',
//       'singular_name' => 'Content Type',
//     ],
//     'public' => true,
//     'hierarchical' => true,
//     'rewrite' => ['slug' => 'content-type'],
//   ]);
// }

// add_action('init', 'register_activity_post_type');

// //
// // Add terms like video, image, live_video, and life_event
// // to the content_type taxonomy
// //
// function add_activity_content_types () {
//   $content_types = ['live_video', 'image', 'video', 'life_event' ];
//   foreach ($content_types as $type) {
//     if (!term_exists($type, 'content_type')) {
//       wp_insert_term($type, 'content_type');
//     }
//   }
// }

// add_action('init', 'add_activity_content_types');

/**
 * Uses custom multiple featured images as the post thumbnail
 *
 * This filter intercepts requests for the post thumbnail ID and returns
 * the first image from the 'multiple_featured_images' custom field instead.
 * This allows posts to have multiple featured images while maintaining
 * compatibility with WordPress's featured image system.
 *
 * @param mixed $value The current meta value
 * @param int $object_id The post ID
 * @param string $meta_key The meta key being requested
 * @param bool $single Whether to return a single value
 * @return mixed The first image ID from multiple_featured_images or original value
 *
 * @since 1.0.0
 */
// Disabled - now handled by Baum Featured Images system
// add_filter('get_post_metadata', function ($value, $object_id, $meta_key, $single) {
//   if ($meta_key === '_thumbnail_id') {
//     // Replace this with your custom logic
//     $image_ids = get_post_meta($object_id, 'multiple_featured_images', true);

//     if (is_array($image_ids) && !empty($image_ids)) {
//       return $image_ids[0]; // Use the first image as the "featured image"
//     }

//     return null; // No thumbnail available
//   }

//   return $value; // Default behavior for other meta keys
// }, 10, 4);


//   function get_post_thumbnail_id($post_id = null) {
//       $post_id = $post_id ?: get_the_ID();
//       $image_ids = get_post_meta($post_id, 'multiple_featured_images', true);

//       // Return a random image if available
//       if (is_array($image_ids) && !empty($image_ids)) {
//           return $image_ids[array_rand($image_ids)];
//       }

//       // Fallback to a default image (optional)
//       // $default_image_id = 123; // Replace with your default image ID
//       return null; // $default_image_id;
//   }

// //
// // Override Featured Image with Random Image
// //
// function load_random_featured_image($html, $post_id) {
//   $image_ids = get_post_meta($post_id, 'multiple_featured_images', true);
//   if ($image_ids && is_array($image_ids)) {
//       $random_image_id = $image_ids[array_rand($image_ids)];
//       $html = wp_get_attachment_image($random_image_id, 'full', false, ['style' => 'border-radius: 8px;']);
//   }
//   return $html;
// }
// add_filter('post_thumbnail_html', 'load_random_featured_image', 10, 2);

/**
 * Loads JSON data from the theme's data directory
 *
 * This function loads and decodes JSON files from the theme's `/data/` folder.
 * It provides a centralized way to access structured data files for the theme.
 *
 * @param string $filename The name of the JSON file (without extension)
 * @return array The decoded JSON data or empty array if file doesn't exist
 *
 * @since 1.0.0
 *
 * @example load_json_data('phones') // Loads data/phones.json
 */
function load_json_data ($filename) {
  // Sanitize filename to prevent directory traversal
  $filename = sanitize_file_name($filename);

  // Path to the JSON file
  $json_file = get_template_directory() . "/data/" . $filename . ".json";

  if (file_exists($json_file) && is_readable($json_file)) { // Check if the file exists and is readable
    $json_content = file_get_contents($json_file);
    $json_data = json_decode($json_content, true);

    // Return data if valid JSON, otherwise empty array
    return (json_last_error() === JSON_ERROR_NONE) ? $json_data : [];
  }
  return []; // Return empty array if file is missing or unreadable
}

/**
 * Filters the wp_die handler to use a custom error page
 *
 * This filter replaces the default WordPress error handler with a custom
 * one that provides better styling and user experience for error pages.
 *
 * @param callable $handler The current wp_die handler
 * @return string The custom handler function name
 *
 * @since 1.0.0
 */
add_filter('wp_die_handler', function ($handler) {
	// return ! is_admin() ? 'baum_wp_die_handler' : $handler;
	return 'baum_wp_die_handler';
}, 10);

/**
 * Custom wp_die handler for better error display
 *
 * This function provides a custom error handler that displays errors
 * using the theme's styling and layout instead of the default WordPress
 * error page. It handles both WP_Error objects and string messages.
 *
 * @param string|WP_Error $message The error message or WP_Error object
 * @param string $title The error title
 * @param array $args Additional arguments for the error handler
 * @return void This function terminates execution
 *
 * @since 1.0.0
 */
function baum_wp_die_handler ($message, $title = '', $args = []) {
	$defaults = [ 'response' => 500 ];
	$r = wp_parse_args($args, $defaults);

	if (function_exists('is_wp_error') && is_wp_error($message)) {
		$errors = $message->get_error_messages();
		switch (count($errors)) {
			case 0 :
				$message = '';
				break;
			case 1 :
				$message = $errors[0];
				break;
			default :
				$message = "<ul>\n\t\t<li>"
          . join("</li>\n\t\t<li>", $errors)
          . "</li>\n\t</ul>";
				break;
		}
	} else {
		$message = strip_tags($message);
	}

	require_once get_template_directory() . '/wp-die.php';
	die();
}

// add_filter('wp_robots', function ($ret) {
//   global $pagenow;
//   if ('wp-comments-post.php' !== $pagenow) return $ret;
//   $url = get_template_directory_uri() . '/style.css';
//   echo '<link rel="stylesheet" href="' . $url . '">';
//   return $ret;
// });

/**
 * Extends WordPress upload MIME types to support additional file formats
 *
 * This function adds support for a wide variety of file types including
 * text files, programming files, documents, images, audio, video, 3D models,
 * CAD files, e-books, GIS files, and archives. It also removes potentially
 * harmful executable file types for security.
 *
 * @param array $mimes The existing array of allowed MIME types
 * @return array The modified array with additional MIME types
 *
 * @since 1.0.0
 */
function baum_upload_mimes ($mimes) {

  //
  // Text and Logs
  //

  $mimes['txt'] = 'text/plain'; // Plain text
  $mimes['log'] = 'text/plain'; // Log files
  $mimes['md'] = 'text/markdown'; // Markdown
  $mimes['markdown'] = 'text/markdown'; // Markdown (alternative)
  // $mimes['csv'] = 'text/csv'; // CSV files
  $mimes['json'] = 'application/json'; // JSON files
  $mimes['xml'] = 'application/xml'; // XML files
  $mimes['yaml'] = 'application/x-yaml'; // YAML files
  $mimes['yml'] = 'application/x-yaml'; // YAML (alternative)
  $mimes['graphql'] = 'application/graphql'; // GraphQL schema
  $mimes['sql'] = 'application/sql'; // SQL files

  //
  // Programming Files
  //

  $mimes['html'] = 'text/html'; // HTML
  $mimes['htm'] = 'text/html'; // HTML (alternative)
  $mimes['css'] = 'text/css'; // CSS
  $mimes['php'] = 'application/x-httpd-php'; // PHP
  $mimes['sql'] = 'application/sql'; // SQL (repeated)
  $mimes['js'] = 'application/javascript'; // JavaScript
  $mimes['ts'] = 'application/typescript'; // TypeScript
  $mimes['sh'] = 'application/x-sh'; // Shell script
  $mimes['bash'] = 'application/x-sh'; // Bash script
  $mimes['java'] = 'text/x-java-source'; // Java
  $mimes['c'] = 'text/x-c'; // C
  $mimes['cpp'] = 'text/x-c++src'; // C++
  $mimes['py'] = 'text/x-python'; // Python

  //
  // Spreadsheet / Tabular Files
  //

  $mimes['xls'] = 'application/vnd.ms-excel'; // Excel 97-2003
  $mimes['xlsx'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; // Excel Modern
  $mimes['ods'] = 'application/vnd.oasis.opendocument.spreadsheet'; // OD Sheet
  $mimes['numbers'] = 'application/x-iwork-numbers-sffnumbers'; // Apple Numbers
  $mimes['csv'] = 'text/csv'; // CSV files (repeated from text)

  //
  // Document Files
  //

  $mimes['doc'] = 'application/msword'; // Word 97-2003
  $mimes['docx'] = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'; // Word Modern
  $mimes['odt'] = 'application/vnd.oasis.opendocument.text'; // Open Doc Text
  $mimes['rtf'] = 'application/rtf'; // Rich Text Format
  $mimes['pdf'] = 'application/pdf'; // PDF
  $mimes['pages'] = 'application/x-iwork-pages-sffpages'; // Apple Pages

  //
  // Podium Presentation Files
  //

  $mimes['ppt'] = 'application/vnd.ms-powerpoint'; // PowerPoint 97-2003
  $mimes['pptx'] = 'application/vnd.openxmlformats-officedocument.presentationml.presentation'; // PowerPoint Modern
  $mimes['key'] = 'application/x-iwork-keynote-sffkey'; // Apple Keynote
  $mimes['odp'] = 'application/vnd.oasis.opendocument.presentation';
    // OpenDocument Presentation

  //
  // Image Files
  //

  $mimes['jpg'] = 'image/jpeg'; // JPEG
  $mimes['jpeg'] = 'image/jpeg'; // JPEG (alternative)
  $mimes['png'] = 'image/png'; // PNG
  $mimes['gif'] = 'image/gif'; // GIF
  $mimes['bmp'] = 'image/bmp'; // BMP
  $mimes['svg'] = 'image/svg+xml'; // SVG
  $mimes['webp'] = 'image/webp'; // WebP

  //
  // Adobe Creative Suite File Types
  //

  $mimes['psd'] = 'image/vnd.adobe.photoshop'; // Adobe Photoshop
  $mimes['ai'] = 'application/postscript'; // Adobe Illustrator
  $mimes['indd'] = 'application/x-indesign'; // Adobe InDesign
  $mimes['xd'] = 'application/vnd.adobe.xd'; // Adobe XD
  $mimes['pdf'] = 'application/pdf'; // Adobe Acrobat
  $mimes['ae'] = 'application/octet-stream'; // After Effects
  $mimes['prproj'] = 'application/octet-stream'; // Premiere Pro Project
  $mimes['enc'] = 'application/octet-stream'; // Adobe Encore Project
  $mimes['otf'] = 'application/x-font-otf'; // Adobe Fonts (OpenType)
  $mimes['ttf'] = 'application/x-font-ttf'; // Adobe Fonts (TrueType)
  $mimes['dng'] = 'image/x-adobe-dng'; // Adobe Digital Negative
  $mimes['abr'] = 'application/octet-stream'; // Photoshop Brushes
  $mimes['asl'] = 'application/octet-stream'; // Photoshop Layer Styles
  $mimes['aep'] = 'application/octet-stream'; // After Effects Project
  $mimes['mogr'] = 'application/octet-stream'; // Motion Graphics Template
  $mimes['lrcat'] = 'application/octet-stream'; // Lightroom Catalog
  $mimes['lrtemplate'] = 'application/octet-stream'; // Lightroom Preset
  $mimes['swf'] = 'application/x-shockwave-flash'; // Flash (SWF)

  //
  // 3D Printing and Modeling Files
  //

  $mimes['stl'] = 'application/sla'; // Stereolithography (3D printing)
  $mimes['obj'] = 'application/octet-stream'; // Wavefront OBJ (3D model)
  $mimes['ply'] = 'application/octet-stream'; // Polygon File Format
  $mimes['fbx'] = 'application/octet-stream'; // Autodesk FBX
  $mimes['glb'] = 'model/gltf-binary'; // GL Transmission Format (binary)
  $mimes['gltf'] = 'model/gltf+json'; // GL Transmission Format (JSON)
  $mimes['3ds'] = 'application/x-3ds'; // 3D Studio Mesh
  $mimes['blend'] = 'application/x-blender'; // Blender file
  $mimes['dae'] = 'model/vnd.collada+xml'; // COLLADA
  $mimes['x3d'] = 'model/x3d+xml'; // X3D
  $mimes['vrml'] = 'model/vrml'; // Virtual Reality Modeling Language
  $mimes['iges'] = 'model/iges'; // IGES
  $mimes['step'] = 'model/step'; // STEP
  $mimes['prt'] = 'application/octet-stream'; // NX/Creo Part File
  $mimes['3mf'] = 'application/vnd.ms-package.3dmanufacturing-3dmodel+xml';
    // 3MF (3D Manufacturing Format)

  //
  // CAD Files
  //

  $mimes['dwg'] = 'application/acad'; // AutoCAD Drawing
  $mimes['dxf'] = 'application/dxf'; // AutoCAD DXF
  $mimes['igs'] = 'model/iges'; // IGES (Initial Graphics Exchange)
  $mimes['stp'] = 'model/step'; // STEP (Standard Exchange of Product Data)
  $mimes['catpart'] = 'application/octet-stream'; // CATIA Part
  $mimes['catdrawing'] = 'application/octet-stream'; // CATIA Drawing
  $mimes['sat'] = 'application/octet-stream'; // ACIS File
  $mimes['sldprt'] = 'application/octet-stream'; // SolidWorks Part
  $mimes['sldasm'] = 'application/octet-stream'; // SolidWorks Assembly
  $mimes['ipt'] = 'application/octet-stream'; // Autodesk Inventor Part
  $mimes['iam'] = 'application/octet-stream'; // Autodesk Inventor Assembly
  $mimes['asm'] = 'application/octet-stream'; // Pro/ENGINEER Assembly
  $mimes['prt'] = 'application/octet-stream'; // Pro/ENGINEER Part

  //
  // Video Files
  //

  $mimes['mp4'] = 'video/mp4'; // MP4
  $mimes['m4v'] = 'video/x-m4v'; // M4V
  $mimes['mov'] = 'video/quicktime'; // QuickTime
  $mimes['wmv'] = 'video/x-ms-wmv'; // Windows Media Video
  $mimes['avi'] = 'video/x-msvideo'; // AVI
  $mimes['webm'] = 'video/webm'; // WebM
  $mimes['mkv'] = 'video/x-matroska'; // Matroska

  //
  // Video Production Files
  //

  $mimes['prproj'] = 'application/octet-stream'; // Premiere Pro Project
  $mimes['ae'] = 'application/octet-stream'; // After Effects Project
  $mimes['aep'] = 'application/octet-stream'; // After Effects Project (alt.)
  $mimes['dav'] = 'video/dav'; // DAV (DVR Video)
  $mimes['ogv'] = 'video/ogg'; // OGG Video
  $mimes['mts'] = 'video/MP2T'; // MPEG Transport Stream
  $mimes['drp'] = 'application/octet-stream'; // DaVinci Resolve Project
  $mimes['mgp'] = 'application/octet-stream'; // Magic Bullet Suite Project

  //
  // Audio Files
  //

  $mimes['mp3'] = 'audio/mpeg'; // MP3
  $mimes['wav'] = 'audio/wav'; // WAV
  $mimes['ogg'] = 'audio/ogg'; // OGG
  $mimes['m4a'] = 'audio/x-m4a'; // M4A
  $mimes['flac'] = 'audio/x-flac'; // FLAC

  //
  // Audio Production Files
  //

  $mimes['aif'] = 'audio/x-aiff'; // Audio Interchange File Format (AIFF)
  $mimes['aiff'] = 'audio/x-aiff'; // AIFF (alternative)
  $mimes['als'] = 'application/octet-stream'; // Ableton Live Set
  $mimes['flp'] = 'application/octet-stream'; // FL Studio Project
  $mimes['logicx'] = 'application/octet-stream'; // Logic Pro Project
  $mimes['cubase'] = 'application/octet-stream'; // Cubase Project
  $mimes['midi'] = 'audio/midi'; // MIDI File
  $mimes['mid'] = 'audio/midi'; // MIDI File (alternative)
  $mimes['omf'] = 'application/octet-stream'; // Open Media Framework (A/V)
  $mimes['audacity'] = 'application/octet-stream'; // Audacity Project

  //
  // E-Book Files
  //

  $mimes['epub'] = 'application/epub+zip'; // EPUB (Open standard)
  $mimes['mobi'] = 'application/x-mobipocket-ebook'; // Mobipocket (Kindle)
  $mimes['azw'] = 'application/vnd.amazon.ebook'; // Amazon Kindle AZW
  $mimes['azw3'] = 'application/vnd.amazon.mobi8-ebook'; // Amazon Kindle AZW3
  $mimes['pdf'] = 'application/pdf'; // PDF (Portable Document Format)
  $mimes['ibooks'] = 'application/x-ibooks+zip'; // Apple iBooks
  $mimes['lit'] = 'application/x-ms-reader'; // Microsoft Reader
  $mimes['cbr'] = 'application/x-cbr'; // Comic Book Archive (RAR-based)
  $mimes['cbz'] = 'application/x-cbz'; // Comic Book Archive (ZIP-based)

  //
  // GIS Files
  //

  $mimes['shp'] = 'application/octet-stream'; // ESRI Shapefile
  $mimes['dbf'] = 'application/octet-stream'; // Shapefile Attribute Database
  $mimes['geojson'] = 'application/geo+json'; // GeoJSON
  $mimes['kml'] = 'application/vnd.google-earth.kml+xml'; // Keyhole ML
  $mimes['kmz'] = 'application/vnd.google-earth.kmz'; // Keyhole ML (compressed)
  $mimes['gpx'] = 'application/gpx+xml'; // GPS Exchange Format
  $mimes['grib'] = 'application/octet-stream'; // GRIB (Weather Data)
  $mimes['tif'] = 'image/tiff'; // GeoTIFF
  $mimes['tiff'] = 'image/tiff'; // GeoTIFF (alternative)
  $mimes['qgs'] = 'application/octet-stream'; // QGIS Project File
  $mimes['osm'] = 'application/octet-stream'; // OpenStreetMap Data
  $mimes['asc'] = 'application/octet-stream'; // ASCII Grid
  $mimes['xyz'] = 'application/octet-stream'; // XYZ (Point Cloud)

  //
  // Archive Files
  //

  $mimes['zip'] = 'application/zip'; // ZIP
  $mimes['rar'] = 'application/x-rar-compressed'; // RAR
  $mimes['7z'] = 'application/x-7z-compressed'; // 7-Zip
  $mimes['tar'] = 'application/x-tar'; // TAR
  $mimes['gz'] = 'application/gzip'; // GZip

  //
  // Remove potentially harmful types (e.g., executables)
  //

  unset($mimes['exe']);
  unset($mimes['bat']);
  unset($mimes['bin']);
  return $mimes;
}

add_filter('upload_mimes', 'baum_upload_mimes');

/////////////////////////////////////
// Enqueue Assets
/////////////////////////////////////

/**
 * Enqueues scripts and styles for the front-end
 *
 * This function loads various CSS and JavaScript files needed for the theme,
 * including Font Awesome, jQuery plugins, syntax highlighting, and custom
 * theme styles. It also handles localization of scripts for AJAX functionality.
 *
 * @return void
 *
 * @since 1.0.0
 */

// Enqueue attachments gallery assets
function baum_enqueue_attachments_assets() {
  if (is_page_template('page-attachments.php')) {
    wp_enqueue_style('baum-attachments', get_template_directory_uri() . '/style-attachments.css', [], '1.0.0');
    wp_enqueue_script('baum-attachments', get_template_directory_uri() . '/js/baum-attachments.js', ['jquery'], '1.0.0', true);

    // Localize script for AJAX
    wp_localize_script('baum-attachments', 'baumAttachments', [
      'ajaxUrl' => admin_url('admin-ajax.php'),
      'nonce' => wp_create_nonce('baum_attachments_nonce')
    ]);
  }
}
add_action('wp_enqueue_scripts', 'baum_enqueue_attachments_assets');

// AJAX handler for loading more attachments
function baum_load_more_attachments() {
  check_ajax_referer('baum_attachments_nonce', 'nonce');

  $paged = intval($_GET['page'] ?? 1);
  $posts_per_page = 24;

  // Check if attachment_tag taxonomy exists
  $attachment_taxonomies = get_object_taxonomies('attachment');

  $query_args = [
    'post_type' => 'attachment',
    'post_status' => 'inherit',
    'posts_per_page' => $posts_per_page,
    'paged' => $paged,
    'orderby' => 'date',
    'order' => 'DESC'
  ];

  // Only add tax_query if attachment_tag taxonomy exists
  if (in_array('attachment_tag', $attachment_taxonomies)) {
    $query_args['tax_query'] = [
      'relation' => 'AND',
      [
        'taxonomy' => 'attachment_tag',
        'field' => 'slug',
        'terms' => ['gallery-exclude', 'private', 'no-public'],
        'operator' => 'NOT IN'
      ]
    ];
  }

  $attachment_query = new WP_Query($query_args);

  if ($attachment_query->have_posts()) {
    while ($attachment_query->have_posts()) {
      $attachment_query->the_post();
      // Include the attachment item template
      get_template_part('parts/baum-attachment-item');
    }
  }

  wp_reset_postdata();
  wp_die();
}
add_action('wp_ajax_baum_load_more_attachments', 'baum_load_more_attachments');
add_action('wp_ajax_nopriv_baum_load_more_attachments', 'baum_load_more_attachments');

/**
 * Load modal template parts into footer
 */
function baum_load_modal_templates() {
  // Only load modals on frontend (not admin)
  if (is_admin()) {
    return;
  }

  $modal_templates = array(
    'search-modal',
    'posts-modal',
    'new-post-modal',
    'post-form-modal',
    'topics-modal',
    'donation-modal',
    'subscription-modal'
  );

  foreach ($modal_templates as $template) {
    // Try includes/modals first (current location)
    $template_path = get_template_directory() . '/includes/modals/' . $template . '.php';
    if (file_exists($template_path)) {
      include $template_path;
    } else {
      // Fallback to template-parts/modals
      get_template_part('template-parts/modals/' . $template);
    }
  }
}
add_action('wp_footer', 'baum_load_modal_templates');

/**
 * Hide admin bars and styling when loaded in iframe
 */
function baum_hide_admin_bars_in_iframe() {
  // Check if we're in an iframe (look for iframe parameter or referrer)
  $is_iframe = isset($_GET['iframe']) ||
               (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], home_url()) !== false);

  if ($is_iframe && is_admin()) {
    // Hide admin bar
    add_filter('show_admin_bar', '__return_false');

    // Add custom CSS to hide elements and style for iframe
    add_action('admin_head', function() {
      echo '<style>

      
      </style>';
    });
  }
}
add_action('init', 'baum_hide_admin_bars_in_iframe');

// Enqueue modal system assets
function baum_enqueue_modal_assets() {
  // Enqueue modal CSS
  wp_enqueue_style('baum-modals', get_template_directory_uri() . '/style-modals.css', [], '1.0.0');

  // Enqueue modal JavaScript files
  wp_enqueue_script('baum-user-dropdown', get_template_directory_uri() . '/js/baum-user-dropdown.js', ['jquery'], '1.0.0', true);
  wp_enqueue_script('baum-search-modal', get_template_directory_uri() . '/js/baum-search-modal.js', ['jquery'], '1.0.0', true);
  wp_enqueue_script('baum-posts-modal', get_template_directory_uri() . '/js/baum-posts-modal.js', ['jquery'], '1.0.0', true);
  wp_enqueue_script('baum-new-post-modal', get_template_directory_uri() . '/js/baum-new-post-modal.js', ['jquery'], '1.0.0', true);
  wp_enqueue_script('baum-post-form-modal', get_template_directory_uri() . '/js/baum-post-form-modal.js', ['jquery'], '1.0.0', true);
  wp_enqueue_script('baum-modal-navigation', get_template_directory_uri() . '/js/baum-modal-navigation.js', ['jquery'], '1.0.0', true);
  wp_enqueue_script('baum-payment-modals', get_template_directory_uri() . '/js/baum-payment-modals.js', ['jquery'], '1.0.0', true);


  // Localize scripts for AJAX
  wp_localize_script('baum-search-modal', 'baumSearch', [
    'ajaxUrl' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('baum_search_nonce')
  ]);

  wp_localize_script('baum-posts-modal', 'baumPosts', [
    'ajaxUrl' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('baum_posts_nonce')
  ]);

  // Add global ajaxurl for compatibility
  wp_localize_script('baum-payment-modals', 'baumGlobal', [
    'ajaxUrl' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('baum_global_nonce')
  ]);

  // Add ajaxurl to global scope for legacy compatibility
  wp_add_inline_script('baum-payment-modals', 'var ajaxurl = "' . admin_url('admin-ajax.php') . '";', 'before');


}


add_action('wp_enqueue_scripts', 'baum_enqueue_modal_assets');




// Load the optimized CSS system
require_once get_template_directory() . '/functions-css-optimization.php';




// AJAX handler for search functionality
function baum_search_handler() {
  check_ajax_referer('baum_search_nonce', 'nonce');

  $query = sanitize_text_field($_POST['query'] ?? '');

  if (empty($query)) {
    wp_send_json_error('Empty search query');
  }

  // Search posts, pages, and custom post types
  $search_query = new WP_Query([
    'post_type' => ['post', 'page', 'activity', 'announcement', 'event', 'group', 'media', 'place'],
    'post_status' => 'publish',
    's' => $query,
    'posts_per_page' => 10,
    'orderby' => 'relevance'
  ]);

  $results = [];

  if ($search_query->have_posts()) {
    while ($search_query->have_posts()) {
      $search_query->the_post();

      $post_type_obj = get_post_type_object(get_post_type());
      $icon = 'fa-solid fa-file-alt';

      // Set appropriate icon based on post type
      switch (get_post_type()) {
        case 'page':
          $icon = 'fa-solid fa-file';
          break;
        case 'activity':
          $icon = 'fa-solid fa-calendar-alt';
          break;
        case 'announcement':
          $icon = 'fa-solid fa-megaphone';
          break;
        case 'event':
          $icon = 'fa-solid fa-calendar';
          break;
        case 'group':
          $icon = 'fa-solid fa-users';
          break;
        case 'media':
          $icon = 'fa-solid fa-image';
          break;
        case 'place':
          $icon = 'fa-solid fa-map-marker-alt';
          break;
      }

      $results[] = [
        'title' => get_the_title(),
        'excerpt' => wp_trim_words(get_the_excerpt() ?: get_the_content(), 15),
        'url' => get_permalink(),
        'type' => $post_type_obj->labels->singular_name ?? 'Post',
        'icon' => $icon
      ];
    }
  }

  wp_reset_postdata();
  wp_send_json_success($results);
}
add_action('wp_ajax_baum_search', 'baum_search_handler');
add_action('wp_ajax_nopriv_baum_search', 'baum_search_handler');

// AJAX handler for saving user interests/topics
function baum_save_user_interests_ajax() {
  // Verify nonce
  if (!wp_verify_nonce($_POST['nonce'], 'baum_save_interests')) {
    wp_send_json_error('Invalid nonce');
  }

  // Check if user is logged in
  if (!is_user_logged_in()) {
    wp_send_json_error('User not logged in');
  }

  $user_id = get_current_user_id();
  $interests = json_decode(stripslashes($_POST['interests']), true);

  if (!is_array($interests)) {
    wp_send_json_error('Invalid interests data');
  }

  // Save user interests
  $result = update_user_meta($user_id, 'baum_user_interests', $interests);

  if ($result !== false) {
    wp_send_json_success(array(
      'message' => 'Interests saved successfully',
      'interests' => $interests
    ));
  } else {
    wp_send_json_error('Failed to save interests');
  }
}
add_action('wp_ajax_baum_save_user_interests', 'baum_save_user_interests_ajax');

// AJAX handler for user posts
function baum_get_user_posts_handler() {
  check_ajax_referer('baum_posts_nonce', 'nonce');

  $filter = sanitize_text_field($_POST['filter'] ?? 'all');
  $current_user_id = get_current_user_id();

  if (!$current_user_id) {
    wp_send_json_error('User not logged in');
  }

  // Query args for user's posts
  $query_args = [
    'author' => $current_user_id,
    'post_type' => ['post', 'page', 'activity', 'announcement', 'event', 'group', 'media', 'place'],
    'posts_per_page' => 50,
    'orderby' => 'date',
    'order' => 'DESC'
  ];

  // Add status filter
  if ($filter === 'publish') {
    $query_args['post_status'] = 'publish';
  } elseif ($filter === 'draft') {
    $query_args['post_status'] = 'draft';
  } else {
    $query_args['post_status'] = ['publish', 'draft', 'pending', 'private'];
  }

  $posts_query = new WP_Query($query_args);

  $posts = [];
  $stats = [
    'total' => 0,
    'published' => 0,
    'drafts' => 0
  ];

  if ($posts_query->have_posts()) {
    while ($posts_query->have_posts()) {
      $posts_query->the_post();

      $post_type_obj = get_post_type_object(get_post_type());
      $edit_url = get_edit_post_link();
      $view_url = get_post_status() === 'publish' ? get_permalink() : null;

      $posts[] = [
        'id' => get_the_ID(),
        'title' => get_the_title() ?: 'Untitled',
        'status' => get_post_status(),
        'type' => $post_type_obj->labels->singular_name ?? 'Post',
        'date' => get_the_date('M j, Y'),
        'edit_url' => $edit_url,
        'view_url' => $view_url
      ];

      // Update stats
      $stats['total']++;
      if (get_post_status() === 'publish') {
        $stats['published']++;
      } elseif (get_post_status() === 'draft') {
        $stats['drafts']++;
      }
    }
  }

  wp_reset_postdata();

  wp_send_json_success([
    'posts' => $posts,
    'stats' => $stats
  ]);
}
add_action('wp_ajax_baum_get_user_posts', 'baum_get_user_posts_handler');

// Inject iframe messaging script into admin when loaded in iframe
function baum_inject_iframe_messaging() {
  // Only inject in admin area
  if (!is_admin()) {
    return;
  }

  // Check if we might be in an iframe (this is a best guess)
  $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
  $referer = $_SERVER['HTTP_REFERER'] ?? '';

  // If referer is from the same domain, we might be in an iframe
  if ($referer && strpos($referer, home_url()) === 0) {
    wp_enqueue_script(
      'baum-iframe-messaging',
      get_template_directory_uri() . '/js/baum-iframe-messaging.js',
      [],
      '1.0.0',
      true
    );
  }
}
add_action('admin_enqueue_scripts', 'baum_inject_iframe_messaging');

// Modal HTML is created dynamically by JavaScript classes
// No need for PHP-generated modal HTML

function baum_enqueue_scripts () {
  global $wp_styles;
  $version = wp_get_theme()->get('Version');

  //
  // Font Awesome 6
  //
  wp_enqueue_style(
    'font-awesome-6',
    get_template_directory_uri() . '/css/font-awesome-6.css',
    [],
    '6.0.0',
    'all',
  );

  //
  // YouTube-style Playlist CSS
  //
  if (is_tax('playlist')) {
    wp_enqueue_style(
      'baum-playlist-youtube',
      get_template_directory_uri() . '/css/style-playlist-youtube.css',
      [],
      $version,
      'all'
    );
  }

  //
  // Font Awesome 5
  //
  // wp_enqueue_style(
  //   'font-awesome-5',
  //   get_template_directory_uri() . '/css/font-awesome-5.css',
  //   [],
  //   '5.12.1',
  //   'all'
  // );

  //
  // Font Awesome 6
  //
  // TODO: Save the font files locally and link to them locally
  // wp_enqueue_style(
  //   'font-awesome-6',
  //   'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css',
  //   [],
  //   '6.0.0',
  //   'all',
  // );


  //
  // jQuery Infinite Scroll
  //
  wp_enqueue_script(
    'baum-infinite',
    get_template_directory_uri() . '/js/infinite-scroll.min.js',
    ['jquery'],
    '1.0',
    true
  );

  //
  // Tooltipster JS
  //
  wp_enqueue_script(
    'baum-tooltips-js',
    get_template_directory_uri() . '/js/tooltipster.bundle.min.js',
    ['jquery'],
    '1.0',
    true
  );

  //
  // Tooltipster CSS
  //
  wp_enqueue_style(
    'tooltipster-css',
    get_template_directory_uri() . '/css/tooltipster.bundle.min.css',
    [],
    $version,
    'all'
  );

  //
  // Tooltipster CSS - Borderless Theme
  //
  wp_enqueue_style(
    'tooltipster-theme-css',
    get_template_directory_uri() . '/css/tooltipster-borderless.min.css',
    ['tooltipster-css'],
    $version,
    'all'
  );

  //
  // Syntax Highlighting - Atom One (dark)
  //
  wp_enqueue_style(
    'baum-atom-one-dark',
    get_template_directory_uri() . '/css/atom-one-dark.css',
    [],
    $version,
    'all'
  );

  // //
  // // Syntax Highlighting - Atom One (light)
  // //
  // wp_enqueue_style(
  //   'baum-atom-one-light',
  //   get_template_directory_uri() . '/css/atom-one-light.css',
  //   [],
  //   $version,
  //   'all'
  // );

  //
  // Highlight.js
  //
  wp_enqueue_script(
    'baum-highlight-js',
    get_template_directory_uri() . '/js/highlight.js',
    ['jquery'],
    '1.0',
    true
  );

  //
  // Syntax Highlighting: list desired programming languages
  //
  $comp_lang = [
    'apache',
    'bash',
    'css',
    'dockerfile',
    'graphql',
    'http',
    'javascript',
    'json',
    'makefile',
    'markdown',
    'nginx',
    'php',
    'plaintext',
    'python',
    'shell',
    'sql',
    'typescript',
    'yaml'
  ];

  //
  // Syntax Highlighting: enqueque each language in the array
  //
  foreach ($comp_lang as $lang) {
    wp_enqueue_script(
      'baum-highlight-language-' . $lang,
      get_template_directory_uri() . '/js/languages/' . $lang . '.js',
      ['baum-highlight-js'],
      '1.0',
      true
    );
  }

  //
  // Main Index JS
  //
  wp_enqueue_script(
    'baum-index-js',
    get_template_directory_uri() . '/js/index.js',
    ['baum-tooltips-js'],
    '1.0',
    true
  );

  $cat_ids = [];
  $cats = get_the_category();
  foreach ($cats as $cat) {
    $cat_ids[] = get_cat_ID($cat->cat_name);
  }

  //
  // Send data to frontend file
  //
  wp_localize_script(
    'baum-index-js',
    'wp_object',
    [
      'cat_ids'          => $cat_ids,
      'categories'       => $cats,
      'ajaxurl'          => admin_url('admin-ajax.php'),
      'is_logged_in'     => is_user_logged_in(),
      'tooltipster'      => [
        'animation'      => 'grow',
        'theme'          => 'tooltipster-borderless',
        'interactive'    => true,
        'contentAsHTML'  => true,
      ]
    ]
  );


  //
  // Comments JS
  //
  wp_enqueue_script(
    'baum-comments-js',
    get_template_directory_uri() . '/js/comments.js',
    ['baum-index-js'],
    '1.0',
    true
  );

  wp_enqueue_script('comment-reply');

  //
  // Baum's Apple Menu JS
  //
  wp_enqueue_script(
    'baum-apple-menu-script',
    get_template_directory_uri() . '/js/apple-menu.js',
    ['jquery'],
    null,
    true
  );

  //
  // Baum's Header Categories JS
  //
  wp_enqueue_script(
    'baum-header-categories-js',
    get_template_directory_uri() . '/js/baum-header-categories.js',
    ['jquery'],
    $version,
    true
  );

  //
  // Baum's Skeleton Utilities JS
  //
  wp_enqueue_script(
    'baum-skeleton-utils-js',
    get_template_directory_uri() . '/js/baum-skeleton-utils.js',
    [],
    $version,
    true
  );

  //
  // Apple Menu Dropdowns JS (Notifications & Developer Tools)
  // Re-enabled for notification system functionality
  //
  wp_enqueue_script(
    'baum-menu-dropdowns-js',
    get_template_directory_uri() . '/js/baum-menu-dropdowns.js',
    ['jquery'],
    $version,
    true
  );

  // Localize notifications script
  wp_localize_script('baum-menu-dropdowns-js', 'baumNotifications', array(
    'ajaxUrl' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('baum_notifications_nonce'),
    'currentUserId' => get_current_user_id(),
    'isLoggedIn' => is_user_logged_in()
  ));

  //
  // Rotating Card JavaScript
  //
  wp_enqueue_script(
    'baum-rotating-card-js',
    get_template_directory_uri() . '/js/baum-rotating-card.js',
    ['jquery'],
    $version,
    true
  );

  //
  // Notifications CSS
  //
  wp_enqueue_style(
    'baum-notifications-css',
    get_template_directory_uri() . '/style-notifications.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Developer Tools CSS
  //
  wp_enqueue_style(
    'baum-developer-tools-css',
    get_template_directory_uri() . '/style-developer-tools.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Rotating Card CSS
  //
  wp_enqueue_style(
    'baum-rotating-card-css',
    get_template_directory_uri() . '/style-rotating-card.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Header Categories Navigation JS
  //
  wp_enqueue_script(
    'baum-header-categories',
    get_template_directory_uri() . '/js/baum-header-categories.js',
    [],
    $version,
    true
  );

  //
  // Date Display JS
  //
  wp_enqueue_script(
    'baum-date-display',
    get_template_directory_uri() . '/js/baum-date-display.js',
    [],
    $version,
    true
  );

  //
  // Time Capsule Component JS
  //
  wp_enqueue_script(
    'baum-time-capsule',
    get_template_directory_uri() . '/js/baum-time-capsule.js',
    [],
    $version,
    true
  );

  //
  // Radio Player CSS
  //
  wp_enqueue_style(
    'baum-radio-player-css',
    get_template_directory_uri() . '/css/baum-radio-player.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Radio Player JavaScript
  //
  wp_enqueue_script(
    'baum-radio-player-js',
    get_template_directory_uri() . '/js/baum-radio-player.js',
    ['jquery'],
    $version,
    true
  );

  //
  // SPA Navigation CSS
  //
  wp_enqueue_style(
    'baum-spa-navigation-css',
    get_template_directory_uri() . '/css/baum-spa-navigation.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // SPA Helpers JavaScript
  //
  wp_enqueue_script(
    'baum-spa-helpers-js',
    get_template_directory_uri() . '/js/baum-spa-helpers.js',
    ['jquery'],
    $version,
    true
  );

  //
  // SPA Navigation JavaScript
  //
  wp_enqueue_script(
    'baum-spa-navigation-js',
    get_template_directory_uri() . '/js/baum-spa-navigation.js',
    ['baum-radio-player-js', 'baum-spa-helpers-js'],
    $version,
    true
  );

  //
  // Style Guide JavaScript (only on style guide page)
  //
  if (is_page('style-guide')) {
    wp_enqueue_script(
      'baum-style-guide-js',
      get_template_directory_uri() . '/js/style-guide.js',
      ['jquery'],
      $version,
      true
    );
  }

  //
  // Account Dropdown JavaScript
  //
  wp_enqueue_script(
    'baum-account-dropdown-js',
    get_template_directory_uri() . '/js/baum-account-dropdown.js',
    ['jquery'],
    $version,
    true
  );
  //
  // Step 3 - enqueque the script
  //
  // wp_enqueue_script('baum-index-js');


  //
  // GitHub Syntax Highlighting (Dark)
  //
  // wp_enqueue_style(
  //   'baum-github-dark-css',
  //   get_template_directory_uri() . '/css/github-dark.css',
  //   array(),
  //   [],
  //   'all'
  // );

  //
  // GitHub Syntax Highlighting (Light)
  //
  // wp_enqueue_style(
  //   'baum-github-css',
  //   get_template_directory_uri() . '/css/github.css',
  //   [],
  //   $version,
  //   'all'
  // );

  //
  // Normalize CSS
  //
  wp_enqueue_style(
    'normalize-css',
    get_template_directory_uri() . '/css/normalize.css',
    [],
    $version,
    'all'
  );

  //
  // Skeleton CSS
  //
  wp_enqueue_style(
    'skeleton-css',
    get_template_directory_uri() . '/css/skeleton.css',
    ['normalize-css'],
    $version,
    'all'
  );

  //
  // Kirki Color System CSS (replaces hardcoded colors)
  //
  wp_enqueue_style(
    'style-kirki-colors-css',
    get_template_directory_uri() . '/style-kirki-colors.css',
    ['skeleton-css'],
    $version,
    'all'
  );

  //
  // Dark Mode Integration CSS (bridges Kirki with manual toggle)
  //
  wp_enqueue_style(
    'style-dark-mode-integration-css',
    get_template_directory_uri() . '/style-dark-mode-integration.css',
    ['style-kirki-colors-css'],
    $version,
    'all'
  );

  //
  // Style Variables CSS (legacy support)
  //
  wp_enqueue_style(
    'style-vars-css',
    get_template_directory_uri() . '/style-baum-vars.css',
    ['style-kirki-colors-css'],
    $version,
    'all'
  );

  //
  // Style CSS
  //
  wp_enqueue_style(
    'style-css',
    get_template_directory_uri() . '/style.css',
    ['skeleton-css'],
    $version,
    'all'
  );

  //
  // WP Elements Stylesheet
  //
  wp_enqueue_style(
    'style-wp-css',
    get_template_directory_uri() . '/style-wp.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's Third Party Integrations Stylesheet
  //
  wp_enqueue_style(
    'style-third-party-css',
    get_template_directory_uri() . '/style-third-party.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // AP News Inspired Stylesheet
  //
  if (get_theme_mod('baum_theme') == 'ap-news') {
    wp_enqueue_style(
      'style-theme-ap-news-css',
      get_template_directory_uri() . '/style-theme-ap-news.css',
      ['style-css'],
      $version,
      'all'
    );
  }

  //
  // GitHub Inspired Stylesheet
  //
  if (get_theme_mod('baum_theme') == 'github') {
    wp_enqueue_style(
      'style-theme-github-css',
      get_template_directory_uri() . '/style-theme-github.css',
      ['style-css'],
      $version,
      'all'
    );
  }

  //
  // Mobile Stylesheet
  //
  wp_enqueue_style(
    'style-mobile-css',
    get_template_directory_uri() . '/style-mobile.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Font Stylesheet
  //
  wp_enqueue_style(
    'style-font-css',
    get_template_directory_uri() . '/style-font.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's Dark Mode Stylesheet (REMOVED - Kirki handles dark mode now)
  // File removed: style-old-dark.css
  //

  //
  // Baum's Main Stylesheet
  //
  wp_enqueue_style(
    'style-baum-css',
    get_template_directory_uri() . '/style-baum.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's Video UI Stylesheet
  //
  wp_enqueue_style(
    'style-baum-video-css',
    get_template_directory_uri() . '/style-baum-video.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's Colors Stylesheet
  //
  wp_enqueue_style(
    'style-baum-colors-css',
    get_template_directory_uri() . '/style-baum-colors.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's News Cards Stylesheet
  //
  wp_enqueue_style(
    'style-baum-cards-css',
    get_template_directory_uri() . '/style-baum-cards.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's Skeleton Loaders Stylesheet
  //
  wp_enqueue_style(
    'baum-skeleton-loaders-css',
    get_template_directory_uri() . '/css/skeleton-loaders.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's Featured Images Frontend Stylesheet
  //
  wp_enqueue_style(
    'baum-featured-images-frontend-css',
    get_template_directory_uri() . '/css/baum-featured-images-frontend.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Time Capsule Stylesheet (force load with version)
  //
  wp_enqueue_style(
    'baum-time-capsule-css',
    get_template_directory_uri() . '/style-time-capsule.css',
    ['style-css'],
    '2.0.0' // Version 2.0 to force cache refresh
  );

  //
  // Hierarchical Byline System CSS
  //
  wp_enqueue_style(
    'baum-hierarchical-byline-css',
    get_template_directory_uri() . '/assets/css/baum-hierarchical-byline.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Shared Post Layout Styles CSS
  //
  wp_enqueue_style(
    'baum-post-layout-shared-css',
    get_template_directory_uri() . '/assets/css/post-layout-shared.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Individual Post Layout Styles CSS
  //
  wp_enqueue_style(
    'baum-post-layout-hero-css',
    get_template_directory_uri() . '/assets/css/layouts/post-layout-hero.css',
    ['baum-post-layout-shared-css'],
    $version,
    'all'
  );

  wp_enqueue_style(
    'baum-post-layout-magazine-css',
    get_template_directory_uri() . '/assets/css/layouts/post-layout-magazine.css',
    ['baum-post-layout-shared-css'],
    $version,
    'all'
  );

  wp_enqueue_style(
    'baum-post-layout-newspaper-css',
    get_template_directory_uri() . '/assets/css/layouts/post-layout-newspaper.css',
    ['baum-post-layout-shared-css'],
    $version,
    'all'
  );

  wp_enqueue_style(
    'baum-post-layout-standard-css',
    get_template_directory_uri() . '/assets/css/layouts/post-layout-standard.css',
    ['baum-post-layout-shared-css'],
    $version,
    'all'
  );

  wp_enqueue_style(
    'baum-post-layout-minimal-css',
    get_template_directory_uri() . '/assets/css/layouts/post-layout-minimal.css',
    ['baum-post-layout-shared-css'],
    $version,
    'all'
  );

  wp_enqueue_style(
    'baum-post-layout-immersive-css',
    get_template_directory_uri() . '/assets/css/layouts/post-layout-immersive.css',
    ['baum-post-layout-shared-css'],
    $version,
    'all'
  );

  wp_enqueue_style(
    'baum-post-layout-social-css',
    get_template_directory_uri() . '/assets/css/layouts/post-layout-social.css',
    ['baum-post-layout-shared-css'],
    $version,
    'all'
  );

  //
  // Post Manager Stylesheet (only on post manager pages)
  //
  if ( is_page_template( 'page-post-manager.php' ) ) {
    wp_enqueue_style(
      'style-post-manager-css',
      get_template_directory_uri() . '/style-post-manager.css',
      ['style-css'],
      $version,
      'all'
    );
  }

  //
  // Chart Conflicts Fix (load early to resolve color variable conflicts)
  //
  wp_enqueue_script(
    'baum-chart-conflicts-fix',
    get_template_directory_uri() . '/js/fix-chart-conflicts.js',
    [],
    $version,
    false // Load in head to execute before other chart scripts
  );

  //
  // Baum's Apple Menu Stylesheet
  //
  // wp_enqueue_style(
  //   'baum-apple-menu-style',
  //   get_template_directory_uri() . '/css/apple-menu.css'
  // );

  /////////////////////////////////////
  // Baum's Query Vars
  /////////////////////////////////////

  function baum_query_vars ($qvars) {
    $qvars[] = 'autoplay';
    return $qvars;
  }

  add_filter('query_vars', 'baum_query_vars');

  /////////////////////////////////////
  // CSS Color Variables
  /////////////////////////////////////


  $ico_style_str = '';
  $cat_array = get_categories();

  foreach ($cat_array as $cat) {
    $cat_id = $cat->term_id;
    $cat_ico = get_field('ico', 'category_' . $cat_id);

    if ($cat_ico) {
      $ico_style_str .= '.menu-item-' . $cat->term_id . '::before {';
      $ico_style_str .= 'background-image: url("' . $cat_ico . '");';
      $ico_style_str .= 'background-size: 12px;';
      $ico_style_str .= 'background-repeat: no-repeat;';
      $ico_style_str .= 'position: relative;';
      $ico_style_str .= 'z-index: 25;';
      $ico_style_str .= 'left: -4px;';
      $ico_style_str .= 'top: 0px;';
      $ico_style_str .= 'content: "";';
      $ico_style_str .= 'width: 20px !important;';
      $ico_style_str .= 'height: 12px;';
      $ico_style_str .= '}';
      $ico_style_str .= '.menu-item-' . $cat->term_id . ' {';
      $ico_style_str .= 'height: 26px;';
      $ico_style_str .= '}';
      $ico_style_str .= '.menu-item-' . $cat->term_id . ' a {';
      $ico_style_str .= 'line-height: 16px; position: relative; left: -8px; top: 0px;';
      $ico_style_str .= '}';
    }
  }

  $terms_array = get_terms([ 'taxonomy' => 'channel' ]);

  foreach ($terms_array as $channel) {
    $channel_id = $channel->term_id;
    $channel_ico = get_field('favicon', $channel);


    $url = get_field('url', $channel);
    $channel_ico = 'https://www.google.com/s2/favicons?domain='.$url.'&sz=128';

    if ($channel_ico) {
      $ico_style_str .= '.baum-channel-' . $channel->term_id . '::before,';
      $ico_style_str .= '.menu-item-' . $channel->term_id . '::before {';
      $ico_style_str .= 'background-image: url("' . $channel_ico . '");';
      $ico_style_str .= 'background-size: 12px;';
      $ico_style_str .= 'background-repeat: no-repeat;';
      $ico_style_str .= 'position: relative;';
      $ico_style_str .= 'z-index: 25;';
      $ico_style_str .= 'left: 0px;';
      $ico_style_str .= 'top: 0px;';
      $ico_style_str .= 'content: "";';
      $ico_style_str .= 'width: 18px !important;';
      $ico_style_str .= 'height: 11px;';
      $ico_style_str .= 'display: inline-block;';
      $ico_style_str .= '}';
      $ico_style_str .= '.menu-item-' . $channel->term_id . ' {';
      $ico_style_str .= 'height: 26px;';
      $ico_style_str .= '}';
      $ico_style_str .= '.menu-item-' . $channel->term_id . ' a {';
      $ico_style_str .= 'line-height: 16px; position: relative; left: -8px; top: -2px;';
      $ico_style_str .= '}';
    }
  }

  wp_register_style('baum-inline-ico-css', false);
  wp_enqueue_style('baum-inline-ico-css');
  wp_add_inline_style('baum-inline-ico-css', $ico_style_str);

  /////////////////////////////////////
  // Baum's Sign Out Redirect
  /////////////////////////////////////

  // function baum_redirect_signout () {
  //   wp_safe_redirect(home_url());
  //   exit;
  // }
  // add_action('wp_logout', 'baum_redirect_signout');

  //
  // Activity Content Types Stylesheet
  //
  wp_enqueue_style(
    'baum-activity-content-types-css',
    get_template_directory_uri() . '/assets/css/activity-content-types.css',
    ['style-css'],
    $version,
    'all'
  );

}

add_action('wp_enqueue_scripts', 'baum_enqueue_scripts');

/**
 * Create sample radio stations for demonstration
 *
 * This function creates sample radio station posts with the 'groups' post type
 * and 'radio' group type for demonstration purposes.
 *
 * @return void
 * @since 1.0.0
 */
function baum_create_sample_radio_stations() {
  // Check if we already have radio stations
  $existing_stations = get_posts(array(
    'post_type' => 'baum_groups',
    'meta_query' => array(
      array(
        'key' => 'group_type',
        'value' => 'radio',
        'compare' => '='
      )
    ),
    'posts_per_page' => 1
  ));

  // If we already have stations, don't create more
  if (!empty($existing_stations)) {
    return;
  }

  // Sample radio stations data
  $sample_stations = array(
    array(
      'title' => 'NPR Live Stream',
      'description' => 'National Public Radio - Live news, talk, and cultural programming.',
      'stream_url' => 'https://npr-ice.streamguys1.com/live.mp3',
      'category' => 'news',
      'content_type' => 'radio',
      'is_live' => true,
      'website' => 'https://www.npr.org/'
    ),
    array(
      'title' => 'KEXP Seattle',
      'description' => 'Where the music matters. Independent music discovery from Seattle.',
      'stream_url' => 'https://kexp-mp3-128.streamguys1.com/kexp128.mp3',
      'category' => 'music',
      'content_type' => 'radio',
      'is_live' => true,
      'website' => 'https://kexp.org/'
    ),
    array(
      'title' => 'Radio Paradise',
      'description' => 'Eclectic music from Paradise! Commercial-free, listener-supported radio.',
      'stream_url' => 'http://stream.radioparadise.com/aac-320',
      'category' => 'music',
      'content_type' => 'radio',
      'is_live' => true,
      'website' => 'https://radioparadise.com/'
    ),
    array(
      'title' => 'SomaFM Groove Salad',
      'description' => 'A nicely chilled plate of ambient/downtempo beats and grooves.',
      'stream_url' => 'http://ice1.somafm.com/groovesalad-256-mp3',
      'category' => 'music',
      'content_type' => 'radio',
      'is_live' => true,
      'website' => 'https://somafm.com/'
    )
  );

  // Create the sample stations
  foreach ($sample_stations as $station_data) {
    $post_id = wp_insert_post(array(
      'post_title' => sanitize_text_field($station_data['title']),
      'post_content' => sanitize_textarea_field($station_data['description']),
      'post_excerpt' => sanitize_textarea_field($station_data['description']),
      'post_status' => 'publish',
      'post_type' => 'baum_groups'
    ));

    if ($post_id && !is_wp_error($post_id)) {
      // Add custom fields
      update_field('group_type', 'radio', $post_id);
      update_field('radio_stream_url', esc_url_raw($station_data['stream_url']), $post_id);

      // Optional fields - only set if not empty
      if (!empty($station_data['category'])) {
        update_field('radio_category', sanitize_text_field($station_data['category']), $post_id);
      }
      if (!empty($station_data['content_type'])) {
        update_field('radio_content_type', sanitize_text_field($station_data['content_type']), $post_id);
      }
      if (isset($station_data['is_live'])) {
        update_field('radio_is_live', (bool)$station_data['is_live'], $post_id);
      }

      // Use existing group fields for logo and website
      if (!empty($station_data['website'])) {
        update_field('group_website', esc_url_raw($station_data['website']), $post_id);
        update_field('website', esc_url_raw($station_data['website']), $post_id); // Fallback
      }
    }
  }
}

// Hook to create sample stations on theme activation
add_action('after_switch_theme', 'baum_create_sample_radio_stations');

/**
 * Debug function to show all baum_groups posts and their meta data
 * Add ?debug_radio=1 to any page URL to see this info
 *
 * @return void
 * @since 1.0.0
 */
function baum_debug_radio_stations() {
  // Only run on specific debug request and for admins
  if (!isset($_GET['debug_radio']) || $_GET['debug_radio'] !== '1' || !current_user_can('manage_options')) {
    return;
  }

  // Don't run on AJAX requests or admin pages
  if (wp_doing_ajax() || is_admin()) {
    return;
  }

  // Use output buffering to prevent conflicts
  ob_start();
  ?>
  <div style="background: white; padding: 20px; margin: 20px; border: 2px solid red; position: fixed; top: 50px; left: 50px; width: 80%; max-height: 80%; overflow: auto; z-index: 999999; box-shadow: 0 0 20px rgba(0,0,0,0.5);">
    <h2>🐛 Debug: Baum Groups Posts</h2>
    <button onclick="this.parentElement.style.display='none'" style="float: right; background: red; color: white; border: none; padding: 5px 10px; cursor: pointer;">Close</button>

    <?php
    // Get all baum_groups posts
    $all_groups = get_posts(array(
      'post_type' => 'baum_groups',
      'posts_per_page' => -1,
      'post_status' => 'publish'
    ));

    if (empty($all_groups)) {
      echo '<p><strong>❌ No baum_groups posts found!</strong></p>';
      echo '<p>Make sure you have created a post with post_type = "baum_groups"</p>';
    } else {
      echo '<p><strong>✅ Found ' . count($all_groups) . ' baum_groups posts:</strong></p>';

      foreach ($all_groups as $group) {
        echo '<div style="border: 1px solid #ccc; padding: 15px; margin: 10px 0; background: #f9f9f9;">';
        echo '<h3>📻 ' . esc_html($group->post_title) . ' (ID: ' . $group->ID . ')</h3>';
        echo '<p><strong>Status:</strong> ' . $group->post_status . '</p>';
        echo '<p><strong>Content:</strong> ' . esc_html(wp_trim_words($group->post_content, 20)) . '</p>';

        // Get all meta data
        $all_meta = get_post_meta($group->ID);
        echo '<h4>🔧 All Meta Fields:</h4>';
        echo '<pre style="background: white; padding: 10px; overflow: auto; max-height: 200px; font-size: 12px;">';
        foreach ($all_meta as $key => $values) {
          if (strpos($key, '_') !== 0) { // Skip internal WordPress fields
            echo esc_html($key) . ' => ' . esc_html(print_r($values, true)) . "\n";
          }
        }
        echo '</pre>';

        // Try ACF fields too
        if (function_exists('get_field')) {
          echo '<h4>🎯 ACF Field Tests:</h4>';
          $test_fields = ['group_type', 'radio_stream_url', 'stream_url', 'radio_content_type', 'radio_category'];
          foreach ($test_fields as $field) {
            $value = get_field($field, $group->ID);
            echo '<p><strong>' . $field . ':</strong> ' . ($value ? esc_html($value) : '<em>empty</em>') . '</p>';
          }
        }

        echo '</div>';
      }
    }
    ?>
  </div>
  <?php
  $debug_output = ob_get_clean();

  // Add to footer instead of head to avoid conflicts
  add_action('wp_footer', function() use ($debug_output) {
    echo $debug_output;
  });
}

// Temporarily disabled debug function to fix homepage
// add_action('init', 'baum_debug_radio_stations');

/////////////////////////////////////
// Rotating Card Shortcode
/////////////////////////////////////

/**
 * Rotating Card Shortcode
 *
 * Creates an RT.com style rotating card with featured image and article list.
 *
 * @param array $atts Shortcode attributes
 * @return string HTML output
 * @since 1.0.0
 */
function baum_rotating_card_shortcode($atts) {
  $args = shortcode_atts(array(
    'posts' => 5,
    'category' => '',
    'tag' => '',
    'post_type' => 'post',
    'orderby' => 'date',
    'order' => 'DESC',
    'class' => '',
    'meta_key' => '',
    'meta_value' => ''
  ), $atts);

  // Convert shortcode attributes to template arguments
  $template_args = array(
    'posts_per_page' => intval($args['posts']),
    'category' => sanitize_text_field($args['category']),
    'tag' => sanitize_text_field($args['tag']),
    'post_type' => sanitize_text_field($args['post_type']),
    'orderby' => sanitize_text_field($args['orderby']),
    'order' => sanitize_text_field($args['order']),
    'class' => sanitize_text_field($args['class']),
    'meta_key' => sanitize_text_field($args['meta_key']),
    'meta_value' => sanitize_text_field($args['meta_value'])
  );

  // Capture output
  ob_start();
  get_template_part('parts/baum-card-rotating', null, $template_args);
  return ob_get_clean();
}

add_shortcode('rotating_card', 'baum_rotating_card_shortcode');

/**
 * Rotating Card Gutenberg Block (simple version)
 *
 * @since 1.0.0
 */
function baum_register_rotating_card_block() {
  if (!function_exists('register_block_type')) {
    return;
  }

  register_block_type('baumpress/rotating-card', array(
    'render_callback' => 'baum_rotating_card_block_render',
    'attributes' => array(
      'posts' => array(
        'type' => 'number',
        'default' => 5
      ),
      'category' => array(
        'type' => 'string',
        'default' => ''
      ),
      'className' => array(
        'type' => 'string',
        'default' => ''
      )
    )
  ));
}

function baum_rotating_card_block_render($attributes) {
  $template_args = array(
    'posts_per_page' => $attributes['posts'] ?? 5,
    'category' => $attributes['category'] ?? '',
    'class' => $attributes['className'] ?? ''
  );

  ob_start();
  get_template_part('parts/baum-card-rotating', null, $template_args);
  return ob_get_clean();
}

add_action('init', 'baum_register_rotating_card_block');

// Include skeleton loader functions
require_once get_template_directory() . '/skeleton-screen.php';

// Include Top Posts Widget
require_once get_template_directory() . '/widgets/class-baum-top-posts-widget.php';

/**
 * Add notifications dropdown to main menu
 *
 * This function adds a notifications dropdown with badge to the main menu
 * in a way that's compatible with the existing Apple menu system.
 *
 * @since 1.0.0
 */
function add_notifications_with_dropdown_to_menu() {
  // Get notifications and unread count using new system
  $current_user_id = get_current_user_id();
  if (!$current_user_id) {
    return '';
  }

  $notifications = baum_get_notifications($current_user_id, array('posts_per_page' => 5));
  $unread_count = baum_get_unread_notification_count($current_user_id);

  // Build notifications menu item HTML
  $notifications_item = '<li class="menu-item menu-item-notifications">';
  $notifications_item .= '<a href="#" class="notifications-trigger">';
  $notifications_item .= '<i class="fas fa-bell"></i>';
  // $notifications_item .= '<span>Notifications</span>';
  if ($unread_count > 0) {
    $notifications_item .= '<span class="notification-badge">' . esc_html($unread_count) . '</span>';
  }
  $notifications_item .= '</a>';

  $notifications_item .= '<ul class="sub-menu notifications-dropdown">';
  $notifications_item .= '<li class="notifications-header"><h4>Notifications</h4></li>';
  $notifications_item .= '<li class="notifications-list-container"><div class="notifications-list">';

  if (!empty($notifications)) {
    foreach ($notifications as $notification) {
      $read_class = $notification['read_status'] === 'read' ? 'read' : 'unread';
      $notifications_item .= '<div class="notification-item ' . $read_class . '" data-id="' . esc_attr($notification['id']) . '"';
      if ($notification['action_url']) {
        $notifications_item .= ' data-link="' . esc_attr($notification['action_url']) . '"';
      }
      $notifications_item .= '>';
      $notifications_item .= '<div class="notification-icon" style="background: ' . esc_attr(baum_get_notification_color($notification['type'])) . ';">';
      $notifications_item .= '<i class="' . esc_attr(baum_get_notification_icon($notification['type'])) . '"></i></div>';
      $notifications_item .= '<div class="notification-content">';
      $notifications_item .= '<div class="notification-text">' . wp_kses_post($notification['message']) . '</div>';
      $notifications_item .= '<div class="notification-time">' . esc_html($notification['time_ago']) . '</div>';
      $notifications_item .= '</div>';
      $notifications_item .= '<button class="notification-delete" onclick="deleteNotification(this, event)" aria-label="Delete notification">';
      $notifications_item .= '<i class="fas fa-times"></i></button></div>';
    }
  } else {
    $notifications_item .= '<div class="no-notifications">No notifications</div>';
  }

  $notifications_item .= '</div></li>';
  $notifications_item .= '<li class="notifications-footer">';
  $notifications_item .= '<button onclick="var items = document.querySelectorAll(\'.notification-item\'); items.forEach(function(item) { item.style.display = \'none\'; }); event.stopPropagation(); return false;" class="clear-all-btn">Clear all notifications</button>';
  $notifications_item .= '</li></ul></li>';

  return $notifications_item;
}

/**
 * Add Quick tools dropdown to main menu
 *
 * This function adds a Quick tools dropdown to the main menu
 * in a way that's compatible with the existing Apple menu system.
 *
 * @since 1.0.0
 */
function add_developer_tools_with_dropdown_to_menu() {
  // Build Quick tools menu item HTML
  $developer_item = '<li class="menu-item menu-item-developer-tools">';
  $developer_item .= '<a href="#" class="developer-trigger">';
  $developer_item .= '<i class="fas fa-bolt"></i>';
  $developer_item .= '<span>Quick</span>';
  // $developer_item .= '<i class="fas fa-chevron-down"></i>';
  $developer_item .= '</a>';
  $developer_item .= '<ul class="sub-menu developer-dropdown">';
  $developer_item .= '<li class="developer-dropdown-content">';
  $developer_item .= baum_get_developer_tools_html();
  $developer_item .= '</li></ul></li>';

  return $developer_item;
}

/**
 * Add custom dropdowns to the right menu
 *
 * This function adds notifications dropdown to the main-menu-right location
 * using a filter approach that's compatible with the existing Apple menu system.
 *
 * @param string $items The menu items HTML
 * @param object $args The menu arguments
 * @return string Modified menu items HTML
 *
 * @since 1.0.0
 */
function baum_add_custom_dropdowns_to_menu($items, $args) {
  // Only add to the main-menu-right location
  if ($args->theme_location !== 'main-menu-right') {
    return $items;
  }

  // If no items exist, create an empty string to append to
  if (empty($items)) {
    $items = '';
  }

  // Add notifications dropdown
  $items .= add_notifications_with_dropdown_to_menu();

  return $items;
}

/**
 * Add Quick tools to the left menu (apple-style-menu)
 *
 * This function adds the Quick tools dropdown to the apple-style-menu location
 * which appears on the left side next to the logo, positioned as the first item.
 *
 * @param string $items The menu items HTML
 * @param object $args The menu arguments
 * @return string Modified menu items HTML
 *
 * @since 1.0.0
 */
function baum_add_developer_tools_to_left_menu($items, $args) {
  // Only add to the apple-style-menu location
  if ($args->theme_location !== 'apple-style-menu') {
    return $items;
  }

  // If no items exist, create an empty string to prepend to
  if (empty($items)) {
    $items = '';
  }

  // Prepend Quick tools dropdown to be first item next to logo
  $items = add_developer_tools_with_dropdown_to_menu() . $items;

  return $items;
}

add_filter('wp_nav_menu_items', 'baum_add_custom_dropdowns_to_menu', 10, 2);
add_filter('wp_nav_menu_items', 'baum_add_developer_tools_to_left_menu', 10, 2);

// Include Time Capsule Admin Interface - WITH MEMORY SAFETY CHECKS
if (is_admin() && memory_get_usage() < 400 * 1024 * 1024) {
  require_once get_template_directory() . '/admin/time-capsule-admin.php';
}

/**
 * NOTIFICATION FUNCTIONS MOVED TO functions-notifications.php
 *
 * The notification system has been moved to functions-notifications.php
 * These functions are now available:
 *
 * - baum_get_notifications($user_id, $args)
 * - baum_get_unread_notification_count($user_id)
 * - baum_create_notification($type, $message, $target_user_id, $source_user_id, $action_url, $meta)
 * - baum_mark_notification_read($notification_id, $user_id)
 * - baum_dismiss_notification($notification_id, $user_id)
 *
 * See functions-notifications.php for full documentation.
 */

/**
 * Get viral tools HTML content
 *
 * @return string HTML content for viral tools dropdown
 */
function baum_get_viral_tools_html() {
  $viral_tools = baum_get_viral_tools();

  $html = '<div class="viral-grid">';

  // Left Column - Viral Menu Items
  $html .= '<div class="viral-main-tools">';
  foreach ($viral_tools['main_items'] as $item) {
    $html .= '<div class="viral-tool-item">';
    $html .= '<a href="' . esc_url($item['link']) . '" class="viral-tool-link">';
    $html .= '<div class="viral-tool-icon">';
    $html .= '<i class="' . esc_attr($item['icon']) . '"></i>';
    $html .= '</div>';
    $html .= '<div class="viral-tool-content">';
    $html .= '<div class="viral-tool-title">' . esc_html($item['title']) . '</div>';
    $html .= '</div>';
    $html .= '</a>';
    $html .= '</div>';
  }
  $html .= '</div>';

  // Right Column - Trending Stories
  $html .= '<div class="viral-trending-stories">';
  $html .= '<div class="viral-section-header">';
  $html .= '<span>TRENDING STORIES</span>';
  $html .= '</div>';

  // Get trending posts
  $trending_posts = get_posts([
    'numberposts' => 5,
    'post_status' => 'publish',
    'meta_key' => 'post_views_count',
    'orderby' => 'meta_value_num',
    'order' => 'DESC'
  ]);

  foreach ($trending_posts as $post) {
    $thumbnail = get_the_post_thumbnail_url($post->ID, 'thumbnail');
    $html .= '<div class="viral-story-item">';
    $html .= '<a href="' . esc_url(get_permalink($post->ID)) . '" class="viral-story-link">';
    if ($thumbnail) {
      $html .= '<div class="viral-story-image">';
      $html .= '<img src="' . esc_url($thumbnail) . '" alt="' . esc_attr($post->post_title) . '">';
      $html .= '</div>';
    }
    $html .= '<div class="viral-story-content">';
    $html .= '<div class="viral-story-title">' . esc_html($post->post_title) . '</div>';
    $html .= '<div class="viral-story-meta">' . human_time_diff(get_the_time('U', $post->ID), current_time('timestamp')) . ' ago</div>';
    $html .= '</div>';
    $html .= '</a>';
    $html .= '</div>';
  }

  $html .= '</div>';
  $html .= '</div>';

  return $html;
}

/**
 * Get top stories using Hacker News algorithm
 *
 * @param int $limit Number of stories to return
 * @return array Array of post objects
 */
function baum_get_top_stories_by_rank($limit = 5) {
  global $do_not_duplicate;

  $args = array(
    'post__not_in' => $do_not_duplicate ?: [],
    'post_type'      => 'post',
    'posts_per_page' => $limit,
    'meta_key'       => 'baum_rank',
    'orderby'        => 'meta_value_num',
    'order'          => 'DESC',
    'post_status'    => 'publish'
  );

  return get_posts($args);
}

/**
 * Get developer tools HTML content (legacy)
 *
 * @return string HTML content for developer tools dropdown
 */
function baum_get_developer_tools_html() {
  $dev_tools = baum_get_viral_tools(); // baum_get_developer_tools();

  $html = '<div class="developer-grid">';

  // Left Column - Main Tools
  $html .= '<div class="developer-main-tools">';
  foreach ($dev_tools['main_items'] as $tool) {
    $html .= '<div class="developer-tool-item">';
    $html .= '<a href="' . esc_url($tool['link']) . '" class="developer-tool-link">';

    // Handle icon - check if it's HTML (shortcode output) or CSS class
    $icon_html = '';
    if (strpos($tool['icon'], '<') !== false) {
      // It's HTML from shortcode
      $icon_html = $tool['icon'];
    } else {
      // It's a CSS class
      $icon_html = '<i class="' . esc_attr($tool['icon']) . '"></i>';
    }

    $html .= '<div class="developer-tool-icon ' . $tool['class'] . '">' . $icon_html . '</div>';
    $html .= '<div class="developer-tool-content">';
    $html .= '<div class="developer-tool-title">' . esc_html($tool['title']) . '</div>';
    $html .= '<div class="developer-tool-description">' . esc_html($tool['description']) . '</div>';
    $html .= '</div></a></div>';
  }
  $html .= '</div>';

  // Right Column - Top Stories by Rank using baum-card-mini styling
  $html .= '<div class="developer-trending-stories">';

  $top_stories = baum_get_top_stories_by_rank(5);
  $rank = 1;
  foreach ($top_stories as $story) {
    $thumbnail = get_the_post_thumbnail_url($story->ID, 'thumbnail');

    // Create baum-card-mini structure
    $html .= '<div class="baum-card-mini" style="filter:none;background:none;border:none;padding:0px;">';

    // Add rank number as a data attribute for styling
    $html .= '<div class="baum-card-body" data-rank="' . $rank . '">';


    if ($thumbnail) {
      $html .= '<div class="baum-card-img">';
      $html .= '<img src="' . esc_url($thumbnail) . '" alt="' . esc_attr($story->post_title) . '" width="64" height="64">';
      $html .= '<div class="developer-story-rank">' . $rank . '</div>';
      $html .= '</div>';
    }

    // Title first (left side)
    $html .= '<a href="' . esc_url(get_permalink($story->ID)) . '" class="post-title">';
    $html .= esc_html($story->post_title);
    $html .= '</a>';


    $html .= '</div>'; // End baum-card-body
    $html .= '</div>'; // End baum-card-mini

    $rank++;
  }

  $html .= '</div>';
  $html .= '</div>';

  return $html;
}

/**
 * Get viral tools configuration
 *
 * @return array Array of viral menu items
 */
function baum_get_viral_tools() {
  return [
    'main_items' => [
      [
        'title' => 'Trending', 
        'description' => 'Most popular posts by ranking algorithm', 
        'icon' => 'fas fa-fire', 
        'class' => 'bg-red', 
        'link' => '/trending'
      ],
      [
        'title' => 'Topics', 
        'description' => 'Browse topics and categories', 
        'icon' => 'fas fa-tags', 
        'class' => 'bg-orange', 
        'link' => '/topics' 
      ],
      [
        'title' => 'Email List', 
        'description' => 'Newsletter and subscription management', 
        'icon' => 'fas fa-envelope', 
        'class' => 'bg-pink', 
        'link' => '/subscribe' 
      ],
      [
        'title' => 'Realtime', 
        'description' => 'Synchronize and broadcast events', 
        'icon' => 'fas fa-microphone', 
        'class' => 'bg-purple', 
        'link' => '/realtime-tools' 
      ],
      [
        'title' => 'All',
        'icon' => 'fas fa-list',
        'class' => 'bg-blue', 
        'link' => '/'
      ]
    ]
  ];
}

/**
 * Get developer tools configuration (legacy)
 *
 * @return array Array of tool categories and items
 */
function baum_get_developer_tools() {
  return [
    'main_tools' => [
      // [
      //   'title' => 'Trending Stories',
      //   'description' => 'Most popular posts by ranking algorithm',
      //   'icon' => 'fas fa-fire',
      //   'link' => '/?trending=1'
      // ],
      [
        'title' => 'Explore',
        'description' => 'Browse topics and categories',
        'icon' => 'fas fa-compass',
        'link' => '/topics'
      ],
      [
        'title' => 'Email Lists',
        'description' => 'Newsletter and subscription management',
        'icon' => 'fas fa-envelope',
        'link' => '/wp-admin/upload.php'
      ],
      // [
      //   'title' => 'Starred',
      //   'description' => 'Your bookmarked and favorite content',
      //   'icon' => 'fas fa-' . do_shortcode('[baum_bookmark_icon_text]'),
      //   'link' => '/?starred=1'
      // ],
      [
        'title' => 'Realtime',
        'description' => 'Synchronize and broadcast events',
        'icon' => 'fas fa-microphone',
        'link' => '/wp-admin/admin.php?page=realtime-tools'
      ]
    ],
    'modules' => [
      [
        'title' => 'Vector',
        'description' => 'AI toolkit to manage embeddings',
        'icon' => 'fas fa-vector-square',
        'link' => '/wp-admin/admin.php?page=vector-tools'
      ],
      [
        'title' => 'Cron',
        'description' => 'Schedule and manage recurring Jobs',
        'icon' => 'fas fa-clock',
        'link' => '/wp-admin/admin.php?page=cron-jobs'
      ],
      [
        'title' => 'Queues',
        'description' => 'Durable Message Queues with guaranteed delivery',
        'icon' => 'fas fa-list',
        'link' => '/wp-admin/admin.php?page=queue-management'
      ],
      [
        'title' => 'Features',
        'description' => 'Explore everything you can do with Supabase.',
        'icon' => 'fas fa-star',
        'link' => '/wp-admin/admin.php?page=feature-explorer'
      ]
    ],
    'customer_story' => [
      'company' => 'kayhan.space',
      'description' => 'Kayhan Space saw 8x improvement in developer speed when moving to Supabase'
    ],
    'compare_links' => [
      'Supabase vs Firebase',
      'Supabase vs Heroku Postgres',
      'Supabase vs Auth0'
    ],
    'solutions' => [
      'AI Builders'
    ]
  ];
}


add_filter('widget_update_callback', function($instance, $new_instance, $old_instance, $widget) {
  if (isset($_POST['acf'])) {
      foreach ($_POST['acf'] as $key => $value) {
          $field = acf_get_field($key);
          if ($field && isset($field['name'])) {
              $instance[$field['name']] = $value;
          }
      }
  }
  return $instance;
}, 10, 4);


add_filter('acf/load_value', function($value, $post_id, $field) {
  if (strpos($post_id, 'widget_') === 0 && is_array($GLOBALS['acf_widget_instance'] ?? null)) {
      $field_name = $field['name'];
      if (isset($GLOBALS['acf_widget_instance'][$field_name])) {
          return $GLOBALS['acf_widget_instance'][$field_name];
      }
  }
  return $value;
}, 10, 3);





// add_filter( 'heartbeat_send', 'custom_heartbeat_send_filter', 10, 2 );

// function custom_heartbeat_send_filter( $response, $data ) {
//     // Optional: Modify or monitor heartbeat data
//     return $response;
// }

// add_filter( 'heartbeat_settings', 'custom_heartbeat_settings' );

// function custom_heartbeat_settings( $settings ) {
//     // Change interval in seconds (default is 15)
//     $settings['interval'] = 60; // One ping per minute
//     return $settings;
// }


// function remove_bbp_keymaster_from_all_users() {
//   global $wpdb;
//   $capabilities_meta_key = $wpdb->prefix . 'capabilities';

//   // Get all users
//   $users = get_users();

//   foreach ( $users as $user ) {
//       // Get the user's capabilities array
//       $caps = get_user_meta( $user->ID, $capabilities_meta_key, true );

//       // If it's an array and has the bbp_keymaster key, remove it
//       if ( is_array( $caps ) && isset( $caps['bbp_keymaster'] ) ) {
//           unset( $caps['bbp_keymaster'] );
//           update_user_meta( $user->ID, $capabilities_meta_key, $caps );
//       }
//   }
// }
// // Hook the function to run in the admin area. Once you have run it and verified the changes, remove or comment out this code.
// add_action( 'admin_init', 'remove_bbp_keymaster_from_all_users' );


/////////////////////////////////////
// Apple / Baum Menu
/////////////////////////////////////

function baum_enqueue_apple_menu_styles () {
  // wp_enqueue_style(
  //   'baum-apple-menu-style',
  //   get_template_directory_uri() . '/css/apple-menu.css'
  // );
  wp_enqueue_script(
    'baum-apple-menu-script',
    get_template_directory_uri() . '/js/apple-menu.js',
    ['jquery'],
    null,
    true
  );
}

// add_action('wp_enqueue_scripts', 'baum_enqueue_apple_menu_styles');

/////////////////////////////////////
// Baum Test Cron Functionality
/////////////////////////////////////

// if (!wp_next_scheduled('baum_test_cron_hook')) {
//   wp_schedule_event(time(), 'hourly', 'baum_test_cron_hook');
// }

// add_action('baum_test_cron_hook', function () {
//   error_log('Baum Test Cron Hook Executed.');
// });

/////////////////////////////////////
// Baum's Sign Out localization triggers deletion of localStorage data
/////////////////////////////////////

// function baum_set_logout_flag ($arg) {
//   error_log('Logging out...' . $arg);
//   $user_id = get_current_user_id();
//   if ($user_id) {
//     update_user_meta($user_id, 'baum_logged_out', '1');
//     error_log('Logged Out flag set: ' . $user_id);
//   }
// }

// add_action('wp_logout', 'baum_set_logout_flag', 0, 1);

// function baum_clear_all_cache_and_transients() {
//   global $wpdb;

//   // Delete all transients
//   $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%'" );

//   // Delete all site transients (for multisite setups)
//   $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_site_transient_%'" );

//   // Optionally delete object cache keys (if you're using an external object cache like Redis or Memcached)
//   if ( function_exists( 'wp_cache_flush' ) ) {
//       wp_cache_flush(); // Clear object cache
//   }

//   // Delete any caching plugin leftovers
//   delete_option( 'wp_cache' ); // WP Super Cache
//   delete_option( 'w3tc_config' ); // W3 Total Cache
//   delete_option( 'advanced_cache' );
//   delete_option( 'autoptimize_css' );
//   delete_option( 'autoptimize_js' );
//   delete_option( 'autoptimize_html' );

//   echo "All transients, caches, and related options have been flushed!";
// }

// Run it once
// baum_clear_all_cache_and_transients();

/**
 * Clean up unused theme_mods from the options table
 *
 * This function removes unused/obsolete keys from the theme_mods_baumpress
 * serialized data in the wp_options table to keep it clean and optimized.
 *
 * @param bool $dry_run If true, only shows what would be removed without actually removing
 * @return array Array of removed keys and statistics
 *
 * @since 1.0.0
 */
function baum_cleanup_theme_mods($dry_run = true) {
  // Get current theme mods
  $theme_mods = get_theme_mods();

  if (empty($theme_mods)) {
    return ['message' => 'No theme mods found.', 'removed' => [], 'count' => 0];
  }

  // Define valid/active theme mod keys that should be kept
  $valid_keys = [
    // === Core WordPress ===
    'custom_logo',
    'custom_logo_dark',
    'header_textcolor',
    'background_color',
    'header_image',
    'header_image_data',
    'nav_menu_locations',
    'sidebars_widgets',

    // === BaumPress General Settings ===
    'baum_theme',
    'baum_about',
    'baum_name',
    'baum_email',
    'baum_position',
    'baum_address',
    'baum_contact',

    // === Logo & Branding ===
    'baum_logo',
    'baum_logo_dark',
    'baum_logo_app',
    'baum_logo_app_dark',
    'baum_icon',
    'baum_icon_dark',

    // === New Logo Types ===
    'baum_logo_black_on_white',
    'baum_logo_white_on_black',
    'baum_logo_monochrome',
    'baum_logo_horizontal',
    'baum_logo_vertical',
    'baum_logo_square',
    'baum_logo_favicon',
    'baum_logo_watermark',
    'baum_logo_print',
    'baum_logo_email',

    // === Advanced Kirki Logo System ===
    'baum_masthead_logo_light',
    'baum_masthead_logo_dark',
    'baum_footer_logo_light',
    'baum_footer_logo_dark',
    'baum_primary_logo_light',
    'baum_primary_logo_dark',
    'baum_site_icon_light',
    'baum_site_icon_dark',
    'baum_masthead_logo_width',
    'baum_footer_logo_width',
    'baum_primary_logo_width',

    // === Social Media ===
    'baum_facebook',
    'baum_twitter',
    'baum_instagram',
    'baum_linkedin',
    'baum_youtube',
    'baum_github',
    'baum_discord',
    'baum_telegram',
    'baum_whatsapp',
    'baum_email_social',

    // === Typography ===
    'baum_font_body',
    'baum_font_headings',

    // === Color Settings ===
    'baum_color_primary',
    'baum_color_accent',
    'baum_color_background',
    'baum_color_text',
    'baum_color_masthead_bg',
    'baum_color_nav_bg',
    'baum_color_nav_text',
    'baum_color_nav_text_hover',
    'baum_color_footer_bg',
    'baum_color_footer_text',
    'baum_color_footer_link',
    'baum_color_footer_link_hover',
    'baum_color_category_nav_bg',
    'baum_color_category_nav_text',
    'baum_color_category_nav_text_hover',

    // === Theme System ===
    'baum_custom_theme_selector',
    'baum_dark_mode',
    'baum_darkmode',
    'baum_site_title_color',

    // === Layout Settings ===
    'baum_layout_sidebar',
    'baum_layout_header',
    'baum_layout_footer',

    // === Logo Sizes ===
    'baum_footer_logo_size',
    'baum_home_logo_size',
    'baum_center_logo_size',
    'baum_logo_size',

    // === Images Settings ===
    'baum_image_quality',
    'baum_image_compression',

    // === Bookmark Settings ===
    'baum_bookmark_icon',

    // === Navigation Settings ===
    'baum_nav_style',
    'baum_nav_position',

    // === Footer Settings ===
    'baum_footer_text',
    'baum_footer_copyright',
    'baum_copyright', // This is what footer.php actually uses

    // === SEO Settings ===
    'baum_meta_description',
    'baum_meta_keywords',

    // === Performance Settings ===
    'baum_enable_caching',
    'baum_minify_css',
    'baum_minify_js',
  ];

  // Add dynamic theme color keys
  $color_themes = include get_stylesheet_directory() . '/inc/baum-color-themes.php';
  if (isset($color_themes['themes'])) {
    foreach ($color_themes['themes'] as $theme_name => $modes) {
      foreach ($modes as $mode => $colors) {
        foreach ($colors as $key => $value) {
          if ($key === 'color' && is_array($value)) {
            foreach ($value as $sub_key => $sub_value) {
              $valid_keys[] = "baum_{$theme_name}_{$mode}_color_{$sub_key}";
            }
          } else {
            $valid_keys[] = "baum_{$theme_name}_{$mode}_{$key}";
          }
        }
      }
    }
  }

  // Find keys to remove
  $keys_to_remove = [];
  $total_keys = count($theme_mods);

  foreach ($theme_mods as $key => $value) {
    if (!in_array($key, $valid_keys)) {
      $keys_to_remove[] = $key;
    }
  }

  $result = [
    'total_keys' => $total_keys,
    'valid_keys' => count($valid_keys),
    'keys_to_remove' => $keys_to_remove,
    'removal_count' => count($keys_to_remove),
    'dry_run' => $dry_run
  ];

  if ($dry_run) {
    $result['message'] = 'DRY RUN: No changes made. Found ' . count($keys_to_remove) . ' keys that would be removed.';
    return $result;
  }

  // Actually remove the keys
  $removed_keys = [];
  foreach ($keys_to_remove as $key) {
    remove_theme_mod($key);
    $removed_keys[] = $key;
  }

  $result['removed_keys'] = $removed_keys;
  $result['message'] = 'Successfully removed ' . count($removed_keys) . ' unused theme mod keys.';

  return $result;
}

/**
 * Display theme mods cleanup results in a readable format
 *
 * @param bool $dry_run Whether to perform a dry run
 * @return void
 */
function baum_display_theme_mods_cleanup($dry_run = true) {
  $result = baum_cleanup_theme_mods($dry_run);

  echo "<div style='background: #f1f1f1; padding: 20px; border-radius: 8px; margin: 20px 0; font-family: monospace;'>";
  echo "<h3 style='color: #333; margin-top: 0;'>🧹 Theme Mods Cleanup Report</h3>";

  echo "<p><strong>Status:</strong> " . ($dry_run ? "DRY RUN (Preview Only)" : "LIVE CLEANUP") . "</p>";
  echo "<p><strong>Total Keys:</strong> " . $result['total_keys'] . "</p>";
  echo "<p><strong>Valid Keys:</strong> " . $result['valid_keys'] . "</p>";
  echo "<p><strong>Keys to Remove:</strong> " . $result['removal_count'] . "</p>";

  if (!empty($result['keys_to_remove'])) {
    echo "<h4 style='color: #d63638; margin-bottom: 10px;'>Keys " . ($dry_run ? "to be removed" : "removed") . ":</h4>";
    echo "<ul style='background: #fff; padding: 15px; border-radius: 4px; max-height: 300px; overflow-y: auto;'>";
    foreach ($result['keys_to_remove'] as $key) {
      echo "<li style='color: #d63638; margin-bottom: 5px;'>" . esc_html($key) . "</li>";
    }
    echo "</ul>";
  } else {
    echo "<p style='color: #46b450;'>✅ No unused keys found! Theme mods are clean.</p>";
  }

  echo "<p style='margin-bottom: 0;'><strong>Result:</strong> " . $result['message'] . "</p>";
  echo "</div>";
}

/**
 * Admin page for theme mods cleanup
 * Add this to wp-admin for easy access
 */
function baum_theme_mods_cleanup_admin_page() {
  add_management_page(
    'Theme Mods Cleanup',
    'Theme Mods Cleanup',
    'manage_options',
    'baum-theme-mods-cleanup',
    'baum_theme_mods_cleanup_page_content'
  );
}

/**
 * Content for the theme mods cleanup admin page
 */
function baum_theme_mods_cleanup_page_content() {
  echo '<div class="wrap">';
  echo '<h1>🧹 BaumPress Theme Mods Cleanup</h1>';
  echo '<p>This tool helps you clean up unused theme modification keys from the database.</p>';

  // Handle form submission
  if (isset($_POST['cleanup_action'])) {
    $dry_run = $_POST['cleanup_action'] === 'preview';
    baum_display_theme_mods_cleanup($dry_run);
  } else {
    // Show initial form
    echo '<form method="post" style="margin: 20px 0;">';
    echo '<p>';
    echo '<input type="submit" name="cleanup_action" value="preview" class="button button-secondary" style="margin-right: 10px;"> Preview Changes (Safe)';
    echo '<input type="submit" name="cleanup_action" value="cleanup" class="button button-primary" onclick="return confirm(\'Are you sure you want to remove unused theme mods? This action cannot be undone.\')"> Clean Up Now';
    echo '</p>';
    echo '</form>';

    // Show current stats
    $stats = baum_cleanup_theme_mods(true);
    echo '<div style="background: #e7f3ff; padding: 15px; border-radius: 6px; border-left: 4px solid #0073aa;">';
    echo '<h3 style="margin-top: 0;">📊 Current Statistics</h3>';
    echo '<p><strong>Total Theme Mods:</strong> ' . $stats['total_keys'] . '</p>';
    echo '<p><strong>Unused Keys Found:</strong> ' . $stats['removal_count'] . '</p>';
    echo '</div>';
  }

  echo '</div>';
}

// Uncomment to add admin page (optional)
// TEMPORARILY DISABLED - TESTING FOR MEMORY ISSUES
// add_action('admin_menu', 'baum_theme_mods_cleanup_admin_page');

/**
 * Quick access functions for theme mods cleanup
 * Use these in wp-admin or via WP-CLI
 */

/**
 * Preview what would be cleaned up (safe to run)
 * Usage: Call this function to see what would be removed
 */
function baum_preview_cleanup() {
  return baum_cleanup_theme_mods(true);
}

/**
 * Actually perform the cleanup (removes data!)
 * Usage: Only call this after reviewing the preview
 */
function baum_perform_cleanup() {
  return baum_cleanup_theme_mods(false);
}

/**
 * Get current theme mods statistics
 */
function baum_get_theme_mods_stats() {
  $theme_mods = get_theme_mods();
  $cleanup_result = baum_cleanup_theme_mods(true);

  return [
    'total_theme_mods' => count($theme_mods),
    'unused_keys' => $cleanup_result['removal_count'],
    'valid_keys' => $cleanup_result['total_keys'] - $cleanup_result['removal_count'],
    'database_size_estimate' => strlen(serialize($theme_mods)) . ' bytes'
  ];
}

// === USAGE EXAMPLES ===
//
// To preview cleanup (safe):
// $preview = baum_preview_cleanup();
// var_dump($preview);
//
// To see current stats:
// $stats = baum_get_theme_mods_stats();
// var_dump($stats);
//
// To actually clean up (removes data!):
// $result = baum_perform_cleanup();
// var_dump($result);

/**
 * Temporary function to show current theme mods status
 * Remove this after cleanup is complete
 */
function baum_show_current_theme_mods_status() {
  $theme_mods = get_theme_mods();
  $stats = baum_get_theme_mods_stats();

  echo "<div style='background: #f9f9f9; padding: 20px; margin: 20px; border-radius: 8px; font-family: monospace;'>";
  echo "<h3>🔍 Current Theme Mods Status</h3>";
  echo "<p><strong>Total Keys:</strong> " . $stats['total_theme_mods'] . "</p>";
  echo "<p><strong>Valid Keys:</strong> " . $stats['valid_keys'] . "</p>";
  echo "<p><strong>Unused Keys:</strong> " . $stats['unused_keys'] . "</p>";
  echo "<p><strong>Database Size:</strong> " . $stats['database_size_estimate'] . "</p>";

  if ($stats['unused_keys'] > 0) {
    echo "<p style='color: #d63638;'><strong>⚠️ Found " . $stats['unused_keys'] . " unused keys that can be cleaned up!</strong></p>";
    echo "<p>To preview what would be removed, call: <code>baum_preview_cleanup()</code></p>";
    echo "<p>To actually clean up, call: <code>baum_perform_cleanup()</code></p>";
  } else {
    echo "<p style='color: #46b450;'><strong>✅ No cleanup needed! All keys are valid.</strong></p>";
  }
  echo "</div>";
}

// Uncomment to show current status
// baum_show_current_theme_mods_status();

// $preview = baum_preview_cleanup();
// var_dump($preview);

// Uncomment to run cleanup once (CAREFUL!)
// baum_display_theme_mods_cleanup(true); // Dry run first
// baum_display_theme_mods_cleanup(false); // Actual cleanup
// $result = baum_perform_cleanup();
// var_dump($result);

// /**
//  * EMERGENCY RESTORE FUNCTION
//  * Restores essential theme mods that might have been accidentally removed
//  */
// function baum_emergency_restore_theme_mods() {
//   // Restore essential navigation and footer settings
//   $essential_defaults = [
//     // Navigation settings
//     'nav_menu_locations' => get_nav_menu_locations(),

//     // Footer settings - comprehensive restore
//     'baum_footer_text' => get_bloginfo('name'),
//     'baum_footer_copyright' => '© ' . date('Y') . ' ' . get_bloginfo('name'),
//     'baum_about' => 'Welcome to ' . get_bloginfo('name') . '. We provide quality content and news.',
//     'baum_footer_bg' => '#333333',
//     'baum_footer_text_color' => '#ffffff',
//     'baum_show_footer' => true,
//     'baum_footer_widgets' => true,

//     // Logo settings (restore to WordPress defaults if missing)
//     'custom_logo' => get_theme_mod('custom_logo', ''),

//     // Color settings (restore to theme defaults)
//     'baum_custom_theme_selector' => 'default',
//     'baum_dark_mode' => 'auto',
//     'baum_color_footer_bg' => '#2c3e50',
//     'baum_color_footer_text' => '#ffffff',
//     'baum_color_footer_link' => '#3498db',
//     'baum_color_footer_link_hover' => '#2980b9',

//     // Layout settings
//     'baum_layout_sidebar' => 'right',
//     'baum_layout_header' => 'default',
//     'baum_layout_footer' => 'default',

//     // Widget settings
//     'sidebars_widgets' => get_option('sidebars_widgets', []),
//   ];

//   $restored = [];
//   foreach ($essential_defaults as $key => $default_value) {
//     $current_value = get_theme_mod($key);
//     if (empty($current_value) && !empty($default_value)) {
//       set_theme_mod($key, $default_value);
//       $restored[] = $key;
//     }
//   }

//   // Force refresh menus
//   wp_cache_delete('theme_mods_' . get_option('stylesheet'), 'theme_mods');

//   return [
//     'restored_keys' => $restored,
//     'message' => 'Restored ' . count($restored) . ' essential theme mods'
//   ];
// }

// /**
//  * Quick fix for broken menu and footer
//  */
// function baum_quick_fix_menu_footer() {
//   // Restore menu locations using the correct theme menu locations
//   $locations = get_nav_menu_locations();
//   $theme_locations = get_registered_nav_menus();

//   if (empty($locations) || count($locations) === 0) {
//     // Get available menus
//     $menus = wp_get_nav_menus();
//     if (!empty($menus)) {
//       $primary_menu = $menus[0];

//       // Set up the correct menu locations for this theme
//       $new_locations = [];
//       foreach ($theme_locations as $location => $description) {
//         $new_locations[$location] = $primary_menu->term_id;
//       }

//       // Use WordPress function to set nav menu locations
//       set_theme_mod('nav_menu_locations', $new_locations);

//       // Also try the direct WordPress way
//       $current_locations = get_nav_menu_locations();
//       foreach ($theme_locations as $location => $description) {
//         $current_locations[$location] = $primary_menu->term_id;
//       }
//       set_theme_mod('nav_menu_locations', $current_locations);
//     }
//   }

//   // Restore footer text if missing
//   if (empty(get_theme_mod('baum_footer_text'))) {
//     set_theme_mod('baum_footer_text', get_bloginfo('name'));
//   }

//   if (empty(get_theme_mod('baum_footer_copyright'))) {
//     set_theme_mod('baum_footer_copyright', '© ' . date('Y') . ' ' . get_bloginfo('name'));
//   }

//   // This is the key that footer.php actually uses!
//   if (empty(get_theme_mod('baum_copyright'))) {
//     set_theme_mod('baum_copyright', '© ' . date('Y') . ' ' . get_bloginfo('name'));
//   }

//   // Restore about text if missing
//   if (empty(get_theme_mod('baum_about'))) {
//     set_theme_mod('baum_about', 'Welcome to ' . get_bloginfo('name') . '. We provide quality content and news.');
//   }

//   // Clear any cached theme mods and menus
//   wp_cache_flush();
//   wp_cache_delete('theme_mods_' . get_option('stylesheet'), 'theme_mods');

//   return 'Menu and footer settings restored!';
// }

// /**
//  * Specific footer restoration function
//  */
// function baum_restore_footer_completely() {
//   // Force restore all footer-related theme mods
//   $footer_settings = [
//     'baum_about' => 'Welcome to ' . get_bloginfo('name') . '. We provide quality content and news.',
//     'baum_footer_text' => get_bloginfo('name'),
//     'baum_footer_copyright' => '© ' . date('Y') . ' ' . get_bloginfo('name'),
//     'baum_copyright' => '© ' . date('Y') . ' ' . get_bloginfo('name'), // This is what footer.php actually uses!
//     'baum_color_footer_bg' => '#2c3e50',
//     'baum_color_footer_text' => '#ffffff',
//     'baum_color_footer_link' => '#3498db',
//     'baum_color_footer_link_hover' => '#2980b9',
//   ];

//   foreach ($footer_settings as $key => $value) {
//     set_theme_mod($key, $value);
//   }

//   // Ensure footer widgets are properly set up
//   $sidebars_widgets = get_option('sidebars_widgets', []);
//   if (empty($sidebars_widgets['baum-footer-widget'])) {
//     // Create a default footer widget setup
//     $sidebars_widgets['baum-footer-widget'] = [];
//     update_option('sidebars_widgets', $sidebars_widgets);
//   }

//   // Clear all caches
//   wp_cache_flush();
//   delete_transient('baum_footer_cache');

//   return 'Footer completely restored!';
// }

// EMERGENCY RESTORE - Run this to restore essential settings
// baum_emergency_restore_theme_mods();
// baum_quick_fix_menu_footer();
// baum_restore_footer_completely();

function baum_enqueue_and_localize_script () {
  //
  // wp_script_is(): Checks the status of the script. The key statuses to check are:
  //
  // 'enqueued' (true when the script is enqueued)
  // 'registered' (true when the script is registered but not necessarily enqueued)
  // 'done' (true when the script has already been printed to the page)
  // 'to_do' (true when the script is queued for output)
  //
  // Check if 'baum-index-js' has been enqueued successfully



  // if (wp_script_is('baum-index-js', 'enqueued') || wp_script_is('baum-index-js', 'to_do')) {
  //   // Get the current user ID
  //   $user_id = get_current_user_id();
  //   $logged_out = get_user_meta($user_id, 'baum_logged_out', true);

  //   if ($logged_out) {
  //     error_log('Logged out: ' . $user_id);
  //     // Localize the script with logout true
  //     wp_localize_script('baum-index-js', 'wp_object', [
  //       'wp_logout'      => true,
  //       'ajaxurl'        => admin_url('admin-ajax.php'),
  //       'tooltipster'    => [
  //         'animation'      => 'grow',
  //         'theme'          => 'tooltipster-borderless',
  //         'interactive'    => true,
  //         'contentAsHTML'  => true,
  //       ],
  //     ]);
  //     delete_user_meta($user_id, 'baum_logged_out'); // Clear the flag after use
  //   } else {
  //     error_log('Not logged out: ' . $user_id);
  //     // Localize the script normally with logout false
  //     wp_localize_script('baum-index-js', 'wp_object', [
  //       'wp_logout'      => false,
  //       'ajaxurl'        => admin_url('admin-ajax.php'),
  //       'tooltipster'    => [
  //         'animation'      => 'grow',
  //         'theme'          => 'tooltipster-borderless',
  //         'interactive'    => true,
  //         'contentAsHTML'  => true,
  //       ],
  //     ]);
  //   }
  // }


  // else if (wp_script_is('baum-index-js', 'registered')) {
  //   error_log('Enqueque script: baum-index-js');
  //   wp_enqueue_script('baum-index-js');
  // }
  // else if (wp_script_is('baum-index-js', 'done')) {
  //   error_log('Error! the baum-index-js script got done before we could localize wp_logout');
  // }

  // wp_enqueue_script(
  //   'baum-index-js',
  //   get_template_directory_uri() . '/js/index.js',
  //   ['baum-tooltips-js'],
  //   '1.0',
  //   true,
  //   // true
  // );
  // Always enqueue the script as it might be used independently of logout action
  // wp_enqueue_script('baum-index-js', 'path/to/baum-index.js', array('jquery'), null, true);

  // Check if the current user has the logout flag set
  // $user_id = get_current_user_id();
  // $logged_out = get_user_meta($user_id, 'baum_logged_out', true);

  // if ($logged_out) {
  //   // Localize the script with logout true
  //   wp_localize_script('baum-index-js', 'wp_object', ['wp_logout' => true]);
  //   delete_user_meta($user_id, 'baum_logged_out'); // Clear the flag after use
  // } else {
  //   // Localize normally
  //   wp_localize_script('baum-index-js', 'wp_object', ['wp_logout' => false]);
  // }
}

add_action('wp_enqueue_scripts', 'baum_enqueue_and_localize_script');


  // function baum_localize_wp_object ($arg) {
  //   error_log('localize baum-index-js');
  //   print_r($arg);
  //   wp_localize_script('baum-index', 'wp_object', ['wp_logout' => true]);
  // }

  // add_action('wp_logout', 'baum_localize_wp_object', 10, 1);


  // error_log('add_action wp_logout');



// TEMPORARILY DISABLED - CAUSING MEMORY EXHAUSTION
if (is_admin()) {
  require_once get_template_directory() . '/functions-admin.php';
}

require_once get_template_directory() . '/functions-ajax.php';
require_once get_template_directory() . '/functions-blocks.php';
require_once get_template_directory() . '/functions-comments.php';
require_once get_template_directory() . '/functions-gamipress.php';
require_once get_template_directory() . '/functions-images.php';
require_once get_template_directory() . '/functions-notifications.php';
require_once get_template_directory() . '/functions-pages.php';
require_once get_template_directory() . '/functions-shortcodes.php';
require_once get_template_directory() . '/functions-widgets.php';
require_once get_template_directory() . '/functions-rendition.php';

/**
 * Enqueue WordPress block editor packages for frontend editor page
 *
 * This function loads all the necessary WordPress packages to make
 * the block editor work on the frontend editor page template.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_enqueue_frontend_gutenberg_packages() {
  // Only load on the editor page template
  if (!is_page_template('page-editor-gutenberg.php')) {
    return;
  }

  // Core WordPress packages needed for block editor
  $packages = array(
    'wp-element',
    'wp-components',
    'wp-blocks',
    'wp-block-editor',
    'wp-data',
    'wp-dom-ready',
    'wp-block-library',
    'wp-format-library',
    'wp-editor',
    'wp-api-fetch',
    'wp-url',
    'wp-html-entities',
    'wp-i18n',
    'wp-hooks',
    'wp-media-utils',
    'wp-notices',
    'wp-keycodes',
    'wp-rich-text',
    'wp-compose'
  );

  // Enqueue all packages
  foreach ($packages as $package) {
    wp_enqueue_script($package);
  }

  // Enqueue WordPress media scripts (needed for image uploads)
  wp_enqueue_media();
  wp_enqueue_script('media-upload');
  wp_enqueue_script('media-views');
  wp_enqueue_script('media-editor');
  wp_enqueue_script('media-audiovideo');

  // Enqueue block editor styles
  wp_enqueue_style('wp-edit-blocks');
  wp_enqueue_style('wp-block-library');
  wp_enqueue_style('wp-block-library-theme');
  wp_enqueue_style('wp-components');
  wp_enqueue_style('wp-editor');

  // Editor settings with media support
  $settings = array(
    'alignWide' => true,
    'allowedMimeTypes' => get_allowed_mime_types(),
    'bodyPlaceholder' => __('Start writing or type / to choose a block'),
    'titlePlaceholder' => __('Add title'),
    'isRTL' => is_rtl(),
    'maxWidth' => 580,
    'styles' => array(
      array(
        'css' => 'body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }'
      )
    ),
    'defaultBlock' => array(
      'name' => 'core/paragraph',
      'attributes' => array()
    ),
    'supportsLayout' => true,
    'supportsTemplateMode' => false,
    // Media upload settings
    'mediaUpload' => true,
    'allowedBlockTypes' => true,
    'hasFixedToolbar' => false,
    'focusMode' => false,
    'hasPermissionsToManageWidgets' => current_user_can('edit_theme_options'),
    'imageSizes' => wp_get_additional_image_sizes(),
    'maxUploadFileSize' => wp_max_upload_size(),
    // REST API settings for media
    'restNonce' => wp_create_nonce('wp_rest'),
    'restUrl' => rest_url(),
  );

  // Inline editor settings and block registration
  wp_add_inline_script(
    'wp-dom-ready',
    'document.addEventListener("DOMContentLoaded", function() {
      window.baumEditorSettings = ' . wp_json_encode($settings) . ';

      // Wait for WordPress packages to be available
      function initializeBlocks() {
        if (typeof wp === "undefined" || !wp.blockLibrary || !wp.domReady) {
          console.log("Waiting for WordPress packages...");
          setTimeout(initializeBlocks, 100);
          return;
        }

        wp.domReady(function() {
          if (wp.blockLibrary && wp.blockLibrary.registerCoreBlocks) {
            wp.blockLibrary.registerCoreBlocks();
            console.log("WordPress core blocks registered for frontend editor");
          } else {
            console.error("wp.blockLibrary.registerCoreBlocks not available");
          }
        });
      }

      initializeBlocks();
    });',
    'after'
  );
}
add_action('wp_enqueue_scripts', 'baum_enqueue_frontend_gutenberg_packages');


function baum_add_roles () {
  // Administrator role is built into WordPress by default
  // Editor role is built into WordPress by default
  // Author role is built into WordPress by default
  // Contributor role is built into WordPress by default
  // Subscriber role is built into WordPress by default
  // Influencer must be created
  // Marketer role must be created
  // Reader role must be created

  //
  // Add Custom Baum Press Roles
  //

  if (get_option('baum_roles_version') < 2) {

    //
    // `influencer` role is a custom role by Baum Press
    //

    add_role(
      'influencer',
      __('Influencer'),
      [
        'read'         => true,
        'comment'      => true,
        'edit_posts'   => false,
        'upload_files' => true,
      ]
    );

    //
    // `marketer` role is a custom role by Baum Press
    //

    add_role(
      'marketer',
      __('Marketer'),
      [
        'read'         => true,
        'comment'      => true,
        'edit_posts'   => false,
        'upload_files' => true,
      ]
    );

    //
    // `reader` role is a custom role by Baum Press
    //

    add_role(
      'reader',
      __('Reader'),
      [
        'read'         => true,
        'comment'      => true,
        'edit_posts'   => false,
        'upload_files' => true,
      ]
    );
    update_option('baum_roles_version', 2);
  }
}

add_action('init', 'baum_add_roles');

/**
 * Renames WordPress default user roles to more publication-appropriate names
 *
 * This function customizes the display names of WordPress user roles to better
 * fit a publishing or news site context. It changes 'Administrator' to 'Chief Editor'
 * and 'Author' to 'Staff Writer', while keeping other roles with their original names.
 *
 * @return void
 *
 * @since 1.0.0
 */
function rename_wordpress_roles() {
  global $wp_roles;

  // Rename roles
  if (isset($wp_roles->roles['administrator'])) {
      $wp_roles->roles['administrator']['name'] = 'Chief Editor';
      $wp_roles->role_names['administrator'] = 'Chief Editor';
  }
  if (isset($wp_roles->roles['editor'])) {
      $wp_roles->roles['editor']['name'] = 'Editor';
      $wp_roles->role_names['editor'] = 'Editor';
  }
  if (isset($wp_roles->roles['author'])) {
      $wp_roles->roles['author']['name'] = 'Staff Writer';
      $wp_roles->role_names['author'] = 'Staff Writer';
  }
  if (isset($wp_roles->roles['contributor'])) {
      $wp_roles->roles['contributor']['name'] = 'Contributor';
      $wp_roles->role_names['contributor'] = 'Contributor';
  }
  if (isset($wp_roles->roles['subscriber'])) {
      $wp_roles->roles['subscriber']['name'] = 'Site Member';
      $wp_roles->role_names['subscriber'] = 'Site Member';
  }
}

add_action('init', 'rename_wordpress_roles');


/**
 * Adds the site icon to the end of the last paragraph in the content.
 *
 * This searches for the last occurrence of a closing paragraph tag `</p>`
 * within the content and appends the site icon just before it. If the content
 * does not contain any paragraph tags, the content is returned unchanged.
 *
 * The site icon is displayed with inline styles for alignment and spacing.
 * The function is hooked into the 'the_content' filter and is applied to
 * singular posts/pages during the main query.
 *
 * @param string $the_content The original content of the post.
 * @return string Modified content with site icon appended to last paragraph.
 *
 * @hook the_content
 */

function add_site_icon_to_the_content ($the_content) {
    global $page, $numpages;

    // Check if we're on the last page of a paginated post
    if ($page !== $numpages) {
      return $the_content; // If not the last page, return content unaltered
    }

  $css = '
    background:none;
    display:inline-block;
    height:20px;
    width:auto;
    vertical-align:text-bottom;
    margin-left: 2.5px;
    margin-right: 2.5px;
  ';

  if (is_singular() && is_main_query()) {
    // Search for the last occurrence of </p> in the content
    $p_end_pos = strrpos($the_content, '</p>');

    if ($p_end_pos !== false) {
      // Split the content at the position of the last </p>
      $before_last_p = substr($the_content, 0, $p_end_pos);
      $after_last_p = substr($the_content, $p_end_pos);

      // Add the site icon before the final </p>
      $site_icon = '<img style="' . $css
        . '" src="' . get_site_icon_url()
        . '" alt="' . esc_attr(get_bloginfo('name', 'display'))
        . '" title="' . esc_attr(get_bloginfo('name', 'display')) . '">';

      // Rebuild the content with the icon inside the last paragraph
      $the_content = $before_last_p . $site_icon . $after_last_p;
    }
  }

  return $the_content;
}

add_filter('the_content', 'add_site_icon_to_the_content');






// function add_site_icon_to_the_content ($the_content) {
//   $css = 'background:none;display:inline-block;height:22px;width:auto;vertical-align:text-bottom;margin-left:5px;margin-right:5px;';
//   if (is_singular() && is_main_query()) {
//     $str = rtrim($the_content); // trimn white space from end of the content
//     $p_end = substr($str, -4); // grab the last 4 chars of the content
//     if ($p_end == '</p>') { // if the chars are </p>
//       $str = substr($str, 0, -4); // remove the </p> at the end of the content

//       // add the site icon inline inside the final paragraph
//       $str = $str . '<img style="' . $css . '" src="' . get_site_icon_url()
//         . '" alt="' . esc_attr(get_bloginfo('name', 'display'))
//         . '" title="' . esc_attr(get_bloginfo('name', 'display')) . '">';
//       $str = $str . '</p>'; // add the final </p> back to the content
//       return $str;
//     }
//   }
//   return $the_content;
// }

// add_filter('the_content', 'add_site_icon_to_the_content');







// function baum_remove_default_comment_textarea() {
//   remove_meta_box('commentstatusdiv', 'comment', 'normal');
// }
// add_action('add_meta_boxes_comment', 'baum_remove_default_comment_textarea');



// function debug_edit_comment_form_hooks() {
//   global $wp_filter;
//   if (isset($wp_filter['edit_comment_form'])) {
//       var_dump($wp_filter['edit_comment_form']);
//   }
// }
// add_action('admin_footer', 'debug_edit_comment_form_hooks');


// /////////////////////////////////////
// // Order Pages By Date in Admin
// /////////////////////////////////////

// function set_post_order_in_admin ($wp_query) {
//   global $pagenow;
//   if (is_admin() && 'edit.php' == $pagenow && !isset($_GET['orderby'])) {
//     $wp_query->set('orderby', 'date');
//     $wp_query->set('order', 'DESC');
//   }
// }

// add_filter('pre_get_posts', 'set_post_order_in_admin');

// apply_filters('activate_tinymce_for_media_description', true);

// //
// // Add Datetime and Screen ID to Adminbar
// //

// function baum_add_datetime_to_adminbar (WP_Admin_Bar $wp_admin_bar) {
//   $screen = get_current_screen();
//   $parent_slug = 'adminbar-date-time';
//   $local_time  = date('F j, Y g:i a', current_time('timestamp', 0));
//   $wp_admin_bar->add_menu([
//     'id'     => $parent_slug,
//     'parent' => 'top-secondary',
//     'group'  => null,
//     'title'  => $screen->id . ' | ' . $local_time,
//     'href'   => admin_url('/options-general.php'),
//   ]);
// }

// add_action('admin_bar_menu', 'baum_add_datetime_to_adminbar');


//
// Notify the editor of admin color scheme
//

// $current_user_id = get_current_user_id();
// if ($current_user_id) {
//   $admin_color_scheme = get_user_option('admin_color', $current_user_id);
//   baum_notify_admin('Current Admin Color Scheme:', $admin_color_scheme);
// }

function baum_register_playlist_taxonomy() {
    register_taxonomy('playlist', 'attachment', [
        'label'        => 'Playlists',
        'rewrite'      => ['slug' => 'playlist'],
        'hierarchical' => false,
        'show_admin_column' => true, // Shows count in admin
        'update_count_callback' => '_update_generic_term_count', // Important for media
    ]);
}
add_action('init', 'baum_register_playlist_taxonomy');


/////////////////////////////////////
// Add Tags to Media Library
/////////////////////////////////////

function add_tags_to_media_library () {
  register_taxonomy_for_object_type('post_tag', 'attachment');
}

add_action('init', 'add_tags_to_media_library');

/////////////////////////////////////
// Change the base URL of the author archives to /profile
/////////////////////////////////////

function baum_author_base () {
  global $wp_rewrite;
  $wp_rewrite->author_base = 'profile';
  $wp_rewrite->flush_rules();
}

add_action('init', 'baum_author_base');

/////////////////////////////////////
// Add Author Comment type
/////////////////////////////////////

// function register_author_comment_type () {
//   // Register a custom comment type
//   register_meta('comment', 'author_comment', [
//     'type' => 'string',
//     'description' => 'Comment about an author',
//     'single' => true,
//     'show_in_rest' => true,
//   ]);
// }

// add_action('init', 'register_author_comment_type');

// function handle_author_comment_submission () {
//   if (!is_user_logged_in() || empty($_POST['comment_content']) || empty($_POST['author_id'])) {
//       wp_die('Invalid comment submission.');
//   }

//   $author_id = absint($_POST['author_id']);
//   $comment_content = sanitize_textarea_field($_POST['comment_content']);
//   $user = wp_get_current_user();

//   // Insert comment
//   $comment_id = wp_insert_comment([
//       'comment_post_ID' => 0, // Not tied to any specific post
//       'comment_content' => $comment_content,
//       'user_id' => $user->ID,
//       'comment_author' => $user->display_name,
//       'comment_author_email' => $user->user_email,
//       'comment_approved' => 1,
//       'comment_type' => 'comment',
//   ]);

//   if ($comment_id) {
//       // Add author ID metadata to associate comment with the author
//       add_comment_meta($comment_id, 'author_comment', $author_id);
//   }

//   wp_redirect(get_author_posts_url($author_id) . '?comment_success=true');
//   exit;
// }

// add_action('admin_post_submit_author_comment', 'handle_author_comment_submission');

// add_action('admin_post_nopriv_submit_author_comment', 'handle_author_comment_submission');


// function register_wall_post_type() {
//   $args = array(
//       'label' => 'Wall',
//       'public' => true,
//       'supports' => array('title', 'editor', 'author', 'comments'),
//       'rewrite' => array('slug' => 'wall'),
//   );
//   register_post_type('wall', $args);
// }

// add_action('init', 'register_wall_post_type');







































// function assign_existing_authors_to_taxonomy() {
//   $users = get_users();

//   foreach ($users as $user) {
//       $user_id = $user->ID;
//       $username = $user->user_login;

//       // Check if the term exists
//       $term = get_term_by('name', $username, 'contributor');
//       if (!$term) {
//           wp_insert_term($username, 'contributor', [
//               'slug' => 'author-' . $user_id,
//           ]);
//       }
//   }
// }

// add_action('init', 'assign_existing_authors_to_taxonomy');

/**
 * Repairs existing posts by assigning them to the correct Home Timeline term
 *
 * This function queries all published posts of specific types and ensures they
 * are properly assigned to the Home Timeline taxonomy term corresponding to
 * their author. It's useful for fixing posts created before the Home Timeline
 * feature was implemented or after data migration.
 *
 * @return void
 *
 * @since 1.0.0
 */
function repair_home_timelines_existing_posts () {
  $post_types = ['story', 'activity'];

  $query = new WP_Query([
      'post_type'      => $post_types,
      'post_status'    => 'publish',
      'posts_per_page' => -1, // Fetch all posts
  ]);

  if ($query->have_posts()) {
      while ($query->have_posts()) {
          $query->the_post();

          $post_id = get_the_ID();
          $post = get_post($post_id);
          $author_id = $post->post_author;

          // Trigger the automatic assignment function
          assign_to_home_timeline($post_id, $post, false);
      }
  }

  wp_reset_postdata();
}

/**
 * Repairs Home Timelines taxonomy by creating terms for all users
 *
 * This function ensures that every registered user has a corresponding term
 * in the Home Timeline taxonomy. It checks for existing terms and creates
 * new ones as needed, which is essential for the proper functioning of
 * the timeline feature.
 *
 * @return void
 *
 * @since 1.0.0
 */
function repair_home_timelines_taxonomy() {
  $users = get_users();

  foreach ($users as $user) {
      $user_id = $user->ID;
      $username = $user->user_login;

      $taxonomy = 'home-timeline';

      // Check if the term already exists for this user
      $term = get_term_by('name', $username, $taxonomy);

      if (!$term) {
          // Create the term if it doesn't exist
          wp_insert_term($username, $taxonomy, [
              'slug' => 'user-' . $user_id,
          ]);
      }
  }
}

//
// Repair Timeline setup
//

// add_action('init', 'repair_home_timelines_existing_posts');
// add_action('init', 'repair_home_timelines_taxonomy');


// /**
//  * Schedule a repair event if not already scheduled.
//  */
// function schedule_home_timelines_repair() {
//   if (!wp_next_scheduled('repair_home_timelines_event')) {
//       wp_schedule_event(time(), 'daily', 'repair_home_timelines_event');
//   }
// }
// add_action('init', 'schedule_home_timelines_repair');

// /**
// * Hook for the repair event to ensure taxonomy accuracy.
// */
// add_action('repair_home_timelines_event', 'repair_home_timelines_taxonomy');


/**
 * Automatically assigns posts to the author's Home Timeline term
 *
 * This function is triggered when a post is saved and assigns it to a taxonomy
 * term corresponding to the post author's username. It creates the term if it
 * doesn't exist, which allows for filtering content by author in the Home Timeline
 * feature.
 *
 * @param int     $post_id The ID of the post being saved
 * @param WP_Post $post The post object
 * @param bool    $update Whether this is an existing post being updated
 * @return void
 *
 * @since 1.0.0
 */

function assign_to_home_timeline ($post_id, $post, $update) {
  // Avoid autosave and unnecessary hooks
  if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
      return;
  }

  // Only proceed for specific post types
  $post_types = ['story', 'activity'];
  if (!in_array($post->post_type, $post_types)) {
      return;
  }

  // Get the post author's user info
  $user_id = $post->post_author;
  $user = get_user_by('ID', $user_id);

  if (!$user) {
      return;
  }

  $taxonomy = 'home-timeline';
  $username = $user->user_login;

  // Find or create the term for the author
  $term = get_term_by('name', $username, $taxonomy);
  if (!$term) {
      $term_data = wp_insert_term($username, $taxonomy, [
          'slug' => 'user-' . $user_id,
      ]);
      $term_id = $term_data['term_id'];
  } else {
      $term_id = $term->term_id;
  }

  // Assign the term to the post
  wp_set_post_terms($post_id, [$term_id], $taxonomy, false);
}

add_action('save_post', 'assign_to_home_timeline', 10, 3);

/**
 * Removes Home Timelines taxonomy meta box from the post editor screen
 *
 * This function hides the Home Timeline taxonomy meta box from the post editor
 * to prevent users from manually assigning posts to timeline terms. This ensures
 * that posts are only assigned to the correct author's timeline automatically.
 *
 * @return void
 *
 * @since 1.0.0
 */
function remove_home_timelines_meta_box () {
  $post_types = ['story', 'activity'];

  foreach ($post_types as $post_type) {
      remove_meta_box('tagsdiv-home-timeline', $post_type, 'side');
  }
}

// TEMPORARILY DISABLED - TESTING FOR MEMORY ISSUES
// add_action('admin_menu', 'remove_home_timelines_meta_box');


/**
 * Create a term in the "Home Timelines" taxonomy for new users.
 *
 * @param int $user_id The ID of the newly registered user.
 */
add_action('user_register', function ($user_id) {
  // Retrieve the user data
  $user = get_user_by('ID', $user_id);
  $username = $user->user_login;

  $taxonomy = 'home-timeline';

  // Check if a term with the user's name already exists
  $term = get_term_by('name', $username, $taxonomy);

  // Create a new term if it doesn't already exist
  if (!$term) {
      wp_insert_term($username, $taxonomy, [
          'slug' => 'user-' . $user_id, // Use a unique slug based on the user ID
      ]);
  }
});


/**
 * Generate the Home Timelines query.
 *
 * @param int $user_id The user ID.
 * @return array Array of feed items sorted by date.
 */

function get_home_timelines_feed($user_id) {
  $following_ids = get_user_following_ids($user_id);

  if (empty($following_ids)) {
      return []; // No content to show if the user isn't following anything.
  }
  $paged = get_query_var('paged');
  $paged = ($paged) ? $paged : 1;
  global $do_not_duplicate;

  $query_args = [
    'post__not_in'  => $do_not_duplicate,
    'paged'          => $paged,
    'post_type'      => ['post', 'story'], // Add your post types
    'posts_per_page' => 10, // Limit the number of posts
    'orderby'        => 'date', // Order by latest
    'order'          => 'DESC',
    // 'author__in'     => $following_ids,
  ];

  $query_args['tax_query'] = [
    'relation' => "OR",
    [
      'taxonomy' => 'home-timeline',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ],
    [
      'taxonomy' => 'story-timeline',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ],
    [
      'taxonomy' => 'story-collection',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ],
    [
      'taxonomy' => 'person',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ],
    [
      'taxonomy' => 'contributor',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ],
    [
      'taxonomy' => 'category',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ],
    [
      'taxonomy' => 'channel',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ]
  ];

  $posts_query = new WP_Query($query_args);

  // // Add comments the user is following.
  // $comments_query = get_comments([
  //     'post_id__in'    => $following_ids, // Comments on followed threads.
  //     'status'         => 'approve',
  //     'number'         => 20,
  //     'orderby'        => 'comment_date',
  //     'order'          => 'DESC',
  // ]);

  // Prepare a unified feed.
  $feed_items = [];

  // Add posts to the feed.
  if ($posts_query->have_posts()) {
      foreach ($posts_query->posts as $post) {
          $feed_items[] = [
              'type'  => 'post',
              'data'  => $post,
              'date'  => strtotime($post->post_date),
          ];
      }
  }

  // // Add comments to the feed.
  // if (!empty($comments_query)) {
  //     foreach ($comments_query as $comment) {
  //         $feed_items[] = [
  //             'type'  => 'comment',
  //             'data'  => $comment,
  //             'date'  => strtotime($comment->comment_date),
  //         ];
  //     }
  // }

  // // Sort feed items by date (descending).
  // usort($feed_items, function ($a, $b) {
  //     return $b['date'] <=> $a['date'];
  // });

  return $feed_items;
}

// /**
//  * Assign posts of type "Story" or "Activity" to the author's "Home Timelines" term.
//  *
//  * @param int     $post_id The ID of the post being saved.
//  * @param WP_Post $post The post object being saved.
//  * @param bool    $update Whether this is an update (true) or a new post (false).
//  */
// add_action('save_post', function ($post_id, $post, $update) {
//   // Avoid autosave actions
//   if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
//       return;
//   }

//   // Define the post types to process
//   $post_types = ['story', 'activity'];
//   if (!in_array($post->post_type, $post_types)) {
//       return;
//   }

//   // Get the post author
//   $user_id = $post->post_author;
//   $user = get_user_by('ID', $user_id);

//   if (!$user) {
//       return; // Bail out if the author cannot be found
//   }

//   $taxonomy = 'home_timelines';
//   $username = $user->user_login;

//   // Find or create the user's term in the taxonomy
//   $term = get_term_by('name', $username, $taxonomy);
//   if (!$term) {
//       $term_data = wp_insert_term($username, $taxonomy, [
//           'slug' => 'user-' . $user_id, // Use a unique slug
//       ]);
//       $term_id = $term_data['term_id'];
//   } else {
//       $term_id = $term->term_id;
//   }

//   // Assign the term to the post
//   wp_set_post_terms($post_id, [$term_id], $taxonomy, false);
// }, 10, 3);

// /**
//  * Retrieve posts associated with a specific user's "Home Timelines" term.
//  *
//  * @param int $user_id The ID of the user whose timeline posts should be retrieved.
//  * @return WP_Post[] Array of post objects associated with the user's timeline term.
//  */
// function get_user_timeline_posts($user_id) {
//   $taxonomy = 'home_timelines';
//   $username = get_user_by('ID', $user_id)->user_login;

//   // Find the term for the user
//   $term = get_term_by('name', $username, $taxonomy);

//   if (!$term) {
//       return []; // Return an empty array if the term doesn't exist
//   }

//   // Query posts associated with the user's term
//   $query = new WP_Query([
//       'post_type' => ['story', 'activity'], // Post types to include
//       'tax_query' => [
//           [
//               'taxonomy' => $taxonomy,
//               'field'    => 'slug',
//               'terms'    => $term->slug, // Filter by the user's term
//           ],
//       ],
//   ]);

//   return $query->posts;
// }











// AJAX functions have been moved to functions-ajax.php










// function notify_my_mail( $comment_id, $comment_approved ) {
// 	if ( ! $comment_approved ) {
// 		$comment = get_comment( $comment_id );
// 		$mail = '<EMAIL>';
// 		$subject = sprintf( 'New Comment by: %s', $comment->comment_author );
// 		$message = $comment->comment_content;

// 		wp_mail( $mail, $subject, $message );
// 	}
// }
// add_action( 'comment_post', 'notify_my_mail', 10, 2 );


// function handle_wall_comment_submission() {
//   // Check if it's our custom comment submission
//   if (isset($_POST['baum_wall_comment_submission']) && $_POST['baum_wall_comment_submission'] === '1') {
//       // Sanitize and validate comment data
//       $comment_content = sanitize_text_field($_POST['comment']);
//       $comment_post_ID = intval($_POST['comment_post_ID']);
//       $user_id = get_current_user_id();

//       if (empty($comment_content) || !$user_id) {
//           wp_die('Error: Comment content or user ID is missing.');
//       }

//       // Set up the comment data
//       $commentdata = array(
//           'comment_post_ID' => $comment_post_ID, // This should be set to the author wall post ID
//           'comment_content' => $comment_content,
//           'user_id'         => $user_id,
//           'comment_author'  => wp_get_current_user()->display_name,
//           'comment_author_email' => wp_get_current_user()->user_email,
//       );

//       // Insert the comment into the database
//       $comment_id = wp_insert_comment($commentdata);

//       if (is_wp_error($comment_id) || !$comment_id) {
//           wp_die('Failed to post comment.');
//       } else {
//           wp_redirect(get_permalink($user_id)); // Redirect back to the wall post or another page
//           exit;
//       }
//   }
// }

// add_action('init', 'handle_wall_comment_submission');

/**
 * Detect if a comment has nested replies and count them.
 *
 * @param WP_Comment $comment The comment object.
 * @return int The number of nested replies. Returns 0 if none exist.
 */

// function count_nested_comment_replies ($comment) {
//   if (!($comment instanceof WP_Comment)) {
//       return 0; // Ensure $comment is a valid WP_Comment object
//   }

//   // Fetch child comments for this comment
//   $replies = get_comments([
//       'parent'       => $comment->comment_ID,
//       'post_id'      => $comment->comment_post_ID,
//       'status'       => 'approve', // Only count approved comments
//       'count'        => true, // Return the count instead of the full comments array
//   ]);

//   return (int) $replies; // Cast to integer for clarity
// }



//
// Create an Iframely Embed at the end of the comment for each URL
//

// function iframely_comment_embed ($comment_content) {
//   preg_match('/\bhttps?:\/\/\S+/i', $comment_content, $urls);
//   // error_log(print_r($urls));
//   if ($urls) {
//     $embed_url = $urls[0];
//     $embed_html = '<div class="iframely-embed" data-url="' . esc_url($embed_url) . '"></div>';
//     $comment_content .= $embed_html;
//     error_log('iframely_comment_embed: ' . $embed_url);
//   }
//   return $comment_content;
// }

// add_filter('comment_text', 'iframely_comment_embed');

// function iframely_comment_embed($comment_content) {
//   // Match URLs that are not within HTML tags
//   preg_match_all('~(?<!\w)(https?:\/\/[^\s<]+)(?![^<>]*>)~i', $comment_content, $matches);

//   if (!empty($matches[1])) {
//       foreach ($matches[1] as $embed_url) {
//           $embed_html = '<div class="iframely-embed" data-url="' . esc_url($embed_url) . '"></div>';
//           // Replace the plain URL with the embed HTML
//           $comment_content = str_replace($embed_url, $embed_html, $comment_content);
//           error_log('iframely_comment_embed: ' . $embed_url);
//       }
//   }

//   return $comment_content;
// }


// function iframely_comment_embed($comment_content) {
//   // Temporarily disable wpautop to prevent interference
//   remove_filter('comment_text', 'wpautop');

//   // Replace plain URLs with Iframely embed
//   $processed_content = preg_replace_callback(
//       '~(?<!\w)(https?:\/\/[^\s<]+)(?![^<>]*>)~i',
//       function ($matches) {
//           $url = $matches[1];
//           return '<div class="iframely-embed" data-url="' . esc_url($url) . '"></div>';
//       },
//       $comment_content
//   );

//   // Re-enable wpautop for subsequent filters
//   add_filter('comment_text', 'wpautop');

//   // Return the processed content
//   return $processed_content;
// }

// add_filter('comment_text', 'iframely_comment_embed');







// add_filter('comment_text', 'iframely_comment_embed');


// function baum_load_nested_comments() {
//     $parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
//     $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;

//     if (!$parent_id || !$post_id) {
//         wp_send_json_error(['message' => 'Invalid parent ID or post ID']);
//     }

//     $comments = get_comments([
//         'parent' => $parent_id,
//         'post_id' => $post_id,
//         'status' => 'approve',
//     ]);

//     if (empty($comments)) {
//         wp_send_json_success('<p>No replies yet.</p>');
//     }

//     ob_start();
//     wp_list_comments([
//         'walker' => new Baum_Comment_Walker(),
//         'style' => 'div',
//         'max_depth' => 3, // Adjust as necessary
//     ], $comments);
//     $html = ob_get_clean();

//     wp_send_json_success($html);
// }


// function baum_load_nested_comments() {
//     $parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
//     $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
//     $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;
//     $number = 2; // The number of replies to load per request

//     if (!$parent_id || !$post_id) {
//         wp_send_json_error(['message' => 'Invalid parent ID or post ID']);
//     }

//     $comments = get_comments([
//         'parent' => $parent_id,
//         'post_id' => $post_id,
//         'status' => 'approve',
//         'number' => $number,
//         'offset' => $offset,
//     ]);

//     if (empty($comments)) {
//         wp_send_json_error(['message' => 'No more replies']);
//     }

//     ob_start();
//     wp_list_comments([
//         'walker' => new Baum_Comment_Walker(),
//         'style' => 'div',
//     ], $comments);
//     $html = ob_get_clean();

//     wp_send_json_success(['html' => $html, 'next_offset' => $offset + $number]);
// }


// function baum_load_nested_comments() {
//   $parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
//   $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;

//   if (!$parent_id || !$post_id) {
//       wp_send_json_error(['message' => 'Invalid parent ID or post ID']);
//   }

//   // Get only the child comments that haven't been loaded yet
//   $comments = get_comments([
//       'parent' => $parent_id,
//       'post_id' => $post_id,
//       'status' => 'approve',
//   ]);

//   if (empty($comments)) {
//       wp_send_json_success('<p>No more replies.</p>');
//   }

//   ob_start();
//   wp_list_comments([
//       'walker' => new Baum_Comment_Walker(),
//       'style' => 'div',
//   ], $comments);
//   $html = ob_get_clean();

//   wp_send_json_success($html);
// }



/////////////////////////////////////
// Get all commenters for a post
/////////////////////////////////////

// function load_commenters() {
//   if (!is_user_logged_in()) {
//     wp_send_json_error('Not authorized');
//   }

//   $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
//   $commenters = get_comments([
//     'post_id' => $post_id,
//     'status' => 'approve',
//     'type' => 'comment',
//   ]);

//   $usernames = [];
//   foreach ($commenters as $comment) {
//     $user = get_userdata($comment->user_id);
//     if ($user && !in_array($user->display_name, $usernames)) {
//       $usernames[] = [
//         'display_name' => $user->display_name,
//         'user_id' => $user->ID,
//       ];
//     }
//   }

//   wp_send_json($usernames);
// }

// add_action('wp_ajax_load_commenters', 'load_commenters');





/**
 * Gets the display name of a user's highest privilege role
 *
 * This function determines which role a user has with the most capabilities
 * and returns its display name. This is useful for showing a user's primary
 * role when they have multiple roles assigned.
 *
 * @param int $user_id The ID of the user
 * @return string|null The display name of the highest role, or null if user not found
 *
 * @since 1.0.0
 */
function get_highest_role_display_name ($user_id) {
  // Get the user object
  $user = get_user_by('ID', absint($user_id));

  // Check if the user exists and has roles
  if (!$user || empty($user->roles)) {
    return null;
  }

  // Get all registered roles
  global $wp_roles;
  $all_roles = $wp_roles->roles;

  // Sort user roles by the number of capabilities they provide, descending
  usort($user->roles, function ($a, $b) use ($all_roles) {
      return count($all_roles[$b]['capabilities']) <=> count($all_roles[$a]['capabilities']);
  });

  // Get the display name of the role with the most capabilities
  $highest_role = $user->roles[0];
  return isset($all_roles[$highest_role]['name']) ? $all_roles[$highest_role]['name'] : null;
}

/**
 * Shortcode to display a user's highest role
 *
 * This shortcode allows displaying a user's highest privilege role name
 * anywhere in post content or widgets.
 *
 * @param array $atts Shortcode attributes containing user_id
 * @return string The display name of the user's highest role
 *
 * @since 1.0.0
 *
 * @example [baum_role user_id="123"]
 */
function get_highest_role_shortcode ($atts) {
  $atts = shortcode_atts([
    'user_id' => '0',
  ], $atts, 'baum_role');

  return get_highest_role_display_name($atts['user_id']);
}

add_shortcode('baum_role', 'get_highest_role_shortcode');

/**
 * Removes SearchWP plugin's default CSS styles
 *
 * This function dequeues the SearchWP live search CSS to prevent conflicts
 * with the theme's custom search styling. It runs with priority 20 to ensure
 * it executes after the plugin enqueues its styles.
 *
 * @return void
 *
 * @since 1.0.0
 */
function remove_searchwp_css () {
	wp_dequeue_style('searchwp-live-search');
}

add_action('wp_enqueue_scripts', 'remove_searchwp_css', 20);

/////////////////////////////////////
// Enqueque Custom Admin Area CSS
/////////////////////////////////////

// function load_custom_wp_admin_style () {
//   wp_register_style(
//     'custom_wp_admin_css',
//     get_bloginfo('stylesheet_directory') . '/css/spectre.css',
//     false,
//     '1.0.0'
//   );
//   wp_enqueue_style( 'custom_wp_admin_css' );
// }

// add_action('admin_enqueue_scripts', 'load_custom_wp_admin_style');

//
// Increase search queries posts per page to 100
//

/**
 * Increases the number of posts per page for search results
 *
 * This function modifies the main WordPress query to show 100 posts per page
 * on search result pages, allowing users to see more results at once without
 * having to navigate through multiple pages.
 *
 * @param WP_Query $query The WordPress query object
 * @return void
 *
 * @since 1.0.0
 */
function baum_increase_posts_per_page ($query) {
  if (!is_admin() && $query->is_main_query()) {
    if ($query->is_search) {
      $query->set('posts_per_page', 100);
    }
  }
}

add_action('pre_get_posts', 'baum_increase_posts_per_page');

/**
 * Orders search results by post type and date
 *
 * This function modifies the search query ordering to prioritize results
 * by post type first (descending), then by publication date (newest first).
 * This ensures that certain post types appear before others in search results.
 *
 * @param string $orderby The current ORDER BY clause
 * @return string Modified ORDER BY clause for search queries
 *
 * @since 1.0.0
 */
function baum_search_orderby ($orderby) {
  if (!is_admin() && is_main_query() && is_search()) {
    $orderby = 'post_type DESC, post_date DESC';
  }
  return $orderby;
}

add_filter('posts_orderby', 'baum_search_orderby');

/**
 * Instantly redirects link format posts based on ACF settings
 *
 * This function checks if a post has the 'link' format and instant redirection
 * is enabled via ACF fields. If so, it immediately redirects the user to the
 * specified link URL using JavaScript.
 *
 * @return void Either redirects and dies, or returns normally
 *
 * @since 1.0.0
 */
function baum_instant_redirect_content_type_link () {
  global $post;

  // Check if instant redirection is enabled
  $instant_redirection = get_field('instant_redirection', $post);
  if ($instant_redirection !== 'true') return;

  // Only redirect on single link format posts, not in admin
  if (get_post_format($post->ID) == 'link' && !is_admin() && is_single($post->ID)) {
    $link = get_field('link', $post);
    if (!$link) return;

    // Use JavaScript redirect and terminate execution
    echo '<script>window.location="' . esc_url($link) . '";</script>';
    die();
  }
}

add_action('the_post', 'baum_instant_redirect_content_type_link');

//
// Yoast SEO - "An error occurred loading the Yoast SEO primary taxonomy picker"
//
// https://stackoverflow.com/questions/57106206/an-error-occurred-loading-the-yoast-seo-primary-taxonomy-picker
//

// add_filter( 'wpseo_primary_term_taxonomies', '__return_empty_array' );

//
// Add ID of story to the content so Browsing History works
//
// TODO: This hack should be handled with the function built-in to wordpress
//

/**
 * Adds JavaScript to track post views for browsing history
 *
 * This function appends a script to the post content that sets the current
 * post ID in a JavaScript variable. This allows the browsing history
 * functionality to track which posts the user has viewed.
 *
 * @param string $content The post content
 * @return string Modified content with the history tracking script
 *
 * @since 1.0.0
 */
function baum_history_add_story_id ($content) {
  $custom_content = '<script>'
  . 'window.baum = window.baum || {};'
  . 'window.baum.story_id = ' . get_the_ID() . ';'
  .'</script>';
  $content .= $custom_content;
  return $content;
}

add_filter('the_content', 'baum_history_add_story_id');

//
// Pass variables to frontend script for Browsing History
//

// function baum_enqueue_history_script () {
//   $action_get = 'baum_history_get';
//   wp_localize_script('baum-history', 'baum_history',
//     [
//       'api' => admin_url('admin-ajax.php'),
//       'action' => $action_get,
//       'nonce' => wp_create_nonce($action_get)
//     ]
//   );
// }

// add_action('wp_enqueue_scripts', 'baum_enqueue_history_script');


//
// Get the last 100 items from Browsing History
//

// get_baum_history function has been moved to functions-ajax.php

// function enqueue_baum_bookmarks_script () {
//   wp_enqueue_script(
//     'baum-bookmarks-js',
//     get_template_directory_uri() . '/js/baum-bookmarks.js',
//     ['jquery'],
//     null,
//     true
//   );

//   wp_localize_script('baum-bookmarks-js', 'baum_bookmarks', [
//     'ajax_url' => admin_url('admin-ajax.php'),
//     'nonce' => wp_create_nonce('get_baum_bookmarks'),
//     'story_id' => get_the_ID(),
//     'is_page' => is_page_template('page-starred.php'),
//     'icon' => get_theme_mod('baum_bookmark_icon', 'star')
//   ]);
// }

// add_action('wp_enqueue_scripts', 'enqueue_baum_bookmarks_script');

//
// Bookmarks
//
// Get the last 100 items bookmarked
//

// function add_baum_bookmarks_nonce () {
//   // This will output the nonce field in the footer
//   echo '<input id="baum_bookmarks_nonce" type="hidden" name="baum_bookmarks_nonce" value="' . wp_create_nonce('get_baum_bookmarks') . '">';
// }

// add_action('wp_footer', 'add_baum_bookmarks_nonce');

// function get_baum_bookmarks () {
//   if (isset($_POST['nonce'])) {
//     $verify = wp_verify_nonce($_POST['nonce'], 'get_baum_bookmarks');
//   } else {
//     $verify = 0;
//   }

//   // print_r($verify);

//   // Check if the nonce is set and valid
//   if (!$verify) {
//     wp_die(__('Nonce verification ' . $verify, 'baum'), '', [
//       'response' => 403
//     ]);
//   }

//   // Retrieve and sanitize the bookmarks from POST
//   $bookmarks = isset($_POST['bookmarks'])
//     ? sanitize_text_field($_POST['bookmarks']) : '';

//   if (!empty($bookmarks)) {
//     // Convert to an array of integers
//     $bookmarks = array_map('intval', explode(',', $bookmarks));
//     $atts = [
//       'size' => 'small',
//       'txtcolor' => 'standard',
//       'bgcolor' => 'standard',
//       'fav_btn' => true,
//     ];
//     echo "<div id='baum-bookmarks' "
//       . "class='baum-cards baum-cards-" . esc_attr($atts['size']) . "'>";
//     $the_query = new WP_Query([
//       'post_type' => 'post',
//       'posts_per_page' => 100,
//       'post__in' => $bookmarks,
//       'orderby' => 'post__in',
//     ]);
//     while ($the_query->have_posts()) {
//       $the_query->the_post();
//       get_template_part('parts/baum', 'card', $atts);
//     }
//     wp_reset_postdata();
//     echo '</div>';
//   }
//   wp_die(); // this is required to return a proper result
// }

// add_action('wp_ajax_action_get_baum_bookmarks', 'get_baum_bookmarks');
// add_action('wp_ajax_nopriv_action_get_baum_bookmarks', 'get_baum_bookmarks');


//
// Get the YouTube thumbnail and set it as the featured image
//

// function baum_get_youtube_thumbnail ($post_id) {

//   // Check if post has featured image
//   if (has_post_thumbnail($post_id)) {
//     error_log('Post ID {$post_id}: Already has a featured image.');
//     return;
//   }

//   // Check if a featured image is set manually
//   if (get_post_meta($post_id, '_thumbnail_id', true)) return;

//   // Check if this is an autosave or a revision
//   if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
//   if (wp_is_post_revision($post_id)) return;

//   // Check if the YouTube Video ID ACF field is set
//   $youtube_id = get_field('youtube_id', $post_id);
//   if (!$youtube_id) return;

//   // Add Featured Image to Post
//   $image_url = 'https://img.youtube.com/vi/' . $youtube_id . '/maxresdefault.jpg';
//   $image_name = $youtube_id . '.jpg';
//   $upload_dir = wp_upload_dir(); // Set upload folder
//   $image_data = file_get_contents($image_url); // Get image data
//   $unique_file_name = wp_unique_filename($upload_dir['path'], $image_name);
//   // Generate unique name
//   $filename = basename( $unique_file_name ); // Create image file name

//   // Check folder permission and define file location
//   if (wp_mkdir_p($upload_dir['path'])) {
//     $file = $upload_dir['path'] . '/' . $filename;
//   } else {
//     $file = $upload_dir['basedir'] . '/' . $filename;
//   }

//   // Create the image  file on the server
//   file_put_contents($file, $image_data);

//   // Check image file type
//   $wp_filetype = wp_check_filetype($filename, null);

//   // Set attachment data
//   $attachment = [
//     'post_mime_type' => $wp_filetype['type'],
//     'post_title' => sanitize_file_name($filename),
//     'post_content' => '',
//     'post_status' => 'inherit'
//   ];

//   // Create the attachment
//   $attach_id = wp_insert_attachment($attachment, $file, $post_id);

//   // Include image.php
//   require_once(ABSPATH . 'wp-admin/includes/image.php');

//   // Define attachment metadata
//   $attach_data = wp_generate_attachment_metadata($attach_id, $file);

//   // Assign metadata to attachment
//   wp_update_attachment_metadata($attach_id, $attach_data);

//   // And finally assign featured image to post
//   set_post_thumbnail($post_id, $attach_id);
// }

// add_filter('save_post', 'baum_get_youtube_thumbnail', 20);

//
// Download Upload Youtube Audio
//

/**
 * Processes YouTube URLs to extract audio
 *
 * This function takes a YouTube URL from an ACF field, converts the video
 * to audio using an external script, and saves the resulting audio URL
 * to another ACF field. It's useful for creating podcast or audio versions
 * of YouTube content.
 *
 * @param int $post_id The ID of the post being saved
 * @return void
 *
 * @since 1.0.0
 */
function process_youtube_url ($post_id) {
  // Check if this is an autosave or a revision
  if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
  if (wp_is_post_revision($post_id)) return;

  // Check if the YouTube URL ACF field is set
  $youtube_url = get_field('youtube_url', $post_id);
  if (!$youtube_url) return;

  // Convert YouTube video to audio and upload to S3
  $audio_url = convert_youtube_to_audio($youtube_url);

  if ($audio_url) {
      // Save the audio URL to a custom field
      update_field('audio_file_url', $audio_url, $post_id);

      // Optionally, use the audio file URL for further processing (e.g., transcription)
  }
}

// add_action('save_post', 'process_youtube_url');

//
// Use external PHP script to convert YouTube video link to audio
//

/**
 * Converts a YouTube video to audio using an external script
 *
 * This function calls an external PHP script that downloads a YouTube video
 * and extracts its audio track. The script is expected to return the URL
 * of the extracted audio file, which this function parses and returns.
 *
 * @param string $youtube_url The URL of the YouTube video
 * @return string The URL of the extracted audio file, or empty string on failure
 *
 * @since 1.0.0
 */
function convert_youtube_to_audio ($youtube_url) {
  $output = shell_exec("php /path/to/your/script.php '$youtube_url'");
  if (strpos($output, 'Audio URL:') !== false) {
      return trim(str_replace('Audio URL:', '', $output));
  }
  return '';
}

//
// ACF field for the ID of a youtube video
// query an API to dictate the youtube video into plaintext
// and insert it into the content of the post
//

// Create an ACF (Advanced Custom Fields) field for the YouTube video ID.
// Add custom code to query an API that converts the YouTube video to text.
// Insert the text into the post content.
// Here’s a step-by-step guide, including the necessary code snippets:

// Step 1: Create an ACF Field
// Install and activate the ACF plugin if you haven't already.
// Create a new field group (e.g., "YouTube Video ID") and add a field:
// Field Label: YouTube Video ID
// Field Name: youtube_video_id
// Field Type: Text
// Step 2: Add Custom Code to Your Theme’s Functions.php
// WordPress hooks process the YouTube video ID,
// query the transcription API, and update the post content

/**
 * Processes YouTube video ID to add transcription to post content
 *
 * This function checks if a post has a YouTube video ID and transcription
 * is enabled, then automatically transcribes the video and appends the
 * transcription to the post content. Only runs if the post has no existing content.
 *
 * @param int $post_id The ID of the post being processed
 * @return void
 *
 * @since 1.0.0
 */
function process_youtube_video_id ($post_id) {

  // Check if post has content - don't overwrite existing content
  $post_content = get_post_field('post_content', $post_id);
  if (strlen($post_content)) return;

  // Check if this is an autosave or a revision
  if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
  if (wp_is_post_revision($post_id)) return;

  // Check if the YouTube Video ID ACF field is set
  $youtube_id = get_field('youtube_id', $post_id);
  if (!$youtube_id) return;

  // Only transcribe if explicitly enabled
  if (get_field('transcribe_video_on_save', $post_id) == 'true') {

    // Transcribe the YouTube video using an external API (e.g., AssemblyAI, Google Cloud Speech-to-Text, etc.)
    $transcription = get_youtube_transcription($youtube_id);

    if ($transcription) {
      // Get the current post content
      $post_content = get_post_field('post_content', $post_id);

      // Append the transcription to the post content
      $post_content .= "\n\n" . '<h2>Video Transcription</h2>' . "\n" . wp_kses_post($transcription);

      // Update the post content
      wp_update_post(array(
        'ID' => $post_id,
        'post_content' => $post_content
      ));
    }
  }
}

// Hook into the save_post action to process the YouTube video ID when the post is saved
// add_action('save_post', 'process_youtube_video_id');

/**
 * Transcribes a YouTube video using AssemblyAI API
 *
 * This function takes a YouTube video ID, submits it to AssemblyAI for transcription,
 * and polls the API until the transcription is complete. It handles the full workflow
 * from submission to completion, including error handling and status polling.
 *
 * @param string $video_id The YouTube video ID to transcribe
 * @return string The transcribed text, or an error message if transcription fails
 *
 * @since 1.0.0
 */
function get_youtube_transcription($video_id) {
  global $YOUR_ASSEMBLYAI_API_KEY;
  $api_key = $YOUR_ASSEMBLYAI_API_KEY;

  // Validate video ID
  if (empty($video_id)) {
    return 'Invalid video ID provided.';
  }

  $video_url = 'https://www.youtube.com/watch?v=' . sanitize_text_field($video_id);

  // Submit the video URL to AssemblyAI for transcription
  $upload_url = 'https://api.assemblyai.com/v2/transcript';
  $response = wp_remote_post($upload_url, array(
    'body' => json_encode(array('audio_url' => $video_url)),
    'headers' => array(
      'Authorization' => $api_key,
      'Content-Type' => 'application/json'
    ),
    'timeout' => 30,
  ));

  if (is_wp_error($response)) {
    return 'Transcription not available.';
  }

  $body = wp_remote_retrieve_body($response);
  $data = json_decode($body, true);

  if (!isset($data['id'])) {
    return 'Failed to submit transcription request.';
  }

  $transcript_id = sanitize_text_field($data['id']);

  // Poll the AssemblyAI API to get the transcription result
  $transcript_url = 'https://api.assemblyai.com/v2/transcript/' . $transcript_id;
  $max_attempts = 120; // Maximum 10 minutes of polling (120 * 5 seconds)
  $attempts = 0;

  while ($attempts < $max_attempts) {
    $response = wp_remote_get($transcript_url, array(
      'headers' => array(
        'Authorization' => $api_key
      ),
      'timeout' => 30,
    ));

    if (is_wp_error($response)) {
      return 'Failed to retrieve transcript.';
    }

    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);

    if ($data['status'] === 'completed') {
      return sanitize_textarea_field($data['text']);
    }

    if ($data['status'] === 'failed') {
      return 'Transcription failed.';
    }

    // Wait for a few seconds before checking again
    sleep(5);
    $attempts++;
  }

  return 'Transcription timed out.';
}

/**
 * Gets the MIME type of a file from its URL
 *
 * This function determines the MIME type of a file based on its URL extension.
 * It uses WordPress's built-in wp_check_filetype function and provides a
 * fallback MIME type if the file type cannot be determined.
 *
 * @param string $url The URL of the file to check
 * @param string $fallback The fallback MIME type to use if detection fails
 * @return string The MIME type of the file or the fallback value
 *
 * @since 1.0.0
 */
function get_mime_type ($url, $fallback = 'video/mp4') {
  $file_info = wp_check_filetype(esc_url_raw($url));
  return $file_info['type'] ?: $fallback; // Fallback to 'video/mp4'
}

/**
 * Renders a menu of items the user is following (deprecated)
 *
 * This function was used to generate a dynamic menu of categories or terms
 * that a user is following. The function is mostly commented out and appears
 * to be deprecated in favor of newer following functionality.
 *
 * @return void Outputs HTML for the following menu
 *
 * @since 1.0.0
 * @deprecated This function is deprecated and may be removed in future versions
 */
function get_following_menu () {
  // 	$baum_follows = $_POST['baum_follows'];
  //   if (isset($baum_follows) && strlen($baum_follows)) {
  //     $user_following = explode(',', $baum_follows);
    // }

  //   // $domain = get_option('siteurl');
  //   // $domain = str_replace('http://', '', $domain);
  //   // $domain = str_replace('www', '', $domain);
  //   // if (isset($_COOKIE["baum_follows"]) && strlen($_COOKIE["baum_follows"])) {
  //   //   $user_following = explode(',', $_COOKIE["baum_follows"]);
  //   // }

  // foreach ($user_following as $term_id) {
  //     $cat = get_term($term_id);
  //     $following_menu_obj[] = array(
  //       'ID' => $cat->term_id,
  //       'title' => $cat->name,
  //       'url' => $cat->slug,
  //       'object' => 'category',
  //       'object_id' => $cat->term_id,
  //       'type' => 'taxonomy',
  //       'class' => ''
  //     );
  //     $term = get_queried_object(); ?>
    <li id="menu-item-<?php echo $cat->term_id; ?>" class="<?php echo ($term->slug == $cat->slug) ? 'current-menu-item' : ''; ?> <?php echo get_field('icon', 'category_' . $term_id); ?> menu-item menu-item-type-taxonomy menu-item-object-post_cat menu-item-<?php echo $cat->term_id; ?>">
      <a href="<?php echo get_category_link($cat); ?>">
        <?php echo $cat->name; ?>
      </a>
    </li>
  <?php // }
  // die();
}

// add_action('wp_ajax_action_get_following_menu', 'get_following_menu');
// add_action('wp_ajax_nopriv_action_get_following_menu', 'get_following_menu');

/**
 * Gets the IDs of items the user is following
 *
 * This function retrieves the list of IDs that a user is following,
 * either from user meta or from a cookie. It's used for the "following"
 * functionality that allows users to track specific content.
 *
 * @param int $user_id The user ID
 * @return array The array of IDs the user is following
 *
 * @since 1.0.0
 */
function get_user_following_ids ($user_id) {
  // This is a placeholder. Replace this with your actual logic for fetching followed IDs.
  // $following_ids = get_user_meta($user_id, 'following_ids', true);

  if (isset($_COOKIE['baum_follows']) && strlen($_COOKIE['baum_follows']))
    $following_ids = explode(',', $_COOKIE['baum_follows']);
  else
    $following_ids = [];

  return is_array($following_ids) ? $following_ids : [];
}

/////////////////////////////////////
// Return the User's Dashboard URL
/////////////////////////////////////

// function dashboard_url () {
//   $current_user = wp_get_current_user();
//   if ($current_user->ID == 0) return '/';
//   return '/dashboard';
// }

/////////////////////////////////////
// Return the User's Profile URL
/////////////////////////////////////

// function get_profile_url ($id) {
//   return get_the_author_meta('url', $id);
// }

/////////////////////////////////////
// Return the User's Edit Profile URL
/////////////////////////////////////

// get_edit_profile_url($user_id)
// function edit_profile_url () {
//   $current_user = wp_get_current_user();
//   if ($current_user->ID == 0) return '/';
//   return '/wp-admin/profile.php';
// }

/**
 * Adds the selected font class to the body tag
 *
 * This function retrieves the font selection from the theme customizer
 * and adds it as a CSS class to the body tag. This allows the theme
 * to apply different font families based on user selection.
 *
 * @param array $classes Array of existing body classes
 * @return array Modified array of body classes with font class added
 *
 * @since 1.0.0
 */
function baum_body_class ($classes) {
  $baum_font = get_theme_mod('baum_font');
  if ($baum_font) {
    $classes[] = sanitize_html_class($baum_font);
  }
  return $classes;
}

add_filter('body_class', 'baum_body_class');

/**
 * Registers post tags for use with pages
 *
 * This function enables the post_tag taxonomy for pages, allowing pages
 * to be tagged just like posts. This extends WordPress's default behavior
 * where tags are only available for posts.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_register_tags_for_pages () {
  // register_taxonomy_for_object_type('category', 'page');
  register_taxonomy_for_object_type('post_tag', 'page');
}

add_action('init', 'baum_register_tags_for_pages');

/////////////////////////////////////
// Query Additional Post Types in Archives of Tags and Categories
/////////////////////////////////////

/**
 * Modifies tag archive queries to include pages
 *
 * This function extends the default WordPress tag archives to include
 * pages in addition to posts. This allows pages to be tagged and displayed
 * in tag archives alongside regular posts.
 *
 * @param WP_Query $query The WordPress query object
 * @return void
 *
 * @since 1.0.0
 */
function baum_query_pages_for_tags ($query) {
  if (is_admin() || ! $query->is_main_query()) {
    return;
  }
  // if ($query->is_category && $query->is_main_query()) {
  //   $query->set('post_type', [ 'post', 'page' ]);
  // }
  if ($query->is_tag && $query->is_main_query()) {
    $query->set('post_type', [ 'post', 'page' ]);
  }
}

add_action('pre_get_posts', 'baum_query_pages_for_tags');

/**
 * Returns an array of active categories for the current context
 *
 * This function determines which categories are relevant to the current page.
 * On category archive pages, it returns the current category. On single posts,
 * it returns all categories assigned to the post.
 *
 * @return array Array of category objects, or empty array if none found
 *
 * @since 1.0.0
 */
function get_current_categories() {
  if (is_category()) {
    return [get_queried_object()];
  } elseif (is_single()) {
    return get_the_category();
  }
  return [];
}

/**
 * Fixes pagination query string issues
 *
 * This function ensures that the 'paged' query variable is properly set
 * for the main query, which helps resolve pagination issues that can
 * occur with custom queries or theme modifications.
 *
 * @param WP_Query $query The WordPress query object
 * @return void
 *
 * @since 1.0.0
 */
function baum_fix_pagination_query_string ($query) {
  if ($query->is_main_query() && !$query->is_feed() && !is_admin()) {
    $query->set('paged', get_query_var('paged'));
  }
}

add_action('pre_get_posts', 'baum_fix_pagination_query_string');

/////////////////////////////////////
// Update default media icons
/////////////////////////////////////

//
// Get the path to the icon directory
//

// function wpdocs_theme_icon_directory ($icon_dir) {
// 	return get_stylesheet_directory() . '/images';
// }

// add_filter('icon_dir', 'wpdocs_theme_icon_directory');

//
// Get the URI of the icon directory
//

// function wpdocs_theme_icon_uri ($icon_dir) {
// 	return get_stylesheet_directory_uri() . '/images';
// }

// add_filter('icon_dir_uri', 'wpdocs_theme_icon_uri');

/**
 * Generates custom pagination links
 *
 * This function creates a custom pagination interface with numbered pages,
 * previous/next links, and first/last page links. It provides more control
 * over pagination display than WordPress's default pagination.
 *
 * @param string $pages Total number of pages (auto-detected if empty)
 * @param int $range Number of page links to show on each side of current page
 * @return void Outputs HTML pagination links
 *
 * @since 1.0.0
 */
function pagination ($pages = '', $range = 4) {
  global $paged;
  $items = ($range * 2) + 1;
  if (empty($paged)) $paged = 1;

  if ($pages == '') {
    global $wp_query;
    $pages = $wp_query->max_num_pages;
    if (!$pages) $pages = 1;
  }

  if (1 != $pages) {
    echo '<div class="pagination baum-pagination" style="display:none;">';
    echo '<span class="pagination-info"> ' . absint($paged) . ' of ' . absint($pages) . '</span>';

    if ($paged > 2 && $paged > $range + 1 && $items < $pages)
      echo '<a href="' . esc_url(get_pagenum_link(1)) . '" class="pagination-btn pagination-first">&laquo;</a>';

    if ($paged > 1 && $items < $pages)
      echo '<a href="' . esc_url(get_pagenum_link($paged - 1)) . '" class="pagination-btn pagination-prev">&lsaquo;</a>';

    for ($i=1; $i <= $pages; $i++) {
      if (1 != $pages && (!($i >= $paged + $range+1 || $i <= $paged - $range - 1) || $pages <= $items)) {
        if ($paged == $i) {
          echo '<span class="pagination-btn pagination-current">' . absint($i) . '</span>';
        } else {
          echo '<a href="' . esc_url(get_pagenum_link($i)) . '" class="pagination-btn pagination-inactive">' . absint($i) . '</a>';
        }
      }
    }

    if ($paged < $pages && $items < $pages)
      echo '<a href="' . esc_url(get_pagenum_link($paged + 1)) . '" class="pagination-btn pagination-next">&rsaquo;</a>';

    if ($paged < $pages - 1 && $paged + $range - 1 < $pages && $items < $pages)
      echo '<a href="' . esc_url(get_pagenum_link($pages)) . '" class="pagination-btn pagination-last">&raquo;</a>';

    echo '</div>';
  }
}

/**
 * Fixes the document title for home pages
 *
 * This function customizes the document title for home pages by combining
 * the site name and description in a specific format. It helps ensure
 * consistent and SEO-friendly titles for the home page.
 *
 * @param string $title The current document title
 * @return string|null Modified title for home pages, or null for other pages
 *
 * @since 1.0.0
 */
function baum_fix_home_title ($title) {
  if ((is_home() && !is_front_page()) || (!is_home() && is_front_page()) || !isset($title)) {
    $baum_home_title = get_bloginfo('name', 'display');
    $baum_home_desc = get_bloginfo('description', 'display');
    return '[' . esc_html($baum_home_title) . '] ' . esc_html($baum_home_desc);
  }
  return null;
}

add_filter('pre_get_document_title', 'baum_fix_home_title');

//
//
//

/**
 * Checks if the current page is the first page of content
 *
 * This function determines whether the current page being viewed is the first
 * page, either of a paginated post or an archive/category/tag page. It's useful
 * for conditionally displaying content only on the first page.
 *
 * @return bool True if it's the first page, false otherwise
 *
 * @since 1.0.0
 */
function is_first_page () {
  global $page, $numpages;

  // Check if it's a paginated single post (multipage post)
  if (is_singular() && ($page == 0 || $page == 1)) {
    return true;
  }

  // Check if it's an archive or paginated page and if it's on page 1
  if (!is_singular() && (get_query_var('paged') == 0 || get_query_var('paged') == 1)) {
    return true;
  }

  return false;
}

/**
 * Checks if the current page is the last page of content
 *
 * This function determines whether the current page being viewed is the last
 * page, either of a paginated post or an archive/category/tag page. It's useful
 * for conditionally displaying content only on the last page.
 *
 * @return bool True if it's the last page, false otherwise
 *
 * @since 1.0.0
 */
function is_last_page () {
  global $wp_query, $page, $numpages;

  // Check if it's a paginated single post
  if (is_singular()) {
    return $page >= $numpages;
  }

  // Check if it's a paginated archive (categories, search results, etc.)
  if ($wp_query->max_num_pages > 1) {
    $current_page = max(1, get_query_var('paged', 1));
    return $current_page >= $wp_query->max_num_pages;
  }

  return true; // If no pagination, it's always the last page.
}

/**
 * Gets the current page number
 *
 * This function returns the current page number for both paginated posts
 * and paginated archives. It handles the different query variables used
 * for different types of pagination.
 *
 * @return int The current page number (minimum 1)
 *
 * @since 1.0.0
 */
function get_current_page () {
  if (is_singular()) {
    return max(1, get_query_var('page', 1)); // Handles paginated posts
  }

  return max(1, get_query_var('paged', 1)); // Handles paginated archives
}

/**
 * Displays a random call-to-action message at the end of content
 *
 * This function shows a randomly selected message at the end of posts or pages
 * to encourage user engagement. It includes different message types that can
 * trigger different actions like sharing, commenting, or viewing related content.
 *
 * @return void Outputs HTML for the end message and related content
 *
 * @since 1.0.0
 */
function display_random_end_message () {
  $messages = [
      "Want more? Check out related posts below!" => 'related',
      "Like what you read? Share this with friends!" => 'share',
      // "Subscribe to get more content like this!" => 'subscribe',
      "Leave a comment and let us know what you think!" => 'comment',
      // "Help us keep the lights on—your support means everything!" => 'donate'
  ];

  // Pick a random message
  $random_key = array_rand($messages);
  $random_message = $random_key;
  $message_type = $messages[$random_key];

  echo "<div style='width:100%; height:20px;'></div>";
  echo "<div class='baum-page-end'>";

  // Display the message
  echo '<h5 class="baum-last-page-message center">' . esc_html($random_message) . '</h5>';

  echo "<div style='width:100%; height:40px;'></div>";

  // Conditionally display extra content based on message type
  switch ($message_type) {

    case 'related':
      // Related posts would be handled elsewhere
      echo '';
      break;

    case 'share':
      echo "<div class='center'>";
      get_template_part('parts/baum-social', 'story');
      echo "</div>";
      break;

    // case 'subscribe':
    //   echo "<a href='/subscribe' class='button baum-button' style=''>Join the Email List &nbsp; <i class='fa-solid fa-paper-plane'></i></a>";
    //   break;

    case 'comment':
      // Comments would be handled elsewhere
      echo '';
      break;

    // case 'donate':
    //   echo "<a href='/donate' class='button baum-button' style=''><i class='fa-solid fa-dollar'></i> &nbsp; Donate</a>";
    //   break;
  }

  echo "</div>";
  echo "<div style='width:100%; height:40px;'></div>";
}

/**
 * Generates styled pagination links for multi-page posts
 *
 * This function creates custom pagination links for posts that are split
 * across multiple pages using the <!--nextpage--> tag. It includes
 * contextual messages and styled buttons for navigation.
 *
 * @param string $numb_or_next Whether to show numbers or next/previous links
 * @return string HTML output for the pagination links
 *
 * @since 1.0.0
 */
function get_paginated_links ($numb_or_next = 'number') {
  ob_start();

  // Display contextual message based on current page
  if (is_last_page()) {
    $numb_or_next = 'number';
    echo '<h5 class="baum-page-message">You are on the last page</h5>';
  } else if (is_first_page() == false) {
    echo '<h5 class="baum-page-message">You are on page ' . absint(get_current_page()) . '</h5>';
  } else {
    echo '<h5 class="baum-page-message">Continue to Page 2</h5>';
  }

  // Generate the pagination links
  wp_link_pages([
    'before'            => '<div class="baum-page-links" style="margin-bottom:50px;">',
    'after'             => '</div>',
    'link_before'       => '<span class="button baum-button baum-button-small">',
    'link_after'        => '</span>',
    'next_or_number'    => $numb_or_next, // 'number' or 'next'
    'nextpagelink'      => 'Next Page &nbsp; <i class="fa-fw fa-solid fa-caret-right"></i>',
    'previouspagelink'  => '<i class="fa-fw fa-solid fa-caret-left"></i> &nbsp; Previous',
  ]);

  return ob_get_clean();
}

/////////////////////////////////////
// Register Baum Menus
/////////////////////////////////////

/**
 * Registers navigation menus for the theme
 *
 * This function registers multiple navigation menu locations that can be
 * managed through the WordPress admin. These menus are used in different
 * parts of the theme for navigation and user interface elements.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_register_menus () {
  register_nav_menus(
    [
      'apple-style-menu'          => 'Apple Style Menu',
      'main-menu-left'            => 'Main Menu Left',
      'main-menu-right'           => 'Main Menu Right',
      'minimal-menu-right'        => 'Minimal Menu Right',
      'main-user-menu'            => 'Main User Menu',
      'main-notifications-menu'   => 'Main Notifications Menu',
    ]
  );
}

add_action('init', 'baum_register_menus');

//
//
//

/**
 * Adds a class to keep submenus open for active menu items
 *
 * This function adds the 'menu-item-open' class to menu items that are
 * either the current page or an ancestor of the current page. This helps
 * to keep submenus expanded when a child page is active.
 *
 * @param array    $classes Array of CSS classes for the menu item
 * @param WP_Post  $item    The current menu item
 * @return array   Modified array of CSS classes
 *
 * @since 1.0.0
 */
function keep_open_submenu_class ($classes, $item) {
  if (in_array('current-menu-item', $classes) || in_array('current-menu-ancestor', $classes)) {
      $classes[] = 'menu-item-open'; // Add the class to open the parent
  }
  return $classes;
}

add_filter('nav_menu_css_class', 'keep_open_submenu_class', 10, 2);

/**
 * Removes Font Awesome classes from menu item CSS classes
 *
 * This function filters out Font Awesome classes from the menu item's CSS classes
 * to prevent them from being applied to the <li> element. The Font Awesome classes
 * are handled separately in the menu item title filter.
 *
 * @param array $classes Array of CSS classes for the menu item
 * @param WP_Post $item The current menu item object
 * @param stdClass $args Menu arguments
 * @return array Filtered array of CSS classes without Font Awesome classes
 *
 * @since 1.0.0
 */
function baum_clean_menu_classes ($classes, $item, $args) {
  // Filter out Font Awesome classes
  $non_fa_classes = array_filter($classes, function ($class) {
      return !str_starts_with($class, 'fa-');
  });

  return $non_fa_classes; // Return cleaned classes for the <li> element
}

add_filter('nav_menu_css_class', 'baum_clean_menu_classes', 10, 3);

/**
 * Adds Font Awesome icons to menu items
 *
 * This function processes menu items to add Font Awesome icons either from
 * CSS classes or ACF fields. It supports showing icons with or without labels
 * and handles the icon display logic.
 *
 * @param string $title The menu item title
 * @param WP_Post $item The current menu item object
 * @param stdClass $args Menu arguments
 * @param int $depth The depth of the menu item
 * @return string Modified menu item title with icon markup
 *
 * @since 1.0.0
 */
function baum_menu_item_fontawesome ($title, $item, $args, $depth) {
  // Check for Font Awesome classes in the menu item's CSS classes
  $fa_classes = array_filter($item->classes, function ($class) {
    return str_starts_with($class, 'fa-');
  });

  // Check ACF fields for icon settings
  $use_icon = get_field('use_icon', $item);

  if ($use_icon == true) {
    $menu_item_icon = get_field('menu_item_icon', $item);
    if ($menu_item_icon) {
      $fa_classes[] = sanitize_html_class($menu_item_icon);
    }
    $use_label_with_icon = get_field('display_label_with_icon', $item);
  }

  if (!empty($fa_classes)) {
    // If Font Awesome classes exist, inject them into the icon span
    $return_value = '<span class="menu-icon fa-fw ' . esc_attr(implode(' ', $fa_classes)) . '"></span>';

    // Check if we should show only the icon or icon with label
    if (isset($use_label_with_icon) && $use_label_with_icon == false) {
      return $return_value;
    }
    return $return_value . '<span class="menu-text">' . $title . '</span>';
  }

  // Return title without an icon if no Font Awesome classes are found
  return '<span class="menu-text">' . $title . '</span>';
}

add_filter('nav_menu_item_title', 'baum_menu_item_fontawesome', 10, 4);

// /**
//  * Adds notifications dropdown to main menu for logged-in users
//  *
//  * This function adds a notifications bell icon with a dropdown menu to the
//  * main menu for logged-in users. The dropdown contains the notifications
//  * menu defined in the WordPress admin.
//  *
//  * @param string $items The existing menu items HTML
//  * @param stdClass $args The menu arguments object
//  * @return string Modified menu items HTML with notifications dropdown
//  *
//  * @since 1.0.0
//  */
// function add_notifications_with_dropdown_to_menu ($items, $args) {
//   // Check if the user is logged in and if it's the primary menu
//   if (is_user_logged_in()
//     && ($args->theme_location === 'main-menu-right'
//     || $args->theme_location === 'minimal-menu-right')) {
//     $current_user = wp_get_current_user();

//     // Add the notifications icon with dropdown menu
//     $items .= '<li class="menu-item user-notifications-menu-item">
//                 <a href="#" id="user-notifications-trigger"><i class="fa-fw fa-solid fa-bell"></i></a>
//                 <ul id="main-notifications-menu" class="sub-menu" style="display:none;">'
//                   . wp_nav_menu([
//                     'theme_location'  => 'main-notifications-menu',
//                     'container'       => false,
//                     'items_wrap'      => '%3$s',
//                     'echo'            => false,
//                     'menu_class'      => 'baum-notifications-menu',
//                   ]) . '
//                 </ul>
//               </li>';
//   }
//   return $items;
// }

// add_filter('wp_nav_menu_items', 'add_notifications_with_dropdown_to_menu', 10, 2);

/**
 * Adds user avatar dropdown to main menu for logged-in users
 *
 * This function adds the current user's avatar with a dropdown menu to the
 * main menu for logged-in users. The dropdown contains the user menu
 * defined in the WordPress admin.
 *
 * @param string $items The existing menu items HTML
 * @param stdClass $args The menu arguments object
 * @return string Modified menu items HTML with avatar dropdown
 *
 * @since 1.0.0
 */
function add_avatar_with_dropdown_to_menu($items, $args) {
  // Check if the user is logged in and if it's the primary menu
  if (is_user_logged_in()
    && ($args->theme_location === 'main-menu-right'
    || $args->theme_location === 'minimal-menu-right')) {
    $current_user = wp_get_current_user();
    $avatar = get_avatar($current_user->ID, 28);

    $items .= '<li class="menu-item user-avatar-menu-item baum-account-dropdown-link">' 
           . do_shortcode('[baum_account_dropdown style="avatar"]') 
           . '</li>'; 

    // // Add the avatar with dropdown menu
    // $items .= '<li class="menu-item user-avatar-menu-item">
    //             <a href="#" id="user-avatar-trigger">' . $avatar . '</a>
    //             <ul id="main-user-menu" class="sub-menu" style="display: none;">'
    //               . wp_nav_menu([
    //                 'theme_location' => 'main-user-menu',
    //                 'container' => false,
    //                 'items_wrap' => '%3$s',
    //                 'echo' => false,
    //                 'menu_class'     => 'baum-avatar-menu',
    //               ]) . '
    //             </ul>
    //           </li>';
  }
  return $items;
}

add_filter('wp_nav_menu_items', 'add_avatar_with_dropdown_to_menu', 10, 2);

//
// <a href='#' data-tooltip-id='#id-guide-step-1'></a>
//
// <div id='id-guide-step-1'>
//   <h5>Guide</h5>
//  <span>Check this feature out.</span>
// </div>
//

// add_action('wp', function () {
//   echo "
//     <script>
//       $('[data-tooltip-id]').forEach(function (element, index) {
//         var id = $(this).data('tooltip-id');
//         var html = $(id).html();
//         $(this).tooltipster({
//           content: $(html),
//           contentAsHTML: true,
//           animation: 'grow',
//           theme: 'tooltipster-borderless',
//           trigger: 'click, tap',
//           delay: 0,
//         });
//       });
//     </script>";
// });

/////////////////////////////////////
// Calculate Reading Time
/////////////////////////////////////

/**
 * Calculates the estimated reading time for a post
 *
 * This function counts the number of words in a post's content and
 * calculates how long it would take to read based on an average reading
 * speed of 200 words per minute. The result is rounded up to the nearest
 * minute.
 *
 * @param int $post_id The ID of the post
 * @return int The estimated reading time in minutes
 *
 * @since 1.0.0
 */
function calculate_reading_time ($post_id) {
  // Get the content of the post
  $content = get_post_field('post_content', $post_id);

  // Strip tags and count the words
  $word_count = str_word_count(strip_tags($content));

  // Define average reading speed (words per minute)
  $reading_speed = 200;

  // Calculate the reading time in minutes
  $reading_time = ceil($word_count / $reading_speed);

  return $reading_time;
}

/////////////////////////////////////
// Add color to widget titles
/////////////////////////////////////

/**
 * Applies custom styling to widget titles
 *
 * This function modifies widget titles by adding custom background colors,
 * text colors, and CSS classes based on ACF fields set for each widget.
 * It wraps the widget title in div elements with the appropriate classes
 * to achieve the desired styling.
 *
 * @param array $params The widget parameters
 * @return array Modified widget parameters
 *
 * @since 1.0.0
 */
function baum_filter_widget_title ($params) {
  $term = 'widget_' . $params[0]['widget_id'];

  $heading_bg_color = get_field('heading_background_color', $term) ?? '';
  // 'baum-bg-secondary';
  $heading_text_color = get_field('heading_text_color', $term) ?? '';
  // 'baum-text-white';
  $heading_css = get_field('heading_css', $term) ?? '';

  // $t_icon = $heading_css;
  // $t_bg_c = $background;
  // $t_text_c = 'baum-text-' .
  // $t_text_c = $heading_text_color;
  // baum-bg baum-width-100
  // . '<i class="' . $title_icon . '"></i>'

  $params[0]['before_title'] = '<div class="baum-title-width"><div class="' . $heading_bg_color . ' ' . $heading_text_color . ' ' . $heading_css . '">' . $params[0]['before_title'];
  $params[0]['after_title'] = $params[0]['after_title'] . '</div></div>';
  return $params;
}

add_filter('dynamic_sidebar_params', 'baum_filter_widget_title');

/////////////////////////////////////
//
/////////////////////////////////////

// function display_all_month_calendars_from_date_url () {
//   global $wp_query;
//   // Get year and month from the URL or fallback to current date
//   $current_year = get_query_var('year') ? intval(get_query_var('year')) : date('Y');
//   $current_month = get_query_var('monthnum') ? intval(get_query_var('monthnum')) : date('n');
//   // Loop through all 12 months
//   for ($month = 1; $month <= 12; $month++) {
//       // Determine if the current month should be active
//       $is_active = ($month == $current_month) ? ' active' : '';
//       // Start a container for each calendar
//       echo '<div class="calendar-container center' . $is_active . '" style="margin-bottom:0px;text-align:left;">';
//       echo '<strong class="center">' . date('F', mktime(0, 0, 0, $month, 1)) . ' ' . $current_year . '</strong>';
//       // Display the calendar for the given month
//       echo get_calendar(false, true, array('monthnum' => $month, 'year' => $current_year));
//       // End container
//       echo '</div>';
//   }
// }

/**
 * Displays a custom calendar for a specific year and month
 *
 * This function generates a WordPress calendar widget for a specific month
 * and year while preserving the global calendar state. It temporarily
 * modifies global variables to generate the calendar and then restores them.
 *
 * @param int $year The year for the calendar
 * @param int $month The month for the calendar (1-12)
 * @return string The HTML output of the calendar
 *
 * @since 1.0.0
 */
function display_custom_calendar ($year, $month) {
  global $wpdb, $m, $monthnum, $year, $wp_locale;

  // Backup global variables to avoid conflicts
  $original_monthnum = $monthnum;
  $original_year = $year;

  // Set year and month for this calendar
  $monthnum = absint($month);
  $year = absint($year);

  // Start capturing calendar HTML
  ob_start();

  // Generate the calendar
  get_calendar(false);

  // Restore global variables
  $monthnum = $original_monthnum;
  $year = $original_year;

  // Return the calendar HTML
  return ob_get_clean();
}

/**
 * Displays all 12 months of calendars for the current year
 *
 * This function renders a full year view with all 12 monthly calendars.
 * It highlights the current month and provides navigation links for each month.
 * The year and month are determined from URL parameters or current date.
 *
 * @return void Outputs HTML for all 12 monthly calendars
 *
 * @since 1.0.0
 */
function display_all_calendars_with_links () {
  // Get current year and month for defaults
  $current_year = get_query_var('year') ? absint(get_query_var('year')) : absint(date('Y'));
  $current_month = get_query_var('monthnum') ? absint(get_query_var('monthnum')) : absint(date('n'));

  // Loop through all 12 months
  for ($month = 1; $month <= 12; $month++) {
    // Determine if this month is the active one
    $is_active = ($month == $current_month) ? ' active' : '';

    // Start a container for each calendar
    echo '<div class="calendar-container center' . esc_attr($is_active) . '" style="margin:0 auto;text-align:left;vertical-align:top;">';
    echo '<strong class="center">' . esc_html(date('F', mktime(0, 0, 0, $month, 1))) . ' ' . absint($current_year) . '</strong>';

    // Display the calendar for the given month
    echo display_custom_calendar($current_year, $month);
    echo '</div>';
  }
}

/**
 * Displays a five-month calendar view centered on the current month
 *
 * This function shows a range of 5 calendars: 2 months before the current month,
 * the current month (highlighted), and 2 months after. It handles year boundaries
 * correctly and provides navigation links for each month.
 *
 * @return void Outputs HTML for five monthly calendars
 *
 * @since 1.0.0
 */
function display_five_month_calendars () {
  global $wpdb, $m, $monthnum, $year, $wp_locale;

  // Get year and month from the URL or fallback to current date
  $current_year = get_query_var('year') ? absint(get_query_var('year')) : absint(date('Y'));
  $current_month = get_query_var('monthnum') ? absint(get_query_var('monthnum')) : absint(date('n'));

  // Calculate the range of months to display (-2, current, +2)
  $months_to_display = range($current_month - 2, $current_month + 2);

  // Start rendering calendars
  foreach ($months_to_display as $month_offset) {
    // Normalize the year and month (handles cases like month=0 or month=13)
    $adjusted_date = mktime(0, 0, 0, $month_offset, 1, $current_year);
    $display_month = absint(date('n', $adjusted_date));
    $display_year = absint(date('Y', $adjusted_date));

    // Determine if this month is active
    $is_active = ($display_month == $current_month && $display_year == $current_year) ? ' active' : '';

    // Backup global variables to avoid conflicts
    $original_monthnum = $monthnum;
    $original_year = $year;

    // Set global variables for the calendar instance
    $monthnum = $display_month;
    $year = $display_year;

    // Capture calendar output
    ob_start();
    get_calendar(false);
    $calendar_html = ob_get_clean();

    // Restore global variables
    $monthnum = $original_monthnum;
    $year = $original_year;

    // Start a container for each calendar
    echo '<div style="margin:5px;">';
    echo '<div class="baum-card calendar-container center' . esc_attr($is_active) . '" style="margin:0 auto;text-align:left;vertical-align:top;">';
    echo '<a href="/' . absint($display_year) . '/' . absint($display_month) . '">' . esc_html(date('F Y', $adjusted_date)) . '</a>';
    echo $calendar_html;
    echo '</div>';
    echo '</div>';
  }
}

// //
// // Register Places Taxonomy
// // - taxonomy is (a hack) used by both `user` and `story`
// //

// function register_shared_places_taxonomy() {
//   $args = array(
//       'labels'            => array(
//           'name'          => 'Places',
//           'singular_name' => 'Place',
//       ),
//       'public'            => true,
//       'hierarchical'      => true,
//       'show_admin_column' => true,
//       'show_in_rest'      => true, // Enable for Gutenberg
//       'rewrite'           => [ 'slug' => 'places' ],
//   );

//   // Register "Places" for both posts and users
//   register_taxonomy('places', ['post', 'user'], $args);
// }

// add_action('init', 'register_shared_places_taxonomy');

/**
 * Links book people to WordPress users when a post is saved
 *
 * This function automatically links people mentioned in book ACF fields
 * (book_subject_people and book_author) to actual WordPress users based on
 * display name and user nicename matching. The linked user IDs are stored
 * in post meta for later use.
 *
 * @param int $post_id The ID of the post being saved
 * @return void
 *
 * @since 1.0.0
 *
 * @todo Handle cases where multiple users have the same display name or nicename
 */
function link_book_people_to_post ($post_id) {
  // Only process posts
  if (get_post_type($post_id) !== 'post') {
      return;
  }

  $linked_users = [];

  // Gather names from both ACF fields
  $people_names = get_field('book_subject_people', $post_id);
  $author_names = get_field('book_author', $post_id);

  $all_names = [];

  // Split multiple subject people (comma-separated)
  if ($people_names) {
      $all_names = array_merge($all_names, array_map('trim', explode(',', $people_names)));
  }

  // Split multiple authors (comma-separated)
  if ($author_names) {
      $all_names = array_merge($all_names, array_map('trim', explode(',', $author_names)));
  }

  // Remove empty values and sanitize
  $all_names = array_filter(array_map('sanitize_text_field', $all_names));

  // Search for each person name in the WordPress users table
  foreach ($all_names as $person_name) {
      $user_query = new WP_User_Query([
          'search'         => '*' . esc_attr($person_name) . '*',
          'search_columns' => ['display_name', 'user_nicename'],
      ]);

      $users = $user_query->get_results();

      if (!empty($users)) {
          foreach ($users as $user) {
              $linked_users[] = absint($user->ID);
          }
      }
  }

  // Remove duplicates and update meta field
  $linked_users = array_unique($linked_users);
  update_post_meta($post_id, '_linked_users', $linked_users);
}

add_action('save_post', 'link_book_people_to_post');

/**
 * Links book places to the places taxonomy when a post is saved
 *
 * This function automatically creates taxonomy terms for places mentioned
 * in the book_subject_places ACF field and associates them with the post.
 * If a place term doesn't exist, it creates it automatically.
 *
 * @param int $post_id The ID of the post being saved
 * @return void
 *
 * @since 1.0.0
 */
function link_book_places_to_taxonomy($post_id) {
  // Only process posts
  if (get_post_type($post_id) !== 'post') {
      return;
  }

  // Get places from ACF field
  $places = get_field('book_subject_places', $post_id);
  if (!$places) {
      return;
  }

  // Split comma-separated places and sanitize
  $places_array = array_map('trim', explode(', ', $places));
  $places_array = array_filter(array_map('sanitize_text_field', $places_array));
  $linked_terms = [];

  foreach ($places_array as $place_name) {
      // Check if the term already exists
      $term = get_term_by('name', $place_name, 'places');

      // Create the term if it doesn't exist
      if (!$term) {
          $term = wp_insert_term($place_name, 'places');
      }

      // Add term ID to linked terms if successful
      if (!is_wp_error($term)) {
          $term_id = is_array($term) ? $term['term_id'] : $term->term_id;
          $linked_terms[] = absint($term_id);
      }
  }

  // Associate the terms with the post
  if (!empty($linked_terms)) {
      wp_set_object_terms($post_id, $linked_terms, 'places', false);
  }
}

add_action('save_post', 'link_book_places_to_taxonomy');

/**
 * Converts ISBN-10 to ISBN-13 format
 *
 * This function takes a 10-digit ISBN and converts it to the 13-digit format
 * by adding the "978" prefix and calculating the new check digit according
 * to the ISBN-13 algorithm.
 *
 * @param string $isbn10 The 10-digit ISBN to convert
 * @return string|false The 13-digit ISBN or false if invalid input
 *
 * @since 1.0.0
 */
function convert_isbn10_to_isbn13 ($isbn10) {
  // Validate input length
  if (strlen($isbn10) !== 10) {
      return false; // Not a valid ISBN-10
  }

  // Create ISBN-13 base by adding "978" prefix and removing check digit
  $isbn13_base = '978' . substr($isbn10, 0, 9);
  $sum = 0;

  // Compute new ISBN-13 check digit using the algorithm
  for ($i = 0; $i < 12; $i++) {
      $digit = (int) $isbn13_base[$i];
      $sum += ($i % 2 === 0) ? $digit : $digit * 3;
  }

  // Calculate and append the new check digit
  $check_digit = (10 - ($sum % 10)) % 10;
  return $isbn13_base . $check_digit;
}

/**
 * Fetches book data from Open Library API using ISBN
 *
 * This function automatically retrieves book information from the Open Library API
 * when a post is saved that has book-related tags/categories and an ISBN field.
 * It populates ACF fields with book metadata including title, author, subjects,
 * and downloads the cover image as a featured image.
 *
 * @param int $post_id The ID of the post being saved
 * @return void
 *
 * @since 1.0.0
 */
function fetch_book_data_from_isbn ($post_id) {

  // Only process posts
  if (get_post_type($post_id) !== 'post') {
      return;
  }

  // Only process posts tagged or categorized as books
  if (!has_tag('Book')
  && !has_tag('Books')
  && !has_category('Book')
  && !has_category('Books')) return;

  $isbn = get_field('isbn_number', $post_id);
  $isbn10 = get_field('isbn_10', $post_id);
  $isbn13 = get_field('isbn_13', $post_id);
  $isbn10 = $isbn10 ? $isbn10 : $isbn;
  $isbn = $isbn ? $isbn : $isbn10;

  // error_log('$isbn: ' . $isbn);

  // Convert ISBN-10 to ISBN-13 if ISBN-13 is missing
  if (!$isbn13 && $isbn10) {
      $isbn13 = convert_isbn10_to_isbn13($isbn10);
  }

  // Use ISBN-13 if available, otherwise fallback to ISBN-10
  $isbn = $isbn13 ? $isbn13 : $isbn10;

  if (!$isbn) {
      return; // No valid ISBN available
  }

  // Now we are guaranteed to be using ISBN-13 if possible
  $api_url = "https://openlibrary.org/api/books?bibkeys=ISBN:{$isbn}&format=json&jscmd=data";
  $response = wp_remote_get($api_url);

  // error_log('$response' . print_r($response, true));

  if (is_wp_error($response)) {
      return;
  }

  $body = wp_remote_retrieve_body($response);
  $data = json_decode($body, true);

  error_log('$data' . print_r($data, true));

  if (empty($data["ISBN:$isbn"])) {
      return;
  }

  $book = $data["ISBN:$isbn"];

  // Extracting book details
  $title = $book['title'] ?? '';
  $authors = isset($book['authors']) ? implode(', ', array_column($book['authors'], 'name')) : '';
  $publish_date = $book['publish_date'] ?? '';
  $publisher = isset($book['publishers']) ? implode(', ', array_column($book['publishers'], 'name')) : '';

  error_log('$book[cover]: ' . print_r($book['cover'], true));

  $cover_url = $book['cover']['large'] ?? '';
  $weight = $book['weight'] ?? '';
  $page_count = $book['number_of_pages'] ?? $book['pagination'] ? $book['pagination'] : '';

  // Extracting identifiers
  $identifiers = $book['identifiers'] ?? [];
  $isbn_10 = isset($identifiers['isbn_10']) ? implode(', ', $identifiers['isbn_10']) : '';
  $isbn_13 = isset($identifiers['isbn_13']) ? implode(', ', $identifiers['isbn_13']) : '';
  $lccn = isset($identifiers['lccn']) ? implode(', ', $identifiers['lccn']) : '';
  $oclc = isset($identifiers['oclc']) ? implode(', ', $identifiers['oclc']) : '';

  // Expanded and refined categorization
  $topics = [];
  $people = [];
  $places = [];
  $times = [];

  if (!empty($book['subjects'])) {
    foreach ($book['subjects'] as $subject) {
      $name = $subject['name'] ?? '';

      // Expanded regex for time periods (historical events, centuries, eras)
      if (preg_match('/\b(century|era|revolution|age|war|period|dynasty|renaissance|millennium|empire|epoch|antiquity|golden age|cold war|great depression|middle ages|dark ages|industrial revolution|atomic age|space race|roaring twenties)\b/i', $name)) {
        $times[] = $name;
      }
      // Expanded regex for people (titles, professions, famous figures)
      elseif (preg_match('/\b(king|queen|prince|princess|duke|duchess|emperor|empress|czar|pharaoh|shah|sultan|chieftain|warlord|knight|bishop|pope|cardinal|monk|priest|rabbi|imam|shaman|prophet|philosopher|scientist|astronomer|mathematician|engineer|inventor|explorer|pioneer|artist|poet|author|playwright|musician|composer|conductor|actor|filmmaker|president|prime minister|chancellor|governor|mayor|senator|congressman|dictator|general|commander|soldier|revolutionary|martyr|activist|civil rights leader|spy|assassin|criminal|outlaw|gangster|pirate|samurai|ninja|knight templar|templar|businessman|entrepreneur|mogul|tycoon|CEO|founder|athlete|sportsman|coach|manager|doctor|surgeon|physician|nurse|healer|psychologist|psychiatrist|lawyer|judge|detective|policeman|sheriff|deputy|cowboy|gunslinger)\b/i', $name)) {
        $people[] = $name;
      }
      // Expanded regex for places (geographical locations, landmarks)
      elseif (preg_match('/\b(city|town|village|metropolis|municipality|kingdom|empire|state|nation|country|province|region|territory|colony|district|zone|area|island|archipelago|peninsula|mountain|valley|ridge|canyon|plateau|forest|rainforest|desert|savanna|tundra|prairie|river|lake|ocean|sea|bay|gulf|strait|channel|fjord|lagoon|wetland|marsh|swamp|cave|glacier|volcano|crater|highland|lowland|grassland|tundra|basin|tropics|equator|antarctic|arctic|tropics|earth|moon|mars|venus|jupiter|saturn|uranus|neptune|pluto|solar system|galaxy|milky way|universe)\b/i', $name)) {
        $places[] = $name;
      }
      // Everything else is classified as a general topic
      else {
        $topics[] = $name;
      }
    }
  }

  // Update ACF fields
  update_field('book_title', $title, $post_id);
  update_field('book_author', $authors, $post_id);
  update_field('book_publish_date', $publish_date, $post_id);
  update_field('book_publisher', $publisher, $post_id);
  update_field('book_weight', $weight, $post_id);
  update_field('book_page_count', $page_count, $post_id);
  update_field('book_subject_topics', implode(', ', $topics), $post_id);
  update_field('book_subject_people', implode(', ', $people), $post_id);
  update_field('book_subject_places', implode(', ', $places), $post_id);
  update_field('book_subject_times', implode(', ', $times), $post_id);

  if (!empty($cover_url)) {
    error_log('download_and_attach_image');
    $attachment_id = download_and_attach_image($cover_url, $post_id);
    if ($attachment_id) {

      error_log('attachment_id: ' . $attachment_id);

      // Get existing featured images from new system
      $existing_images = get_featured_images($post_id);
      $existing_images[] = $attachment_id;

      error_log('images: ' . print_r($existing_images, true));
      $existing_images = array_unique($existing_images);

      // Use new system to set featured images
      Baum_Featured_Images::set_featured_images($post_id, $existing_images);
    }
  }

  // Update identifiers
  update_field('book_identifiers', compact('isbn_10', 'isbn_13', 'lccn', 'oclc'), $post_id);
}

add_action('save_post', 'fetch_book_data_from_isbn');

/**
 * Wrap the first occurrence of specified delimiters and everything before it in a span with a specific class in post titles.
 *
 * Delimiters include colon (:), long dash (–), brackets ([]), rounded brackets (()), carets (<>), and tildes (`word`).
 * Tildes will be stripped out on the frontend but remain in the backend.
 *
 * @param string $title The original post title.
 * @param int $id The post ID.
 * @return string The modified post title.
 */

/**
 * Formats post titles with special styling for delimited text
 *
 * This function processes post titles and applies special styling to text
 * enclosed in backticks (`). It replaces the backticks with a span element
 * that has primary background color and white text, creating a highlighted
 * effect in the title.
 *
 * @param string $title The post title
 * @param int    $id    The post ID
 * @return string The formatted title
 *
 * @since 1.0.0
 */
function wrap_first_delimiter_in_title ($title, $id) {
  // Array of delimiters to search for

  // Handle tildes globally after processing the first delimiter
  $title = preg_replace_callback('/`([^`]+)`/', function($matches) {
    // Wrap the text within tildes in a span and remove tildes
    return '<span class="primary-bg white">' . $matches[1] . '</span>';
  }, $title);

  return $title;
}

add_filter('the_title', 'wrap_first_delimiter_in_title', 10, 2);

//
//
//

/**
 * Applies special styling to post titles with specific delimiters
 *
 * This function looks for specific delimiters (like '–') in post titles
 * and applies custom styling to the text before and including the delimiter.
 * It creates a visually distinct heading element with background color
 * and white text.
 *
 * @param string $title The post title
 * @return string The styled title
 *
 * @since 1.0.0
 */
function style_the_title ($title) {
  $delimiters = ['–']; // '<'];

  // Check if we're in the main query and it's a single post/page view
  // if (is_single($id) || is_page($id)) {
    foreach ($delimiters as $delimiter) {
      if (strpos($title, $delimiter) !== false) {
        // Split the title into two parts: before the first delimiter and after the first delimiter
        list($before_delimiter, $after_delimiter) = explode($delimiter, $title, 2);

        // Wrap the text before and including the first delimiter in a span with the desired style
        // $wrapped_text = '<span class="quinary">' . $before_delimiter . $delimiter . '</span>';
        // $wrapped_text = '<span class="secondary-bg denary" style="letter-spacing:0px;display:block;border-radius:var(--border-radius);font-size:40%;line-height:1;padding:10px;width:auto;clear:both;margin-bottom:10px;">'
        // . $before_delimiter . $delimiter . '</span>';
        $wrapped_text = '<span class="baum-heading baum-heading tertiary-bg white" style="margin-bottom:10px;">'
        . $before_delimiter . $delimiter . '</span>';

        // Combine the wrapped text with the remaining title
        $title = $wrapped_text . $after_delimiter;
        break; // Only wrap the first found delimiter
      }
    }
  // }
  return $title;
}

// oEmbed functions have been moved to functions-ajax.php

/////////////////////////////////////
// Popular Posts (deprecated)
//
// INFO: this functionality was done away with in favor of Post Views Counter
// because the plugin has charts that help with determining site traffic
//
/////////////////////////////////////

// function getCrunchifyPostViews ($postID) {
//   $count_key = 'post_views_count';
//   $count = get_post_meta($postID, $count_key, true);
//   if ($count == '') {
//     delete_post_meta($postID, $count_key);
//     add_post_meta($postID, $count_key, '0');
//     return "0 Views";
//   }
//   return number_format($count) . ' Views';
// }

// function setCrunchifyPostViews ($postID) {
//   $count_key = 'post_views_count';
//   $count = get_post_meta($postID, $count_key, true);
//   if ($count == '') {
//     $count = 0;
//     delete_post_meta($postID, $count_key);
//     add_post_meta($postID, $count_key, '0');
//   } else {
//     $count++;
//     update_post_meta($postID, $count_key, $count);
//   }
// }

/////////////////////////////////////
// Related Posts (deprecated)
//
// INFO: This functionality is now handled by the Baum Flexible Widget
//
/////////////////////////////////////

// function baum_related_posts ($baum_related_num) {
//   global $post;
//   global $do_not_duplicate;
//   $orig_post = $post;
//   $return_value = 0;
//   $tags = wp_get_post_tags($post->ID);
//     if ($tags) {
//       $tag_ids = array();
//       foreach ($tags as $individual_tag) $tag_ids[] = $individual_tag->term_id;
//       $args = array(
//         'tag__in' => $tag_ids,
//         'order' => 'DESC',
//         'orderby' => 'date',
// 				'post__not_in' => $do_not_duplicate,
//         'posts_per_page'=> $baum_related_num,
//         'ignore_sticky_posts'=> 1
//       );
//       $my_query = new WP_Query($args);
//       $return_value = $my_query;

//     }
//   $post = $orig_post;
//   return $return_value;
// }

/////////////////////////////////////
// Change Label For Posts To Stories
/////////////////////////////////////

/**
 * Changes the WordPress default "Posts" labels to "Stories"
 *
 * This function modifies the labels of the default WordPress post type
 * to use "Stories" instead of "Posts" throughout the admin interface.
 * It also changes the menu icon to better represent the content type.
 *
 * @return void
 *
 * @since 1.0.0
 */
function change_posts_to_stories () {
  $get_post_type = get_post_type_object('post');
  $get_post_type->menu_icon = 'dashicons-edit-large';
  $labels = $get_post_type->labels;
  $labels->name = 'Stories';
  $labels->singular_name = 'Story';
  $labels->add_new = 'Add Story';
  $labels->add_new_item = 'Add Story';
  $labels->edit_item = 'Edit Story';
  $labels->new_item = 'Story';
  $labels->view_item = 'View Story';
  $labels->search_items = 'Search Stories';
  $labels->not_found = 'No Stories found';
  $labels->not_found_in_trash = 'No Stories found in Trash';
  $labels->all_items = 'All Stories';
  $labels->menu_name = 'Stories';
  $labels->name_admin_bar = 'Story';
}

add_action('init', 'change_posts_to_stories');

/**
 * Changes the WordPress default "Categories" labels to "Topics"
 *
 * This function modifies the labels of the default WordPress category taxonomy
 * to use "Topics" instead of "Categories" throughout the admin interface.
 * This provides more semantic meaning for content organization.
 *
 * @return void
 *
 * @since 1.0.0
 */
function change_categories_to_topics () {
  global $wp_taxonomies;
  $labels = &$wp_taxonomies['category']->labels;
  $labels->name = 'Topics';
  $labels->singular_name = 'Topic';
  $labels->add_new = 'Add Topic';
  $labels->add_new_item = 'Add Topic';
  $labels->edit_item = 'Edit Topic';
  $labels->new_item = 'Topic';
  $labels->view_item = 'View Topic';
  $labels->search_items = 'Search Topics';
  $labels->not_found = 'No Topics found';
  $labels->not_found_in_trash = 'No Topics found in Trash';
  $labels->all_items = 'All Topics';
  $labels->menu_name = 'Topics';
  $labels->name_admin_bar = 'Topics';
}

add_action('init', 'change_categories_to_topics');

/////////////////////////////////////
//
// Use meta data in single.php or elsewhere like this
//
// get_post_meta($post->ID, "baum_video_embed", true)
// Values: null / abc123
//
// get_post_meta($post->ID, "baum_post_template", true)
// Values: temp1 / temp2 // ... / temp7
//
// get_post_meta($post->ID, "baum_featured_image", true)
// Values: show / hide
// Values: on / off
//
// get_post_meta($post->ID, "baum_post_sidebar", true)
// Values: show / hide
// Values: on / off
//
// get_post_meta($post->ID, "baum_post_author", true)
// Values: show / hide / guest_only
// Values: on / off / guest_only
//
/////////////////////////////////////


/////////////////////////////////////
// Add 5 / 30 / 60 / 90 / 180 / 360 minute crons
/////////////////////////////////////

// function my_cron_schedules ($schedules) {
//   if (!isset($schedules["5min"])) {
//     $schedules["5min"] = array(
//       'interval' => 5*60,
//       'display' => __('Once every 5 minutes')
//     );
//   }
//   if (!isset($schedules["30min"])) {
//     $schedules["30min"] = array(
//       'interval' => 30*60,
//       'display' => __('Once every 30 minutes')
//     );
//   }
//   if (!isset($schedules["60min"])) {
//     $schedules["60min"] = array(
//       'interval' => 60*60,
//       'display' => __('Once every 60 minutes')
//     );
//   }
//   if (!isset($schedules["90min"])) {
//     $schedules["90min"] = array(
//       'interval' => 90*60,
//       'display' => __('Once every 90 minutes')
//     );
//   }
//   if (!isset($schedules["180min"])) {
//     $schedules["180min"] = array(
//       'interval' => 180*60,
//       'display' => __('Once every 180 minutes')
//     );
//   }
//   if (!isset($schedules["360min"])) {
//     $schedules["360min"] = array(
//       'interval' => 360*60,
//       'display' => __('Once every 360 minutes')
//     );
//   }
//   if (!isset($schedules["720min"])) {
//     $schedules["720min"] = array(
//       'interval' => 720*60,
//       'display' => __('Once every 720 minutes')
//     );
//   }
//   return $schedules;
// }

// add_filter('cron_schedules', 'my_cron_schedules');

/////////////////////////////////////
// Breaking News Expiration Cron Job
/////////////////////////////////////

// function schedule_update_cron () {
//   wp_schedule_event(time(), '5min', 'update_crons_hook');
// }

// if (!wp_next_scheduled('update_crons_hook')) {
//   add_action('init', 'schedule_update_cron');
// }

// function baum_update_crons () {
//   wp_clear_scheduled_hook('tag_hook');
// }

// add_action('update_crons_hook', 'baum_update_crons');

// function schedule_tag_cron () {
//   $baum_breaking_news_cron = get_theme_mod('baum_breaking_news_cron', 180);
//   // wp_schedule_event(time(), $baum_breaking_news_cron, 'tag_hook');
//   $time = time() + ($baum_breaking_news_cron * 60);
//   wp_schedule_single_event($time, 'tag_hook');
// }

// if (!wp_next_scheduled('tag_hook')) {
//   add_action('init', 'schedule_tag_cron');
// }

// function baum_expire_breaking_tag () {
//   $the_query = new WP_Query([ 'tag' => 'breaking' ]);
//   while ($the_query->have_posts()) {
//     if ($the_query->have_posts()) {
//       $the_query->the_post();
//       $post_id = get_the_ID();
//       $post_tags = wp_get_post_terms($post_id, 'post_tag', [
//         'fields' => 'slugs'
//       ]);
//       $pos = array_search('breaking', $post_tags);
//       if (false !== $pos) {
//         unset($post_tags[$pos]);
//         wp_set_post_terms($post_id, $post_tags, 'post_tag');
//       }
//     }
//   }
// }

// add_action('tag_hook', 'baum_expire_breaking_tag');

//
//
//

/**
 * Gets unique email addresses of authors from a specific story collection
 *
 * This function queries posts within a specified story collection taxonomy term
 * and collects unique email addresses from both the main post authors and any
 * additional authors specified in ACF fields.
 *
 * @param string $taxonomy_term The slug of the story collection taxonomy term
 * @return string Comma-separated string of unique email addresses
 *
 * @since 1.0.0
 */
function get_authors_emails_by_story_collection($taxonomy_term) {
  // Initialize an array to store unique email addresses
  $email_addresses = [];

  // Sanitize the taxonomy term
  $taxonomy_term = sanitize_text_field($taxonomy_term);

  // Query posts in the specified taxonomy term
  $query = new WP_Query([
      'post_type'      => ['story', 'post'], // Adjust to your post types
      'tax_query'      => [
          [
              'taxonomy' => 'story-collection', // Taxonomy name
              'field'    => 'slug',
              'terms'    => $taxonomy_term, // Term slug
          ],
      ],
      'posts_per_page' => -1, // Get all posts
  ]);

  if ($query->have_posts()) {
      while ($query->have_posts()) {
          $query->the_post();

          // Get the post author's email
          $post_author_id = get_the_author_meta('ID');
          $post_author_email = get_the_author_meta('user_email', $post_author_id);
          if ($post_author_email && is_email($post_author_email) && !in_array($post_author_email, $email_addresses)) {
              $email_addresses[] = sanitize_email($post_author_email);
          }

          // Get additional authors from the ACF field
          $additional_authors = get_field('additional_authors'); // Adjust field name as needed
          if (!empty($additional_authors) && is_array($additional_authors)) {
              foreach ($additional_authors as $author) {
                  if (!empty($author['ID'])) {
                      $user_data = get_userdata(absint($author['ID']));
                      $additional_author_email = $user_data->user_email ?? null;
                      if ($additional_author_email && is_email($additional_author_email) && !in_array($additional_author_email, $email_addresses)) {
                          $email_addresses[] = sanitize_email($additional_author_email);
                      }
                  }
              }
          }
      }
  }
  wp_reset_postdata();

  // Return a comma-separated string of unique email addresses
  return implode(',', $email_addresses);
}

/**
 * Generates a person card shortcode for story collection authors
 *
 * This helper function creates a baum_person_card shortcode with email addresses
 * from authors in a specific story collection, formatted according to the
 * provided attributes.
 *
 * @param array $atts Shortcode attributes including taxonomy_term, format, and title
 * @return string The generated person card shortcode output
 *
 * @since 1.0.0
 */
function generate_single_baum_person_card_shortcode($atts) {
  // Get the comma-separated list of email addresses
  $email_addresses = get_authors_emails_by_story_collection($atts['taxonomy_term']);

  // Sanitize attributes
  $format = sanitize_text_field($atts['format']);
  $title = sanitize_text_field($atts['title']);

  // Return the person card shortcode with the email addresses
  return do_shortcode("[baum_person_card email='{$email_addresses}' format='{$format}' title='{$title}' float='center' size='64' columns='1' width='100%']");
}

/**
 * Displays authors from a specific story collection as person cards
 *
 * This shortcode function displays all authors (main and additional) from posts
 * within a specified story collection taxonomy term as formatted person cards.
 *
 * @param array $atts Shortcode attributes
 * @return string HTML output of the person cards or error message
 *
 * @since 1.0.0
 *
 * @example [story_collection_authors taxonomy_term="example-collection" format="5" title="Authors"]
 */
function display_story_collection_authors($atts) {
  $atts = shortcode_atts([
    'title' => '',
    'format' => '5',
    'taxonomy_term' => '', // Term slug of the story collection
  ], $atts, 'story_collection_authors');

  // Validate required parameter
  if (empty($atts['taxonomy_term'])) {
      return '<p>Please provide a valid taxonomy term.</p>';
  }

  // Generate and return the person card shortcode
  return generate_single_baum_person_card_shortcode($atts);
}

add_shortcode('story_collection_authors', 'display_story_collection_authors');

//
//
//

/**
 * Gets unique email addresses of influencers from posts
 *
 * This function queries posts (optionally filtered by story collection) and
 * collects unique email addresses from users specified in the 'influencers'
 * ACF field. Can be used to get influencers from all posts or a specific collection.
 *
 * @param string|null $taxonomy_term Optional. The slug of the story collection taxonomy term
 * @return string Comma-separated string of unique influencer email addresses
 *
 * @since 1.0.0
 */
function get_influencer_emails($taxonomy_term = null) {

  // Initialize an array to store unique email addresses
  $email_addresses = [];

  // Set up the query arguments
  $query_args = [
      'post_type'      => ['story', 'post'], // Adjust to your post types
      'posts_per_page' => -1, // Get all posts
  ];

  // If a taxonomy term is provided, add it to the query
  if ($taxonomy_term) {
      $taxonomy_term = sanitize_text_field($taxonomy_term);
      $query_args['tax_query'] = [
          [
              'taxonomy' => 'story-collection', // Taxonomy name
              'field'    => 'slug',
              'terms'    => $taxonomy_term, // Term slug
          ],
      ];
  }

  // Query posts
  $query = new WP_Query($query_args);

  if ($query->have_posts()) {
      while ($query->have_posts()) {
          $query->the_post();

          // Get users from the influencers ACF field
          $influencers = get_field('influencers'); // Adjust field name as needed

          if (!empty($influencers) && is_array($influencers)) {
              foreach ($influencers as $user) {
                  if (!empty($user['ID'])) {
                      $user_data = get_userdata(absint($user['ID']));
                      $influencer_email = $user_data->user_email ?? null;
                      if ($influencer_email && is_email($influencer_email) && !in_array($influencer_email, $email_addresses)) {
                          $email_addresses[] = sanitize_email($influencer_email);
                      }
                  }
              }
          }
      }
  }

  wp_reset_postdata();

  // Return a comma-separated string of unique email addresses
  return implode(',', $email_addresses);
}

//
//
//

/**
 * Generates a person card shortcode for influencers
 *
 * This helper function creates a baum_person_card shortcode with email addresses
 * from influencers, formatted according to the provided attributes with support
 * for multiple columns.
 *
 * @param array $atts Shortcode attributes including taxonomy_term, format, and columns
 * @return string The generated person card shortcode output
 *
 * @since 1.0.0
 */
function generate_influencer_person_card_shortcode($atts) {
  // Get the comma-separated list of influencer email addresses
  $email_addresses = get_influencer_emails($atts['taxonomy_term']);

  // Sanitize attributes
  $format = sanitize_text_field($atts['format']);
  $columns = sanitize_text_field($atts['columns']);

  // Return the person card shortcode with the email addresses
  return do_shortcode("[baum_person_card email='{$email_addresses}' format='{$format}' float='center' size='64' columns='{$columns}' width='100%']");
}

/**
 * Displays influencers as person cards
 *
 * This shortcode function displays influencers from posts (optionally filtered
 * by story collection) as formatted person cards. Supports multiple columns
 * and different display formats.
 *
 * @param array $atts Shortcode attributes
 * @return string HTML output of the influencer person cards
 *
 * @since 1.0.0
 *
 * @example [story_influencers columns="3" format="3"]
 * @example [story_influencers taxonomy_term="example-collection" columns="2" format="5"]
 */
function display_influencers($atts) {
  $atts = shortcode_atts([
    'columns' => '3',
    'format' => '3',
    'taxonomy_term' => '', // Optional: Term slug of the story collection
  ], $atts, 'story_influencers');

  // Generate and return the influencer person card shortcode
  return generate_influencer_person_card_shortcode($atts);
}

add_shortcode('story_influencers', 'display_influencers');

//
// Usage Example
//
//
// For Influencers in All Stories:
// [story_influencers]
//
//
// For Influencers in a Specific Story Collection:
// [story_influencers taxonomy_term="example-term-slug"]
//
//

/**
 * Gets all author email addresses for the current post
 *
 * This function retrieves email addresses from both the main post author
 * and any additional authors specified in the 'additional_authors' ACF field.
 * It can return either a delimited string or an array of author objects.
 *
 * @param string $delimiter The delimiter to use for joining emails (default: ',')
 *                         If empty, returns array of author objects instead
 * @return string|array|null Comma-separated email string, array of authors, or null if no authors
 *
 * @since 1.0.0
 */
function get_all_author_emails ($delimiter = ',') {

  // Get the post author ID
  $post_author_id = get_post_field('post_author', get_the_ID());

  // Retrieve the user object of the post author
  $post_author_object = get_user_by('id', $post_author_id);

  if (!$post_author_object) {
    return null;
  }

  $post_author_object = $post_author_object->to_array();

  // Get the existing additional authors from ACF
  $authors = get_field('additional_authors');

  // Ensure $authors is an array
  // (it might be null if no additional authors are set)
  if (!is_array($authors)) {
    $authors = [];
  }

  // Prepend the post author object to the authors array
  array_unshift($authors, $post_author_object);

  if (!$authors) return null;

  // If delimiter is provided, return email string
  if ($delimiter) {
    // Extract user emails and sanitize them
    $user_emails = array_map(function($author) {
      $email = $author['user_email'] ?? '';
      return is_email($email) ? sanitize_email($email) : '';
    }, $authors);

    // Remove empty emails and convert to a delimited string
    $user_emails = array_filter($user_emails);
    $user_emails_string = implode($delimiter, $user_emails);

    return $user_emails_string;
  }

  // Return the full authors array
  return $authors;
}

/**
 * Gets the playlist slug or ID for a media file attachment
 *
 * This function retrieves the playlist taxonomy term associated with a media
 * attachment and returns either the slug or term ID based on the return parameter.
 * Useful for organizing media files into playlists.
 *
 * @param int $attachment_id The ID of the media attachment
 * @param string $return What to return: 'slug' for term slug, 'id' for term ID
 * @return string|int|false The playlist slug/ID or false if no playlist found
 *
 * @since 1.0.0
 *
 * @example $playlist_slug = baum_get_media_playlist($attachment_id, 'slug');
 * @example $playlist_id = baum_get_media_playlist($attachment_id, 'id');
 */
function baum_get_media_playlist ($attachment_id, $return = 'slug') {
  // Validate attachment ID
  $attachment_id = absint($attachment_id);
  if (!$attachment_id) {
    return false;
  }

  // Get playlist terms for the attachment
  $terms = wp_get_post_terms($attachment_id, 'playlist');

  if (is_wp_error($terms) || empty($terms)) {
      return false;
  }

  // Return the requested format
  return ($return === 'id') ? absint($terms[0]->term_id) : sanitize_title($terms[0]->slug);
}

//
//
//

function baum_recalculate_playlist_counts() {
  $taxonomies = ['playlist']; // Add more if needed
  foreach ($taxonomies as $taxonomy) {
      $terms = get_terms(['taxonomy' => $taxonomy, 'hide_empty' => false]);
      foreach ($terms as $term) {
          wp_update_term_count_now([$term->term_id], $taxonomy);
      }
  }
}

add_action('admin_init', 'baum_recalculate_playlist_counts');

//
//
//

// function enqueue_leaflet_assets() {
//   // Leaflet CSS and JavaScript
//   wp_enqueue_style('leaflet-css', 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css');
//   wp_enqueue_script('leaflet-js', 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js', array(), null, true);
//   // Esri Leaflet Plugin
//   wp_enqueue_script('esri-leaflet', 'https://unpkg.com/esri-leaflet@3.0.10/dist/esri-leaflet.js', array('leaflet-js'), null, true);
// }

// add_action('wp_enqueue_scripts', 'enqueue_leaflet_assets');

/**
 * Email Endpoint Functionality
 *
 * Sets up the email endpoint system for rendering posts/categories
 * optimized for email delivery at domain.com/slug/email
 */

// Include email endpoint functionality
require_once get_template_directory() . '/email-endpoint.php';

/**
 * Adds email endpoint to WordPress rewrite rules
 *
 * This function registers the /email endpoint that can be appended
 * to any post, page, or category URL to get email-optimized content.
 *
 * @return void
 */
function add_email_endpoint() {
  add_rewrite_endpoint('email', EP_ALL);
}
add_action('init', 'add_email_endpoint');

/**
 * Handles email endpoint requests
 *
 * This function intercepts requests to the /email endpoint and
 * renders the appropriate email-optimized content.
 *
 * @return void
 */
function handle_email_endpoint_request() {
  global $wp_query;

  if (isset($wp_query->query_vars['email'])) {
    $wp_query->query_vars['email_endpoint'] = true;
    handle_email_endpoint();
  }
}
add_action('template_redirect', 'handle_email_endpoint_request');

/**
 * Flushes rewrite rules when theme is activated
 *
 * This ensures the email endpoint is properly registered
 * when the theme is first activated.
 *
 * @return void
 */
function flush_email_endpoint_rules() {
  add_email_endpoint();
  flush_rewrite_rules();
}
add_action('after_switch_theme', 'flush_email_endpoint_rules');

/**
 * Adds frontend editing endpoint to WordPress rewrite rules
 *
 * This function registers the /edit endpoint that can be appended
 * to any post URL to enable frontend block editing.
 * Example: site.com/my-post/edit
 *
 * @return void
 */
function baum_add_frontend_edit_endpoint() {
  add_rewrite_endpoint('edit', EP_PERMALINK);
}
add_action('init', 'baum_add_frontend_edit_endpoint');

/**
 * Handles frontend edit endpoint requests
 *
 * This function intercepts requests to the /edit endpoint and
 * loads the frontend block editor template.
 *
 * @return void
 */
function baum_handle_frontend_edit_request() {
  global $wp_query;

  if (isset($wp_query->query_vars['edit'])) {
    // Check if user has permission to edit this post
    if (!is_singular() || !current_user_can('edit_post', get_the_ID())) {
      wp_redirect(home_url());
      exit;
    }

    // Set query var to indicate frontend editing mode
    $wp_query->query_vars['frontend_edit'] = true;

    // Load the frontend editor template
    add_filter('template_include', 'baum_load_frontend_editor_template');
  }
}
add_action('template_redirect', 'baum_handle_frontend_edit_request');

/**
 * Loads the frontend editor template
 *
 * @param string $template The current template path
 * @return string The path to the frontend editor template
 */
function baum_load_frontend_editor_template($template) {
  global $wp_query;

  if (isset($wp_query->query_vars['frontend_edit'])) {
    $frontend_template = locate_template('single-frontend-editor.php');
    if ($frontend_template) {
      return $frontend_template;
    }

    // Fallback to creating the template dynamically
    return get_template_directory() . '/single-frontend-editor.php';
  }

  return $template;
}

/**
 * Flushes rewrite rules when theme is activated to register edit endpoint
 *
 * @return void
 */
function baum_flush_frontend_edit_rules() {
  baum_add_frontend_edit_endpoint();
  flush_rewrite_rules();
}
add_action('after_switch_theme', 'baum_flush_frontend_edit_rules');

/**
 * Temporary function to flush rewrite rules for frontend editor
 * Remove this after testing
 */
function baum_temp_flush_rules() {
  if (isset($_GET['flush_frontend_rules']) && current_user_can('manage_options')) {
    baum_add_frontend_edit_endpoint();
    flush_rewrite_rules();
    wp_redirect(remove_query_arg('flush_frontend_rules'));
    exit;
  }
}
add_action('init', 'baum_temp_flush_rules');

/**
 * Add floating edit button to single posts
 *
 * @return void
 */
function baum_add_floating_edit_button() {
  // Only show on single posts for users who can edit
  if (!is_singular('post') || !current_user_can('edit_post', get_the_ID())) {
    return;
  }

  $edit_url = get_permalink() . 'edit/';
  $admin_edit_url = get_edit_post_link();
  ?>
  <div id="baum-floating-edit" style="
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: flex-end;
  ">
    <!-- Main Edit Button -->
    <a href="<?php echo esc_url($edit_url); ?>" id="baum-floating-edit-btn" style="
      background: var(--color-accent);
      color: white;
      width: 56px;
      height: 56px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
      box-shadow: 0 4px 16px rgba(0,0,0,0.2);
      transition: all 0.3s ease;
      font-size: 18px;
    " title="Edit this post">
      <i class="fas fa-pen"></i>
    </a>

    <!-- Secondary Options (hidden by default) -->
    <div id="baum-floating-edit-options" style="
      display: none;
      flex-direction: column;
      gap: 8px;
      align-items: flex-end;
    ">
      <a href="<?php echo esc_url($admin_edit_url); ?>" style="
        background: var(--color-secondary);
        color: white;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        transition: all 0.3s ease;
        font-size: 16px;
      " title="Admin editor">
        <i class="fas fa-cog"></i>
      </a>

      <a href="<?php echo esc_url(get_preview_post_link()); ?>" target="_blank" style="
        background: var(--color-tertiary);
        color: white;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        transition: all 0.3s ease;
        font-size: 16px;
      " title="Preview post">
        <i class="fas fa-eye"></i>
      </a>
    </div>
  </div>

  <style>
  #baum-floating-edit-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
  }

  #baum-floating-edit-options a:hover {
    transform: scale(1.1);
  }

  @media (max-width: 768px) {
    #baum-floating-edit {
      bottom: 80px; /* Account for mobile navigation */
    }
  }
  </style>

  <script>
  // Handle floating edit button interactions
  document.addEventListener('DOMContentLoaded', function() {
    const editBtn = document.getElementById('baum-floating-edit-btn');
    const options = document.getElementById('baum-floating-edit-options');
    let optionsVisible = false;

    if (editBtn && options) {
      // Long press or right click to show options
      let pressTimer;

      editBtn.addEventListener('mousedown', function(e) {
        if (e.button === 2) { // Right click
          e.preventDefault();
          showOptions();
          return false;
        }

        pressTimer = setTimeout(showOptions, 500); // Long press
      });

      editBtn.addEventListener('mouseup', function() {
        clearTimeout(pressTimer);
      });

      editBtn.addEventListener('mouseleave', function() {
        clearTimeout(pressTimer);
      });

      // Prevent context menu on right click
      editBtn.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
      });

      function showOptions() {
        if (!optionsVisible) {
          options.style.display = 'flex';
          optionsVisible = true;

          // Hide options after 3 seconds
          setTimeout(hideOptions, 3000);
        }
      }

      function hideOptions() {
        options.style.display = 'none';
        optionsVisible = false;
      }

      // Hide options when clicking elsewhere
      document.addEventListener('click', function(e) {
        if (!e.target.closest('#baum-floating-edit')) {
          hideOptions();
        }
      });
    }
  });
  </script>
  <?php
}
add_action('wp_footer', 'baum_add_floating_edit_button');

//
//
//

/**
 * Creates a Leaflet weather map shortcode with multiple weather layers
 *
 * This function generates an interactive weather map using Leaflet.js with
 * OpenWeatherMap data layers and NASA satellite imagery. It includes toggleable
 * weather layers for clouds, precipitation, wind, pressure, temperature, and snow.
 *
 * @param array $atts Shortcode attributes (currently unused)
 * @return string HTML output containing the interactive weather maps
 *
 * @since 1.0.0
 *
 * @example [leaflet_weather_map]
 */
function baum_leaflet_unified_weather_shortcode($atts) {
  $api_key = '140135e33fa723259cf21dbe1c630fff'; // Replace with your actual key
  ob_start();
  ?>
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
  <script src="https://unpkg.com/leaflet-openweathermap/leaflet-openweathermap.js"></script>
  <div id="map" style="height:500px;width:100%;border-radius:var(--border-radius);margin:10px 0;"></div>
  <script>
  var map = L.map('map').setView([37.8, -96], 4);
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '&copy; OpenStreetMap'
  }).addTo(map);
  // Add NASA GIBS MODIS Terra imagery
  L.tileLayer('https://gibs.earthdata.nasa.gov/wmts/epsg3857/best/MODIS_Terra_CorrectedReflectance_TrueColor/default/2023-08-01/GoogleMapsCompatible_Level9/{z}/{y}/{x}.jpg', {
    attribution: 'NASA GIBS / MODIS Terra',
    tileSize: 256,
    opacity: 0.6,
    maxZoom: 9, // MODIS only goes to level 9
    minZoom: 1,
    noWrap: true
  }).addTo(map);
  </script>
  <div id="leaflet-weather-map" style="height:500px;width:100%;border-radius:var(--border-radius);margin:10px 0;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') return;
    var map = L.map('leaflet-weather-map').setView([39.8283, -98.5795], 4);
    var base = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {}).addTo(map);
    L.tileLayer('https://gibs.earthdata.nasa.gov/wmts/epsg3857/best/VIIRS_SNPP_CorrectedReflectance_TrueColor/default/2023-03-01/250m/{z}/{y}/{x}.jpg', { attribution: 'NASA GIBS', opacity: 0.6 }).addTo(map);

    // OpenWeatherMap layers
    var clouds = L.tileLayer('https://tile.openweathermap.org/map/clouds_new/{z}/{x}/{y}.png?appid=<?php echo esc_attr($api_key); ?>', {
      opacity: 0.9
    });

    var precipitation = L.tileLayer('https://tile.openweathermap.org/map/precipitation_new/{z}/{x}/{y}.png?appid=<?php echo esc_attr($api_key); ?>', {
      attribution: '&copy; OpenWeatherMap', opacity: 0.5
    });

    var wind = L.tileLayer('https://tile.openweathermap.org/map/wind_new/{z}/{x}/{y}.png?appid=<?php echo esc_attr($api_key); ?>', {
      attribution: '&copy; OpenWeatherMap', opacity: 0.5
    });

    var pressure = L.tileLayer('https://tile.openweathermap.org/map/pressure_new/{z}/{x}/{y}.png?appid=<?php echo esc_attr($api_key); ?>', {
      attribution: '&copy; OpenWeatherMap', opacity: 0.5
    });

    var temp = L.tileLayer('https://tile.openweathermap.org/map/temp_new/{z}/{x}/{y}.png?appid=<?php echo esc_attr($api_key); ?>', {
      attribution: '&copy; OpenWeatherMap', opacity: 0.5
    });

    var snow = L.tileLayer('https://tile.openweathermap.org/map/snow/{z}/{x}/{y}.png?appid=<?php echo esc_attr($api_key); ?>', {
      attribution: '&copy; OpenWeatherMap', opacity: 0.5
    });

    // Layer toggles
    var overlays = {
      "Clouds": clouds,
      "Precipitation": precipitation,
      "Wind": wind,
      "Pressure": pressure,
      "Temperature": temp,
      "Snow": snow
    };

    L.control.layers(null, overlays, { collapsed: false }).addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

add_shortcode('leaflet_weather_map', 'baum_leaflet_unified_weather_shortcode');

//
//
//

/**
 * Creates a basic Leaflet weather map with cloud overlay (deprecated)
 *
 * This function generates a simple weather map using Leaflet.js with
 * OpenWeatherMap cloud data. This is a simpler version compared to the
 * unified weather map shortcode. Currently commented out.
 *
 * @param array $atts Shortcode attributes (currently unused)
 * @return string HTML output containing the weather map
 *
 * @since 1.0.0
 * @deprecated Use baum_leaflet_unified_weather_shortcode instead
 */
function leaflet_weather_map_shortcode ($atts) {
  ob_start();
  ?>
  <div id="weather-map" style="height:500px;width:100%;border-radius:var(--border-radius);margin:10px 0;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') {
      console.error('Leaflet not loaded');
      return;
    }
    var map = L.map('weather-map').setView([39.8283, -98.5795], 4);
    // Base Layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);
    // Add OpenWeatherMap Clouds layer
    L.OWM.clouds({ opacity: 0.5, appId: '140135e33fa723259cf21dbe1c630fff' }).addTo(map);

    // // Radar Layer
    // var radarLayer = L.tileLayer.wms('https://opengeo.ncep.noaa.gov/geoserver/ows?', {
    //     layers: 'nexrad_base_reflectivity',
    //     format: 'image/png',
    //     transparent: true,
    //     opacity: 0.5,
    //     attribution: 'NOAA NWS'
    // });

    // // Smoke Layer
    // var smokeLayer = L.esri.dynamicMapLayer({
    //     url: 'https://services.arcgisonline.com/arcgis/rest/services/USA_Wildfires/MapServer',
    //     opacity: 0.5,
    //     attribution: 'Esri'
    // });

    // // Temperature Layer
    // var tempLayer = L.tileLayer.wms('https://digital.weather.gov/ndfd/ndfd_wms.cgi?', {
    //     layers: 'maxt',
    //     format: 'image/png',
    //     transparent: true,
    //     opacity: 0.5,
    //     attribution: 'NOAA NWS'
    // });

    // // Cloud Cover Layer
    // var cloudLayer = L.tileLayer.wms('https://digital.weather.gov/ndfd/ndfd_wms.cgi?', {
    //     layers: 'sky',
    //     format: 'image/png',
    //     transparent: true,
    //     opacity: 0.5,
    //     attribution: 'NOAA NWS'
    // });

    // // Layer Control
    // var overlayMaps = {
    //     "Radar": radarLayer,
    //     "Smoke": smokeLayer,
    //     "Temperature": tempLayer,
    //     "Cloud Cover": cloudLayer
    // };

    L.control.layers(null, overlayMaps, { collapsed: false }).addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_weather_map', 'leaflet_weather_map_shortcode');

//
//
//

/**
 * Creates a Leaflet map with NOAA radar overlay
 *
 * This function generates an interactive map displaying NOAA weather radar data
 * using the CONUS base reflectivity layer. Shows precipitation and storm activity
 * across the continental United States.
 *
 * @param array $atts Shortcode attributes (currently unused)
 * @return string HTML output containing the radar map
 *
 * @since 1.0.0
 *
 * @example [leaflet_radar_animation]
 */
function leaflet_radar_animation_shortcode($atts) {
  ob_start();
  ?>
  <div id="radar-animation-map" style="height:500px;width:100%;border-radius:var(--border-radius);margin:10px 0;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') {
      console.error('Leaflet not loaded');
      return;
    }
    var map = L.map('radar-animation-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);
    var radarLayer = L.tileLayer.wms('https://opengeo.ncep.noaa.gov/geoserver/conus/conus_bref_qcd/ows?', {
      layers: 'conus_bref_qcd',
      format: 'image/png',
      transparent: true,
      opacity: 0.5,
      attribution: 'NOAA NWS',
      version: '1.3.0'
    });
    radarLayer.addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_radar_animation', 'leaflet_radar_animation_shortcode');

//
//
//

/**
 * Creates a Leaflet map with smoke overlay from NOAA
 *
 * This function generates an interactive map displaying smoke data from NOAA NCDC
 * using Esri Leaflet. Useful for tracking wildfire smoke and air quality conditions.
 * Requires Esri Leaflet plugin to be loaded.
 *
 * @param array $atts Shortcode attributes (currently unused)
 * @return string HTML output containing the smoke overlay map
 *
 * @since 1.0.0
 *
 * @example [leaflet_smoke_overlay]
 */
function leaflet_smoke_overlay_shortcode($atts) {
  ob_start();
  ?>
  <div id="smoke-overlay-map" style="height: 500px;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined' || typeof L.esri === 'undefined') {
      console.error('Leaflet or Esri Leaflet not loaded.');
      return;
    }
    var map = L.map('smoke-overlay-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);
    var smokeLayer = L.esri.dynamicMapLayer({
      url: 'https://gis.ncdc.noaa.gov/arcgis/rest/services/cdo/smoke/MapServer',
      opacity: 0.5,
      attribution: 'NOAA NCDC'
    });
    smokeLayer.addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_smoke_overlay', 'leaflet_smoke_overlay_shortcode');

/**
 * Creates a Leaflet map with temperature overlay from NOAA
 *
 * This function generates an interactive map displaying maximum temperature data
 * from NOAA's National Digital Forecast Database (NDFD). Shows temperature
 * forecasts across the United States.
 *
 * @param array $atts Shortcode attributes (currently unused)
 * @return string HTML output containing the temperature map
 *
 * @since 1.0.0
 *
 * @example [leaflet_temperature_map]
 */
function leaflet_temperature_map_shortcode($atts) {
  ob_start();
  ?>
  <div id="temperature-map" style="height: 500px;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') {
      console.error('Leaflet not loaded');
      return;
    }
    var map = L.map('temperature-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);
    var tempLayer = L.tileLayer.wms('https://digital.weather.gov/ndfd/ndfd_wms.cgi?', {
      layers: 'maxt',
      format: 'image/png',
      transparent: true,
      opacity: 0.5,
      attribution: 'NOAA NWS'
    });
    tempLayer.addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_temperature_map', 'leaflet_temperature_map_shortcode');

//
//
//

function leaflet_cloud_cover_map_shortcode($atts) {
  ob_start();
  ?>
  <div id="cloud-cover-map" style="height: 500px;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') {
      console.error('Leaflet not loaded');
      return;
    }
    var map = L.map('cloud-cover-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);
    var cloudLayer = L.tileLayer.wms('https://www.ncei.noaa.gov/thredds/wms/nwm/nwm_cloud_cover.nc', {
      layers: 'Total_cloud_cover_entire_atmosphere_single_layer',
      format: 'image/png',
      transparent: true,
      opacity: 0.5,
      attribution: 'NOAA NCEI'
    });
    cloudLayer.addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_cloud_cover_map', 'leaflet_cloud_cover_map_shortcode');

//
//
//

// function enqueue_esri_leaflet() {
//   wp_enqueue_script('esri-leaflet', 'https://unpkg.com/esri-leaflet/dist/esri-leaflet.js', array('leaflet'), null, true);
// }

// add_action('wp_enqueue_scripts', 'enqueue_esri_leaflet');

//
//
//

// function baum_enqueue_leaflet_assets() {
//   // Leaflet core
//   wp_enqueue_style('leaflet-css', 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css');
//   wp_enqueue_script('leaflet-js', 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js', array(), null, true);
//   // Esri Leaflet plugin
//   wp_enqueue_script('esri-leaflet', 'https://unpkg.com/esri-leaflet@3.0.10/dist/esri-leaflet.js', array('leaflet-js'), null, true);
// }

// add_action('wp_enqueue_scripts', 'baum_enqueue_leaflet_assets');

//
//
//

function leaflet_weather_map($atts) {
  ob_start();
  ?>
  <div id="weather-map" style="height:500px;width:100%;border-radius:var(--border-radius);margin:10px 0;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') {
      console.error('Leaflet not loaded');
      sleep(2500);
    }
    var map = L.map('weather-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);

    // var radarLayer = L.tileLayer.wms('https://opengeo.ncep.noaa.gov/geoserver/conus/conus_bref_qcd/ows?', {
    //     layers: 'conus_bref_qcd',
    //     format: 'image/png',
    //     transparent: true,
    //     attribution: 'NOAA NWS'
    // });
    // radarLayer.addTo(map);

    var radarLayer = L.tileLayer.wms('https://opengeo.ncep.noaa.gov/geoserver/conus/conus_bref_qcd/ows?', {
      layers: 'conus_bref_qcd',
      format: 'image/png8', // crucial for transparency!
      transparent: true,    // forces alpha channel
      opacity: 0.6,
      attribution: 'NOAA NWS',
      version: '1.3.0',
      uppercase: true       // optional; NOAA WMS sometimes needs this
    });

    radarLayer.addTo(map);

    // https://services.arcgisonline.com/arcgis/rest/services/World_Weather/MapServer
    // ☀️ Layer Reference (World_Weather)
    // Layer Index	Name
    // 0	Precipitation
    // 1	Wind Speed
    // 2	Surface Pressure
    // 3	Relative Humidity
    // 4	Temperature
    // 5	Cloud Cover

    // L.esri.dynamicMapLayer({
    //     url: 'https://services.arcgisonline.com/arcgis/rest/services/World_Weather/MapServer',
    //     opacity: 0.7,
    //     layers: [4] // Clouds
    // }).addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_weather', 'leaflet_weather_map');

//
//
//

function leaflet_air_quality_map($atts) {
  ob_start();
  ?>
  <div id="air-quality-map" style="height: 500px;"></div>
  <script>
    var map = L.map('air-quality-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);

    L.esri.featureLayer({
      url: 'https://services.arcgis.com/your_service_url/arcgis/rest/services/Air_Quality/FeatureServer/0'
    }).addTo(map);
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_air_quality', 'leaflet_air_quality_map');

//
//
//

function baum_leaflet_wildfire_map_shortcode($atts) {
  ob_start();
  ?>
  <div id="wildfire-map" style="height: 500px;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') {
      console.error('Leaflet not loaded');
      sleep(2500);
    }
    var map = L.map('wildfire-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);
    L.esri.featureLayer({
      url: 'https://services3.arcgis.com/T4QMspbfLg3qTGWY/arcgis/rest/services/USA_WildfireActivity/FeatureServer/0'
    }).addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_wildfire', 'baum_leaflet_wildfire_map_shortcode');

//
//
//

/**
 * Custom filter for WordPress heartbeat send data
 *
 * This function allows monitoring or modifying the data sent during WordPress
 * heartbeat requests. Currently used for optional monitoring purposes.
 *
 * @param array $response The heartbeat response data
 * @param array $data The heartbeat request data
 * @return array The potentially modified response data
 *
 * @since 1.0.0
 */
function custom_heartbeat_send_filter( $response, $data ) {
  // Optional: Modify or monitor heartbeat data
  return $response;
}

add_filter( 'heartbeat_send', 'custom_heartbeat_send_filter', 10, 2 );

/**
 * Customizes WordPress heartbeat settings
 *
 * This function modifies the WordPress heartbeat interval from the default
 * 15 seconds to 60 seconds to reduce server load and improve performance.
 *
 * @param array $settings The current heartbeat settings
 * @return array Modified heartbeat settings with custom interval
 *
 * @since 1.0.0
 */
function custom_heartbeat_settings( $settings ) {
  // Change interval in seconds (default is 15)
  $settings['interval'] = 60; // One ping per minute
  return $settings;
}

add_filter( 'heartbeat_settings', 'custom_heartbeat_settings' );

/**
 * Renders star ratings as HTML with Font Awesome icons
 *
 * This function creates a visual star rating display using Font Awesome icons.
 * It rounds the score to the nearest integer and displays filled stars for
 * the rating and empty stars for the remainder.
 *
 * @param float $score The rating score (0-5)
 * @return string HTML output containing the star rating display
 *
 * @since 1.0.0
 */
function baumpress_render_stars ($score) {
  $stars = round(floatval($score)); // Ensure it's rounded to an integer
  $stars = max(0, min(5, $stars)); // Clamp between 0 and 5
  $output = '<span class="star-rating">';

  for ($i = 1; $i <= 5; $i++) {
    if ($i <= $stars) {
      $output .= '<span class="star full"><i class="fa-fw fa-solid fa-star gold"></i></span>';
    } else {
      $output .= '<span class="star empty"><i class="fa-fw fa-regular fa-star senary"></i></span>';
    }
  }
  $output .= '</span>';
  return $output;
}

/**
 * Displays star ratings with support for half stars
 *
 * This function creates a more detailed star rating display that supports
 * half stars for more precise rating representation. Uses Font Awesome icons
 * with different classes for full, half, and empty stars.
 *
 * @param float $rating The rating value (0-5, supports decimals)
 * @return string HTML output containing the star rating with half-star support
 *
 * @since 1.0.0
 */
function display_stars($rating) {
  $rating = floatval($rating);
  $rating = max(0, min(5, $rating)); // Clamp between 0 and 5

  $output = '';
  $full_stars = floor($rating);
  $half_star = ($rating - $full_stars) >= 0.5 ? true : false;
  $empty_stars = 5 - $full_stars - ($half_star ? 1 : 0);

  // Add full stars
  for ($i = 0; $i < $full_stars; $i++) {
    $output .= "<i class='fas fa-star gold'></i>";
  }

  // Add half star if needed
  if ($half_star) {
    $output .= "<i class='fas fa-star-half-alt gold'></i>";
  }

  // Add empty stars
  for ($i = 0; $i < $empty_stars; $i++) {
    $output .= "<i class='far fa-star senary'></i>";
  }

  return $output;
}


/**
 * Time Capsule Shortcode
 *
 * Creates a shortcode for easily adding Time Capsule components to posts and pages.
 * Usage: [time_capsule title="TITLE" subtitle="Subtitle" date="Date" content="Content" button_text="Button Text"]
 *
 * @param array $atts Shortcode attributes
 * @return string HTML output
 *
 * @since 1.0.0
 */
function baum_time_capsule_shortcode($atts) {
  // Set default attributes
  $atts = shortcode_atts([
    "title" => "TIME CAPSULE",
    "subtitle" => "From Britannica Book Of The Year",
    "date" => date("F j, Y"),
    "content" => "",
    "button_text" => "SHOW ANOTHER EVENT",
    "source" => "",
    "show_icon" => "true",
    "css_class" => "",
    "animate" => "false"
  ], $atts, "time_capsule");

  // Convert string booleans to actual booleans
  $atts["show_icon"] = ($atts["show_icon"] === "true");

  // Add animation class if requested
  if ($atts["animate"] === "true") {
    $atts["css_class"] .= " animate-in";
  }

  // Start output buffering
  ob_start();

  // Load the template part
  get_template_part("parts/baum-time-capsule", null, [
    "title" => $atts["title"],
    "subtitle" => $atts["subtitle"],
    "date" => $atts["date"],
    "content" => $atts["content"],
    "button_text" => $atts["button_text"],
    "source" => $atts["source"],
    "show_icon" => $atts["show_icon"],
    "css_class" => trim($atts["css_class"])
  ]);

  // Return the buffered content
  return ob_get_clean();
}

/**
 * Enhanced Time Capsule Helper Functions
 *
 * These functions extend the baum-cosmology plugin functionality
 * for the enhanced time capsule page
 */

/**
 * Load historical data for a specific date with caching
 *
 * @param string $month_day Date in MM-DD format (e.g., "07-20")
 * @return array|null Historical data or null on error
 */
function baum_load_historical_data($month_day) {
  // Check cache first
  $cache_key = 'baum_history_' . $month_day;
  $cached_data = wp_cache_get($cache_key, 'baum_time_capsule');

  if ($cached_data !== false) {
    return $cached_data;
  }

  // Try multiple possible locations for the data file
  $possible_paths = [
    // Baum cosmology plugin directory
    WP_PLUGIN_DIR . '/baum-cosmology/data/history-of-today/' . $month_day . '.json',
    // Current directory (if plugin is in theme)
    get_template_directory() . '/data/history-of-today/' . $month_day . '.json',
    // Alternative plugin path
    plugin_dir_path(__FILE__) . 'data/history-of-today/' . $month_day . '.json'
  ];

  $history_file = null;
  foreach ($possible_paths as $path) {
    if (file_exists($path)) {
      $history_file = $path;
      break;
    }
  }

  if (!$history_file) {
    // Cache the null result to avoid repeated file checks
    wp_cache_set($cache_key, null, 'baum_time_capsule', 3600);
    return null;
  }

  $json_content = file_get_contents($history_file);
  $data = json_decode($json_content, true);

  // Cache the data for 1 hour
  wp_cache_set($cache_key, $data, 'baum_time_capsule', 3600);

  return $data;
}

/**
 * Get comprehensive historical information for a date with performance optimization
 *
 * @param string $date Date in Y-m-d format
 * @return array Comprehensive historical data
 */
function baum_get_comprehensive_history($date) {
  // Check comprehensive cache first
  $cache_key = 'baum_comprehensive_' . $date;
  $cached_result = get_transient($cache_key);

  if ($cached_result !== false) {
    return $cached_result;
  }

  $start_time = microtime(true);
  global $baum_cosmology;

  $month_day = date('m-d', strtotime($date));
  $weekday = date('l', strtotime($date));

  $result = [
    'date' => $date,
    'month_day' => $month_day,
    'weekday' => $weekday,
    'events' => [],
    'births' => [],
    'deaths' => [],
    'weekday_events' => [],
    'astronomical' => [],
    'holidays' => [],
    'performance' => []
  ];

  // Load date-specific data
  $data_start = microtime(true);
  $history_data = baum_load_historical_data($month_day);
  if ($history_data && isset($history_data['data'])) {
    $result['events'] = array_slice($history_data['data']['Events'] ?? [], 0, 20); // Limit to 20 items
    $result['births'] = array_slice($history_data['data']['Births'] ?? [], 0, 15); // Limit to 15 items
    $result['deaths'] = array_slice($history_data['data']['Deaths'] ?? [], 0, 15); // Limit to 15 items
  }
  $result['performance']['data_load_time'] = microtime(true) - $data_start;

  // Load weekday-specific data with caching
  $weekday_cache_key = 'baum_weekday_' . $weekday;
  $weekday_events = wp_cache_get($weekday_cache_key, 'baum_time_capsule');

  if ($weekday_events === false) {
    $weekday_file = get_template_directory() . '/data/weekday-history.json';
    if (file_exists($weekday_file)) {
      $weekday_data = json_decode(file_get_contents($weekday_file), true);
      $weekday_events = $weekday_data[$weekday] ?? [];
      wp_cache_set($weekday_cache_key, $weekday_events, 'baum_time_capsule', 3600);
    } else {
      $weekday_events = [];
    }
  }
  $result['weekday_events'] = array_slice($weekday_events, 0, 10);

  // Add comprehensive astronomical information if baum-cosmology is available
  $astro_start = microtime(true);
  if ($baum_cosmology && class_exists('Baum_Cosmology')) {
    try {
      // Use the specific date being queried, not today's date
      $query_date = $date; // This should be the date from the URL/query

      $result['astronomical'] = [
        'moon_phase' => $baum_cosmology->get_moon_phase($query_date),
        'sun_times' => $baum_cosmology->get_sun_times($query_date, BAUM_TIME_CAPSULE_DEFAULT_LAT, BAUM_TIME_CAPSULE_DEFAULT_LON),
        'season' => $baum_cosmology->get_current_season('northern', $query_date),
        'planets' => $baum_cosmology->get_visible_planets($query_date),
        'planetary_positions' => $baum_cosmology->get_planetary_positions($query_date),
        'daily_transits' => array_slice($baum_cosmology->get_daily_transits($query_date), 0, 5),

        'meteor_showers' => baum_get_meteor_showers_for_date($baum_cosmology, $query_date),
        'eclipses' => $baum_cosmology->get_eclipses($query_date, 2)
      ];
    } catch (Exception $e) {
      error_log('Baum Cosmology error: ' . $e->getMessage());
      $result['astronomical'] = [];
    }
  }
  $result['performance']['astro_time'] = microtime(true) - $astro_start;

  // Load holidays with caching
  $holidays_cache_key = 'baum_holidays_' . $date;
  $holidays = wp_cache_get($holidays_cache_key, 'baum_time_capsule');

  if ($holidays === false) {
    $holidays_file = get_template_directory() . '/data/holidays-public.json';
    if (file_exists($holidays_file)) {
      $all_holidays = json_decode(file_get_contents($holidays_file), true);
      $holidays = array_filter($all_holidays, function($holiday) use ($date) {
        return $holiday['date'] === $date;
      });
      wp_cache_set($holidays_cache_key, $holidays, 'baum_time_capsule', 86400); // Cache for 24 hours
    } else {
      $holidays = [];
    }
  }
  $result['holidays'] = $holidays;

  // Record total performance
  $result['performance']['total_time'] = microtime(true) - $start_time;
  $result['performance']['memory_usage'] = memory_get_usage(true);

  // Cache the complete result for 2 hours
  set_transient($cache_key, $result, 2 * HOUR_IN_SECONDS);

  // Log performance if it's slow
  if ($result['performance']['total_time'] > 1.0) {
    error_log("Slow time capsule query for {$date}: " . $result['performance']['total_time'] . "s");
  }

  return $result;
}

/**
 * External API Integration Functions
 *
 * These functions integrate with external historical data APIs
 * to supplement local data with additional information
 */

/**
 * Fetch data from external historical APIs including API Ninjas
 *
 * @param string $date Date in Y-m-d format
 * @return array External historical data
 */
function baum_fetch_external_history($date) {
  $external_data = [
    'events' => [],
    'births' => [],
    'deaths' => [],
    'movies' => [],
    'sports' => [],
    'music' => [],
    'technology' => [],
    'celebrities' => []
  ];

  // Cache key for this date
  $cache_key = 'baum_external_history_' . $date;
  $cached_data = get_transient($cache_key);

  if ($cached_data !== false) {
    return $cached_data;
  }

  $month = date('n', strtotime($date));
  $day = date('j', strtotime($date));

  // Try to fetch from multiple sources
  $sources = [
    'wikipedia' => baum_fetch_wikipedia_history($month, $day),
    'api_ninjas_celebs' => baum_fetch_api_ninjas_celebrities($month, $day),
    'api_ninjas_events' => baum_fetch_api_ninjas_historical_events($date),
    'api_ninjas_quotes' => baum_fetch_api_ninjas_quotes($date),
    'api_ninjas_facts' => baum_fetch_api_ninjas_facts($date),
    'domains' => baum_fetch_famous_domains_by_date($date),
    'numbers_api' => baum_fetch_numbers_api_data($date),
    'tmdb' => baum_fetch_movie_releases($date),
    'sports' => baum_fetch_sports_events($date)
  ];

  // Merge data from all sources
  foreach ($sources as $source => $data) {
    if (is_array($data)) {
      foreach ($data as $category => $items) {
        if (isset($external_data[$category]) && is_array($items)) {
          $external_data[$category] = array_merge($external_data[$category], $items);
        }
      }
    }
  }

  // Cache for 24 hours
  set_transient($cache_key, $external_data, 24 * HOUR_IN_SECONDS);

  return $external_data;
}

/**
 * Fetch celebrity birthdays from API Ninjas
 *
 * @param int $month Month (1-12)
 * @param int $day Day (1-31)
 * @return array Celebrity birthday data
 */
function baum_fetch_api_ninjas_celebrities($month, $day) {
  // Try API Ninjas first
  $data = baum_api_ninjas_request('celebrity', ['birthday' => "{$month}/{$day}"]);

  $celebrities = [];

  // Process API Ninjas data if available
  if ($data && is_array($data)) {
    foreach (array_slice($data, 0, 5) as $celebrity) {
      // Skip celebrities with no name or "Unknown" name
      if (empty($celebrity['name']) || $celebrity['name'] === 'Unknown') {
        continue;
      }

      $celebrities[] = [
        'name' => $celebrity['name'],
        'birthday' => $celebrity['birthday'] ?? '',
        'nationality' => $celebrity['nationality'] ?? '',
        'occupation' => $celebrity['occupation'] ?? [],
        'is_alive' => $celebrity['is_alive'] ?? true,
        'age' => $celebrity['age'] ?? null,
        'height' => $celebrity['height'] ?? null,
        'net_worth' => $celebrity['net_worth'] ?? null,
        'source' => 'API Ninjas'
      ];
    }
  }

  // Add fallback celebrities from our database
  $fallback_celebrities = baum_get_fallback_celebrities($month, $day);
  $celebrities = array_merge($celebrities, $fallback_celebrities);

  return [
    'celebrities' => $celebrities,
    'births' => array_map(function($celeb) {
      return [
        'year' => $celeb['birthday'] ? date('Y', strtotime($celeb['birthday'])) : ($celeb['year'] ?? ''),
        'text' => $celeb['name'] . ' - ' . implode(', ', (array)$celeb['occupation']),
        'html' => '<strong>' . esc_html($celeb['name']) . '</strong> - ' .
                 esc_html(implode(', ', (array)$celeb['occupation'])) .
                 ($celeb['nationality'] ? ' (' . esc_html($celeb['nationality']) . ')' : ''),
        'source' => $celeb['source'] ?? 'Celebrity Database',
        'celebrity_data' => $celeb
      ];
    }, $celebrities)
  ];
}

/**
 * Get fallback celebrities for any date
 *
 * @param int $month Month (1-12)
 * @param int $day Day (1-31)
 * @return array Fallback celebrity data
 */
function baum_get_fallback_celebrities($month, $day) {
  $celebrity_config = baum_get_celebrity_birthdays_config();
  $month_day = sprintf('%02d-%02d', $month, $day);

  // Get celebrities for this specific date
  $date_celebrities = $celebrity_config[$month_day] ?? [];

  // If no celebrities for this date, use a rotating selection
  if (empty($date_celebrities)) {
    $all_celebrities = [];
    foreach ($celebrity_config as $celebrities) {
      $all_celebrities = array_merge($all_celebrities, $celebrities);
    }

    // Use date to deterministically select celebrities
    $seed = ($month * 31) + $day;
    $celebrity_count = count($all_celebrities);

    if ($celebrity_count > 0) {
      $selected_celebrity = $all_celebrities[$seed % $celebrity_count];
      $date_celebrities = [$selected_celebrity];
    }
  }

  // Format celebrities for consistency
  return array_map(function($celeb) {
    return [
      'name' => $celeb['name'],
      'year' => $celeb['year'],
      'nationality' => $celeb['nationality'],
      'occupation' => $celeb['occupation'],
      'description' => $celeb['description'] ?? '',
      'source' => 'Celebrity Database',
      'is_alive' => $celeb['year'] > 1900 // Simple heuristic
    ];
  }, $date_celebrities);
}

/**
 * Fetch historical events from API Ninjas
 *
 * @param string $date Date in Y-m-d format
 * @return array Historical events data
 */
function baum_fetch_api_ninjas_historical_events($date) {
  $api_key = 'POSYz0dZXmSaJFy0trshow==liKp5hLYjdJgsED8';

  // Try different search terms based on the date
  $year = date('Y', strtotime($date));
  $month = date('F', strtotime($date));
  $day = date('j', strtotime($date));

  $search_terms = [
    $month . ' ' . $day,
    $year,
    'July 20' // Specific famous date
  ];

  $all_events = [];

  foreach ($search_terms as $term) {
    $api_url = "https://api.api-ninjas.com/v1/historicalevents?text=" . urlencode($term);

    $response = wp_remote_get($api_url, [
      'timeout' => 10,
      'headers' => [
        'X-Api-Key' => $api_key,
        'User-Agent' => 'BaumPress Time Capsule/1.0'
      ]
    ]);

    if (!is_wp_error($response)) {
      $body = wp_remote_retrieve_body($response);
      $data = json_decode($body, true);

      if ($data && is_array($data)) {
        $all_events = array_merge($all_events, array_slice($data, 0, 3));
      }
    }

    // Don't overwhelm the API
    if (count($all_events) >= 5) break;
  }

  return [
    'historical_events' => array_map(function($event) {
      return [
        'year' => $event['year'] ?? '',
        'text' => $event['event'] ?? '',
        'html' => '<strong>' . esc_html($event['year'] ?? '') . '</strong> - ' . esc_html($event['event'] ?? ''),
        'source' => 'API Ninjas Historical Events'
      ];
    }, array_slice($all_events, 0, 5))
  ];
}

/**
 * Fetch historical data from Wikipedia API
 *
 * @param int $month Month (1-12)
 * @param int $day Day (1-31)
 * @return array Wikipedia historical data
 */
function baum_fetch_wikipedia_history($month, $day) {
  $api_url = "https://en.wikipedia.org/api/rest_v1/feed/onthisday/all/{$month}/{$day}";

  $response = wp_remote_get($api_url, [
    'timeout' => 10,
    'headers' => [
      'User-Agent' => 'BaumPress Time Capsule/1.0 (https://example.com)'
    ]
  ]);

  if (is_wp_error($response)) {
    return [];
  }

  $body = wp_remote_retrieve_body($response);
  $data = json_decode($body, true);

  if (!$data) {
    return [];
  }

  $result = [
    'events' => [],
    'births' => [],
    'deaths' => []
  ];

  // Process events
  if (isset($data['events'])) {
    foreach (array_slice($data['events'], 0, 10) as $event) {
      $result['events'][] = [
        'year' => $event['year'] ?? '',
        'text' => wp_strip_all_tags($event['text'] ?? ''),
        'source' => 'Wikipedia'
      ];
    }
  }

  // Process births
  if (isset($data['births'])) {
    foreach (array_slice($data['births'], 0, 10) as $birth) {
      $result['births'][] = [
        'year' => $birth['year'] ?? '',
        'text' => wp_strip_all_tags($birth['text'] ?? ''),
        'source' => 'Wikipedia'
      ];
    }
  }

  // Process deaths
  if (isset($data['deaths'])) {
    foreach (array_slice($data['deaths'], 0, 10) as $death) {
      $result['deaths'][] = [
        'year' => $death['year'] ?? '',
        'text' => wp_strip_all_tags($death['text'] ?? ''),
        'source' => 'Wikipedia'
      ];
    }
  }

  return $result;
}

/**
 * Fetch movie releases with TMDB API integration
 *
 * @param string $date Date in Y-m-d format
 * @return array Movie release data with cover images
 */
function baum_fetch_movie_releases($date) {
  $month = date('n', strtotime($date));
  $day = date('j', strtotime($date));
  $year = date('Y', strtotime($date));

  // Cache key for this date
  $cache_key = 'baum_movies_' . $date;
  $cached_data = get_transient($cache_key);

  if ($cached_data !== false) {
    return $cached_data;
  }

  // Get movie data from configuration
  $movie_config = baum_get_movie_releases_config();
  $month_day = sprintf('%02d-%02d', $month, $day);

  $movies = $movie_config[$month_day] ?? [];

  // Add fallback movies for dates without specific data
  if (empty($movies)) {
    $movies = baum_get_fallback_movies($month, $day);
  }



  $result = ['movies' => $movies];

  // Cache for 24 hours
  set_transient($cache_key, $result, 24 * HOUR_IN_SECONDS);

  return $result;
}

/**
 * Fetch sports events (placeholder)
 *
 * @param string $date Date in Y-m-d format
 * @return array Sports event data
 */
function baum_fetch_sports_events($date) {
  $month = date('n', strtotime($date));
  $day = date('j', strtotime($date));

  $sample_sports = [];

  // July 20th sample data
  if ($month == 7 && $day == 20) {
    $sample_sports = [
      [
        'event' => 'Apollo 11 Moon Landing',
        'year' => 1969,
        'teams' => 'NASA Mission',
        'score' => 'Successful Landing',
        'source' => 'Historical Records'
      ]
    ];
  }

  // December 18th sample data (World Cup Final 2022)
  if ($month == 12 && $day == 18) {
    $sample_sports = [
      [
        'event' => 'FIFA World Cup Final',
        'year' => 2022,
        'teams' => 'Argentina vs France',
        'score' => '4-2 (3-3 AET, 4-2 Penalties)',
        'source' => 'FIFA'
      ]
    ];
  }

  return [
    'sports' => $sample_sports,
    'technology' => baum_get_technology_milestones($date)
  ];
}

/**
 * Get technology milestones for a specific date
 *
 * @param string $date Date in Y-m-d format
 * @return array Technology milestone data
 */
function baum_get_technology_milestones($date) {
  $month = date('n', strtotime($date));
  $day = date('j', strtotime($date));

  $milestones = [];

  // July 20th - Moon Landing
  if ($month == 7 && $day == 20) {
    $milestones = [
      [
        'title' => 'Apollo 11 Moon Landing',
        'year' => 1969,
        'description' => 'First humans land on the Moon using advanced spacecraft technology.',
        'company' => 'NASA',
        'source' => 'Historical Records'
      ]
    ];
  }

  // January 9th - iPhone announcement
  if ($month == 1 && $day == 9) {
    $milestones = [
      [
        'title' => 'iPhone Announcement',
        'year' => 2007,
        'description' => 'Steve Jobs unveils the first iPhone, revolutionizing mobile technology.',
        'company' => 'Apple Inc.',
        'source' => 'Tech History'
      ]
    ];
  }

  // April 4th - Windows 95
  if ($month == 4 && $day == 4) {
    $milestones = [
      [
        'title' => 'Windows 95 Development Milestone',
        'year' => 1995,
        'description' => 'Major milestone in Windows 95 development, introducing the Start button.',
        'company' => 'Microsoft',
        'source' => 'Tech History'
      ]
    ];
  }

  return $milestones;
}

/**
 * Fetch quotes from API Ninjas
 *
 * @param string $date Date in Y-m-d format
 * @return array Quotes data
 */
function baum_fetch_api_ninjas_quotes($date) {
  // Get quotes from famous historical figures
  $categories = ['history', 'inspirational', 'wisdom'];
  $category = $categories[array_rand($categories)];

  $data = baum_api_ninjas_request('quotes', [
    'category' => $category,
    'limit' => 3
  ]);

  if (!$data || !is_array($data)) {
    return ['quotes' => []];
  }

  return [
    'quotes' => array_map(function($quote) {
      return [
        'text' => $quote['quote'] ?? '',
        'author' => $quote['author'] ?? 'Unknown',
        'category' => $quote['category'] ?? '',
        'html' => '"' . esc_html($quote['quote'] ?? '') . '" - <strong>' . esc_html($quote['author'] ?? 'Unknown') . '</strong>',
        'source' => 'API Ninjas Quotes'
      ];
    }, $data)
  ];
}

/**
 * Fetch interesting facts from API Ninjas
 *
 * @param string $date Date in Y-m-d format
 * @return array Facts data
 */
function baum_fetch_api_ninjas_facts($date) {
  $data = baum_api_ninjas_request('facts', ['limit' => 3]);

  if (!$data || !is_array($data)) {
    return ['facts' => []];
  }

  return [
    'facts' => array_map(function($fact) {
      return [
        'text' => $fact['fact'] ?? '',
        'html' => esc_html($fact['fact'] ?? ''),
        'source' => 'API Ninjas Facts'
      ];
    }, $data)
  ];
}

/**
 * Get famous domain registrations data configuration
 *
 * @return array Domain registrations by date
 */
function baum_get_famous_domains_config() {
  return [
    '03-15' => [ // March 15, 1985 - symbolics.com (first domain ever)
      ['domain' => 'symbolics.com', 'year' => 1985, 'description' => 'First domain name ever registered'],
    ],
    '04-24' => [ // April 24, 1986
      ['domain' => 'cmu.edu', 'year' => 1986, 'description' => 'Carnegie Mellon University'],
    ],
    '05-08' => [ // May 8, 1986
      ['domain' => 'purdue.edu', 'year' => 1986, 'description' => 'Purdue University'],
    ],
    '07-11' => [ // July 11, 1986
      ['domain' => 'rice.edu', 'year' => 1986, 'description' => 'Rice University'],
    ],
    '09-15' => [ // September 15, 1997
      ['domain' => 'google.com', 'year' => 1997, 'description' => 'Google - Search engine that changed the world'],
    ],
    '01-18' => [ // January 18, 1993
      ['domain' => 'whitehouse.gov', 'year' => 1993, 'description' => 'Official White House website'],
    ],
    '02-14' => [ // February 14, 2005
      ['domain' => 'youtube.com', 'year' => 2005, 'description' => 'YouTube - Video sharing platform'],
    ],
    '02-04' => [ // February 4, 2004
      ['domain' => 'facebook.com', 'year' => 2004, 'description' => 'Facebook - Social networking platform'],
    ],
    '07-20' => [ // July 20th - add some tech domains for testing
      ['domain' => 'apple.com', 'year' => 1987, 'description' => 'Apple Computer Company'],
      ['domain' => 'microsoft.com', 'year' => 1991, 'description' => 'Microsoft Corporation'],
    ]
  ];
}

/**
 * Get movie releases configuration data
 *
 * @return array Movie releases by date
 */
function baum_get_movie_releases_config() {
  return [
    // January
    '01-01' => [
      ['title' => 'The Godfather Part II', 'year' => 1974, 'genre' => 'Crime, Drama', 'rating' => 9.0, 'director' => 'Francis Ford Coppola', 'poster_url' => '', 'overview' => 'The early life and career of Vito Corleone in 1920s New York City.', 'source' => 'Classic Films']
    ],
    '01-15' => [
      ['title' => 'Black Hawk Down', 'year' => 2001, 'genre' => 'Action, Drama, War', 'rating' => 7.7, 'director' => 'Ridley Scott', 'poster_url' => '', 'overview' => 'The story of 160 elite U.S. soldiers who dropped into Mogadishu in October 1993.', 'source' => 'War Films']
    ],

    // February
    '02-14' => [
      ['title' => 'Casablanca', 'year' => 1942, 'genre' => 'Drama, Romance, War', 'rating' => 8.5, 'director' => 'Michael Curtiz', 'poster_url' => '', 'overview' => 'A cynical American expatriate meets a former lover in wartime Casablanca.', 'source' => 'Classic Romance']
    ],
    '02-28' => [
      ['title' => 'The Silence of the Lambs', 'year' => 1991, 'genre' => 'Crime, Drama, Thriller', 'rating' => 8.6, 'director' => 'Jonathan Demme', 'poster_url' => '', 'overview' => 'A young FBI cadet must receive the help of an incarcerated cannibal killer.', 'source' => 'Thriller']
    ],

    // March
    '03-15' => [
      ['title' => 'The Ides of March', 'year' => 2011, 'genre' => 'Drama, Thriller', 'rating' => 7.1, 'director' => 'George Clooney', 'poster_url' => '', 'overview' => 'An idealistic staffer for a new presidential candidate gets a crash course on dirty politics.', 'source' => 'Political Drama']
    ],
    '03-31' => [
      ['title' => 'The Matrix', 'year' => 1999, 'genre' => 'Action, Sci-Fi', 'rating' => 8.7, 'director' => 'The Wachowskis', 'poster_url' => '', 'overview' => 'A computer programmer discovers reality is a simulation.', 'source' => 'Sci-Fi Classic']
    ],

    // April
    '04-01' => [
      ['title' => 'The Truman Show', 'year' => 1998, 'genre' => 'Comedy, Drama', 'rating' => 8.2, 'director' => 'Peter Weir', 'poster_url' => '', 'overview' => 'An insurance salesman discovers his entire life is a TV show.', 'source' => 'Comedy Drama']
    ],
    '04-15' => [
      ['title' => 'Titanic', 'year' => 1997, 'genre' => 'Drama, Romance', 'rating' => 7.9, 'director' => 'James Cameron', 'poster_url' => '', 'overview' => 'A seventeen-year-old aristocrat falls in love with a kind but poor artist aboard the luxurious, ill-fated R.M.S. Titanic.', 'source' => 'Epic Romance']
    ],

    // May
    '05-04' => [
      ['title' => 'Star Wars: Episode IV - A New Hope', 'year' => 1977, 'genre' => 'Adventure, Fantasy, Sci-Fi', 'rating' => 8.6, 'director' => 'George Lucas', 'poster_url' => 'https://image.tmdb.org/t/p/w300/6FfCtAuVAW8XJjZ7eWeLibRLWTw.jpg', 'overview' => 'Luke Skywalker joins forces with a Jedi Knight, a cocky pilot, and two droids to save the galaxy.', 'source' => 'TMDB']
    ],
    '05-25' => [
      ['title' => 'Star Wars: Episode IV - A New Hope', 'year' => 1977, 'genre' => 'Adventure, Fantasy, Sci-Fi', 'rating' => 8.6, 'director' => 'George Lucas', 'poster_url' => 'https://image.tmdb.org/t/p/w300/6FfCtAuVAW8XJjZ7eWeLibRLWTw.jpg', 'overview' => 'Luke Skywalker joins forces with a Jedi Knight, a cocky pilot, and two droids to save the galaxy.', 'source' => 'TMDB']
    ],

    // June
    '06-11' => [
      ['title' => 'E.T. the Extra-Terrestrial', 'year' => 1982, 'genre' => 'Family, Sci-Fi', 'rating' => 7.9, 'director' => 'Steven Spielberg', 'poster_url' => 'https://image.tmdb.org/t/p/w300/5MKL9LKGOhLjNkKPVgxw9gIaGvG.jpg', 'overview' => 'A troubled child summons the courage to help a friendly alien escape Earth.', 'source' => 'TMDB']
    ],
    '06-20' => [
      ['title' => 'Jaws', 'year' => 1975, 'genre' => 'Adventure, Drama, Thriller', 'rating' => 8.1, 'director' => 'Steven Spielberg', 'poster_url' => '', 'overview' => 'A giant great white shark terrorizes a beach town.', 'source' => 'Summer Blockbuster']
    ],

    // July
    '07-04' => [
      ['title' => 'Independence Day', 'year' => 1996, 'genre' => 'Action, Adventure, Sci-Fi', 'rating' => 7.0, 'director' => 'Roland Emmerich', 'poster_url' => '', 'overview' => 'Aliens attack Earth on Independence Day.', 'source' => 'Holiday Films']
    ],
    '07-20' => [
      [
        'title' => 'The Dark Knight',
        'year' => 2008,
        'genre' => 'Action, Crime, Drama',
        'rating' => 9.0,
        'director' => 'Christopher Nolan',
        'poster_url' => 'https://image.tmdb.org/t/p/w300/qJ2tW6WMUDux911r6m7haRef0WH.jpg',
        'overview' => 'Batman raises the stakes in his war on crime with the help of Lt. Jim Gordon and District Attorney Harvey Dent.',
        'source' => 'TMDB'
      ],
      [
        'title' => 'Dunkirk',
        'year' => 2017,
        'genre' => 'Action, Drama, History',
        'rating' => 7.8,
        'director' => 'Christopher Nolan',
        'poster_url' => 'https://image.tmdb.org/t/p/w300/cUqEgoP6kj8ykfNjJx3Tl5zHCcN.jpg',
        'overview' => 'Allied soldiers from Belgium, the British Empire, and France are surrounded by the German Army.',
        'source' => 'TMDB'
      ]
    ],

    // August
    '08-15' => [
      ['title' => 'Apocalypse Now', 'year' => 1979, 'genre' => 'Drama, Mystery, War', 'rating' => 8.4, 'director' => 'Francis Ford Coppola', 'poster_url' => '', 'overview' => 'A U.S. Army officer serving in Vietnam is tasked with assassinating a renegade colonel.', 'source' => 'War Epic']
    ],

    // September
    '09-11' => [
      ['title' => 'World Trade Center', 'year' => 2006, 'genre' => 'Drama, History, Thriller', 'rating' => 6.0, 'director' => 'Oliver Stone', 'poster_url' => '', 'overview' => 'Two Port Authority police officers become trapped under the rubble of the World Trade Center.', 'source' => 'Historical Drama']
    ],

    // October
    '10-31' => [
      ['title' => 'Halloween', 'year' => 1978, 'genre' => 'Horror, Thriller', 'rating' => 7.7, 'director' => 'John Carpenter', 'poster_url' => '', 'overview' => 'Fifteen years after murdering his sister, Michael Myers escapes and returns to his hometown.', 'source' => 'Horror Classic']
    ],

    // November
    '11-22' => [
      ['title' => 'JFK', 'year' => 1991, 'genre' => 'Drama, History, Thriller', 'rating' => 8.0, 'director' => 'Oliver Stone', 'poster_url' => '', 'overview' => 'New Orleans DA Jim Garrison discovers there is more to the Kennedy assassination.', 'source' => 'Historical Thriller']
    ],

    // December
    '12-25' => [
      [
        'title' => 'It\'s a Wonderful Life',
        'year' => 1946,
        'genre' => 'Drama, Family, Fantasy',
        'rating' => 8.6,
        'director' => 'Frank Capra',
        'poster_url' => 'https://image.tmdb.org/t/p/w300/bSqt9rhDZx1Q7UZ86dBPKdNomp2.jpg',
        'overview' => 'An angel is sent from Heaven to help a desperately frustrated businessman.',
        'source' => 'Classic Films'
      ]
    ],
    '12-31' => [
      ['title' => 'Strange Days', 'year' => 1995, 'genre' => 'Crime, Drama, Sci-Fi', 'rating' => 7.2, 'director' => 'Kathryn Bigelow', 'poster_url' => '', 'overview' => 'A former cop turned street-hustler accidentally uncovers a conspiracy in Los Angeles in 1999.', 'source' => 'New Year Films']
    ]
  ];
}

/**
 * Get fallback movies for dates without specific movie data
 *
 * @param int $month Month (1-12)
 * @param int $day Day (1-31)
 * @return array Fallback movie data
 */
function baum_get_fallback_movies($month, $day) {
  // Create a pool of classic movies to rotate through
  $classic_movies = [
    ['title' => 'The Shawshank Redemption', 'year' => 1994, 'genre' => 'Drama', 'rating' => 9.3, 'director' => 'Frank Darabont', 'poster_url' => '', 'overview' => 'Two imprisoned men bond over years, finding solace and redemption.', 'source' => 'Classic Films'],
    // ['title' => 'The Godfather', 'year' => 1972, 'genre' => 'Crime, Drama', 'rating' => 9.2, 'director' => 'Francis Ford Coppola', 'poster_url' => '', 'overview' => 'The aging patriarch of an organized crime dynasty transfers control to his reluctant son.', 'source' => 'Classic Films'],
    // ['title' => 'Pulp Fiction', 'year' => 1994, 'genre' => 'Crime, Drama', 'rating' => 8.9, 'director' => 'Quentin Tarantino', 'poster_url' => '', 'overview' => 'The lives of two mob hitmen, a boxer, and others intertwine in four tales of violence.', 'source' => 'Classic Films'],
    // ['title' => 'Schindler\'s List', 'year' => 1993, 'genre' => 'Biography, Drama, History', 'rating' => 9.0, 'director' => 'Steven Spielberg', 'poster_url' => '', 'overview' => 'In German-occupied Poland, Oskar Schindler saves over a thousand Jewish lives.', 'source' => 'Historical Drama'],
    // ['title' => '12 Angry Men', 'year' => 1957, 'genre' => 'Crime, Drama', 'rating' => 9.0, 'director' => 'Sidney Lumet', 'poster_url' => '', 'overview' => 'A jury holdout attempts to prevent a miscarriage of justice.', 'source' => 'Classic Films'],
    // ['title' => 'The Lord of the Rings: The Return of the King', 'year' => 2003, 'genre' => 'Action, Adventure, Drama', 'rating' => 9.0, 'director' => 'Peter Jackson', 'poster_url' => '', 'overview' => 'Gandalf and Aragorn lead the World of Men against Sauron\'s army.', 'source' => 'Fantasy Epic'],
    // ['title' => 'Goodfellas', 'year' => 1990, 'genre' => 'Biography, Crime, Drama', 'rating' => 8.7, 'director' => 'Martin Scorsese', 'poster_url' => '', 'overview' => 'The story of Henry Hill and his life in the mob.', 'source' => 'Crime Classic'],
    // ['title' => 'Fight Club', 'year' => 1999, 'genre' => 'Drama', 'rating' => 8.8, 'director' => 'David Fincher', 'poster_url' => '', 'overview' => 'An insomniac office worker forms an underground fight club.', 'source' => 'Cult Classic'],
    // ['title' => 'Forrest Gump', 'year' => 1994, 'genre' => 'Drama, Romance', 'rating' => 8.8, 'director' => 'Robert Zemeckis', 'poster_url' => '', 'overview' => 'The presidencies of Kennedy and Johnson through the eyes of an Alabama man.', 'source' => 'Drama'],
    // ['title' => 'Inception', 'year' => 2010, 'genre' => 'Action, Sci-Fi, Thriller', 'rating' => 8.8, 'director' => 'Christopher Nolan', 'poster_url' => '', 'overview' => 'A thief who steals corporate secrets through dream-sharing technology.', 'source' => 'Sci-Fi Thriller']
  ];

  // Use the date to deterministically select movies (so same date always shows same movies)
  $seed = ($month * 31) + $day;
  $movie_count = count($classic_movies);

  // Select 1-2 movies based on the date
  $selected_movies = [];
  $selected_movies[] = $classic_movies[$seed % $movie_count];

  // Add a second movie for some dates
  if ($day % 3 === 0) { // Every 3rd day gets 2 movies
    $selected_movies[] = $classic_movies[($seed + 1) % $movie_count];
  }

  return $selected_movies;
}

/**
 * Fetch domain registration data for famous domains
 *
 * @param string $date Date in Y-m-d format
 * @return array Domain data
 */
function baum_fetch_famous_domains_by_date($date) {
  $famous_domains = baum_get_famous_domains_config();
  $month_day = date('m-d', strtotime($date));
  $domains = $famous_domains[$month_day] ?? [];

  return [
    'domains' => array_map(function($domain) {
      return [
        'domain' => $domain['domain'],
        'year' => $domain['year'],
        'description' => $domain['description'],
        'html' => '<strong>' . esc_html($domain['domain']) . '</strong> (' . $domain['year'] . ') - ' . esc_html($domain['description']),
        'source' => 'Domain History Database'
      ];
    }, $domains)
  ];
}

/**
 * Fetch data from Numbers API (free)
 *
 * @param string $date Date in Y-m-d format
 * @return array Numbers API data
 */
function baum_fetch_numbers_api_data($date) {
  $month = date('n', strtotime($date));
  $day = date('j', strtotime($date));
  $year = date('Y', strtotime($date));

  $facts = [];

  // Get date fact
  $date_url = "http://numbersapi.com/{$month}/{$day}/date";
  $date_response = wp_remote_get($date_url, ['timeout' => 10]);
  if (!is_wp_error($date_response)) {
    $date_fact = wp_remote_retrieve_body($date_response);
    if ($date_fact) {
      $facts[] = [
        'type' => 'date',
        'text' => $date_fact,
        'html' => esc_html($date_fact),
        'source' => 'Numbers API'
      ];
    }
  }

  // Get year fact
  $year_url = "http://numbersapi.com/{$year}/year";
  $year_response = wp_remote_get($year_url, ['timeout' => 10]);
  if (!is_wp_error($year_response)) {
    $year_fact = wp_remote_retrieve_body($year_response);
    if ($year_fact) {
      $facts[] = [
        'type' => 'year',
        'text' => $year_fact,
        'html' => esc_html($year_fact),
        'source' => 'Numbers API'
      ];
    }
  }

  return ['number_facts' => $facts];
}

/**
 * Filter historical events to remove violent content and clean text
 *
 * @param array $events Array of historical events
 * @return array Filtered events
 */
function baum_filter_historical_events($events) {
  $violent_words = ['suicide', 'kill', 'death', 'murder', 'assassin', 'execution', 'massacre', 'genocide', 'war', 'battle', 'bombing', 'terrorist', 'violence', 'attack', 'invasion'];

  $filtered_events = [];

  foreach ($events as $event) {
    $text = $event['text'] ?? $event['html'] ?? '';
    $text_lower = strtolower($text);

    // Check for violent words
    $contains_violence = false;
    foreach ($violent_words as $word) {
      if (strpos($text_lower, $word) !== false) {
        $contains_violence = true;
        break;
      }
    }

    // Skip violent events
    if ($contains_violence) {
      continue;
    }

    // Remove year from beginning of text if present
    $cleaned_text = preg_replace('/^\d{4}\s*[-–—]\s*/', '', $text);
    $cleaned_text = preg_replace('/^\d{4}\s+/', '', $cleaned_text);

    // Update the event with cleaned text
    $event['text'] = $cleaned_text;
    if (isset($event['html'])) {
      $event['html'] = $cleaned_text;
    }

    $filtered_events[] = $event;
  }

  return $filtered_events;
}

/**
 * Get enhanced historical data with external sources
 *
 * @param string $date Date in Y-m-d format
 * @return array Combined local and external historical data
 */
function baum_get_enhanced_history($date) {
  // Get local data
  $local_data = baum_get_comprehensive_history($date);

  // Get external data
  $external_data = baum_fetch_external_history($date);

  // Merge the data
  $enhanced_data = $local_data;

  // Add external events to local events
  if (!empty($external_data['events'])) {
    $enhanced_data['events'] = array_merge($enhanced_data['events'], $external_data['events']);
  }

  // Filter and clean historical events
  if (!empty($enhanced_data['events'])) {
    $enhanced_data['events'] = baum_filter_historical_events($enhanced_data['events']);
  }

  // Add external births to local births
  if (!empty($external_data['births'])) {
    $enhanced_data['births'] = array_merge($enhanced_data['births'], $external_data['births']);
  }

  // Add external deaths to local deaths
  if (!empty($external_data['deaths'])) {
    $enhanced_data['deaths'] = array_merge($enhanced_data['deaths'], $external_data['deaths']);
  }

  // Add new categories from external sources
  $enhanced_data['movies'] = $external_data['movies'] ?? [];
  $enhanced_data['sports'] = $external_data['sports'] ?? [];
  $enhanced_data['music'] = $external_data['music'] ?? [];
  $enhanced_data['technology'] = $external_data['technology'] ?? [];
  $enhanced_data['celebrities'] = $external_data['celebrities'] ?? [];

  // Add new data sources
  $enhanced_data['quotes'] = $external_data['quotes'] ?? [];
  $enhanced_data['facts'] = $external_data['facts'] ?? [];
  $enhanced_data['domains'] = $external_data['domains'] ?? [];
  $enhanced_data['number_facts'] = $external_data['number_facts'] ?? [];
  $enhanced_data['historical_events_api'] = $external_data['historical_events'] ?? [];

  // Sort data by year (most recent first)
  if (!empty($enhanced_data['events'])) {
    usort($enhanced_data['events'], function($a, $b) {
      $year_a = isset($a['year']) ? (int)$a['year'] : 0;
      $year_b = isset($b['year']) ? (int)$b['year'] : 0;
      return $year_b - $year_a; // Descending order
    });

    // Enhance events with cosmological comparisons
    $enhanced_data['events'] = baum_enhance_events_with_cosmology_comparisons($date, $enhanced_data['events']);
  }

  if (!empty($enhanced_data['births'])) {
    usort($enhanced_data['births'], function($a, $b) {
      $year_a = isset($a['year']) ? (int)$a['year'] : 0;
      $year_b = isset($b['year']) ? (int)$b['year'] : 0;
      return $year_b - $year_a; // Descending order
    });
  }

  if (!empty($enhanced_data['deaths'])) {
    usort($enhanced_data['deaths'], function($a, $b) {
      $year_a = isset($a['year']) ? (int)$a['year'] : 0;
      $year_b = isset($b['year']) ? (int)$b['year'] : 0;
      return $year_b - $year_a; // Descending order
    });
  }

  // Add contextual information
  $year = date('Y', strtotime($date));
  $month = date('n', strtotime($date));
  $day = date('j', strtotime($date));

  // Add comprehensive holidays
  $comprehensive_holidays = baum_get_comprehensive_holidays($date);
  $enhanced_data['holidays'] = array_merge($enhanced_data['holidays'], $comprehensive_holidays);

  $enhanced_data['context'] = [
    'chinese_zodiac' => baum_get_chinese_zodiac($year),
    'western_zodiac' => baum_get_western_zodiac($month, $day),
    'pope' => baum_get_pope_for_year($year),
    'potus' => baum_get_potus_for_year($year),
    'year' => $year,
    'month' => $month,
    'day' => $day
  ];

  return $enhanced_data;
}

/**
 * Debug WordPress template loading for date archives
 */
function baum_debug_template_loading() {
  if (!current_user_can('manage_options')) {
    return;
  }

  error_log('=== TEMPLATE LOADING DEBUG ===');
  error_log('Current URL: ' . $_SERVER['REQUEST_URI']);
  error_log('is_date(): ' . (is_date() ? 'TRUE' : 'FALSE'));
  error_log('is_archive(): ' . (is_archive() ? 'TRUE' : 'FALSE'));
  error_log('is_home(): ' . (is_home() ? 'TRUE' : 'FALSE'));
  error_log('is_front_page(): ' . (is_front_page() ? 'TRUE' : 'FALSE'));

  global $wp_query;
  error_log('Query vars: ' . print_r($wp_query->query_vars, true));

  // Check what template WordPress would normally load
  $template_hierarchy = [];
  if (is_date()) {
    $year = get_query_var('year');
    $monthnum = get_query_var('monthnum');
    $day = get_query_var('day');

    if ($day && $monthnum && $year) {
      $template_hierarchy[] = "date-{$year}-{$monthnum}-{$day}.php";
      $template_hierarchy[] = "date-{$year}-{$monthnum}.php";
      $template_hierarchy[] = "date-{$year}.php";
    } elseif ($monthnum && $year) {
      $template_hierarchy[] = "date-{$year}-{$monthnum}.php";
      $template_hierarchy[] = "date-{$year}.php";
    } elseif ($year) {
      $template_hierarchy[] = "date-{$year}.php";
    }

    $template_hierarchy[] = 'date.php';
    $template_hierarchy[] = 'archive.php';
    $template_hierarchy[] = 'index.php';
  }

  error_log('Template hierarchy: ' . print_r($template_hierarchy, true));

  // Check which templates exist
  foreach ($template_hierarchy as $template) {
    $template_path = get_template_directory() . '/' . $template;
    error_log("Template {$template}: " . (file_exists($template_path) ? 'EXISTS' : 'NOT FOUND'));
  }
}

add_action('template_redirect', 'baum_debug_template_loading');

/**
 * Log when date.php template is actually loaded
 */
function baum_log_template_include($template) {
  if (basename($template) === 'date.php') {
    error_log('=== DATE.PHP TEMPLATE INCLUDED ===');
    error_log('Template path: ' . $template);
    error_log('is_date(): ' . (is_date() ? 'TRUE' : 'FALSE'));
    error_log('Current URL: ' . $_SERVER['REQUEST_URI']);
  }
  return $template;
}

add_filter('template_include', 'baum_log_template_include');

/**
 * Debug WordPress query parsing for date URLs
 */
function baum_debug_query_parsing($wp) {
  if (!current_user_can('manage_options')) {
    return;
  }

  $request_uri = $_SERVER['REQUEST_URI'];

  // Only log for date-like URLs
  if (preg_match('#/(\d{4})/(\d{2})/(\d{2})/?#', $request_uri, $matches)) {
    error_log('=== DATE URL DETECTED ===');
    error_log('URL: ' . $request_uri);
    error_log('Matched: Year=' . $matches[1] . ', Month=' . $matches[2] . ', Day=' . $matches[3]);
    error_log('WP Request: ' . $wp->request);
    error_log('WP Matched Rule: ' . $wp->matched_rule);
    error_log('WP Matched Query: ' . $wp->matched_query);

    global $wp_rewrite;
    error_log('Rewrite rules exist: ' . (empty($wp_rewrite->rules) ? 'NO' : 'YES'));

    // Check if date rewrite rules exist
    $date_rules = [];
    foreach ($wp_rewrite->rules as $pattern => $replacement) {
      if (strpos($pattern, 'year') !== false || strpos($replacement, 'year') !== false) {
        $date_rules[$pattern] = $replacement;
      }
    }
    error_log('Date rewrite rules: ' . print_r($date_rules, true));
  }
}

add_action('parse_request', 'baum_debug_query_parsing');

/**
 * Check if WordPress permalink structure supports date archives
 */
function baum_check_permalink_structure() {
  if (!current_user_can('manage_options')) {
    return;
  }

  $permalink_structure = get_option('permalink_structure');
  error_log('=== PERMALINK STRUCTURE CHECK ===');
  error_log('Permalink structure: ' . $permalink_structure);
  error_log('Using pretty permalinks: ' . (empty($permalink_structure) ? 'NO' : 'YES'));

  global $wp_rewrite;
  error_log('Rewrite rules flushed: ' . ($wp_rewrite->rules ? 'YES' : 'NO'));

  // Check if we need to flush rewrite rules
  if (empty($wp_rewrite->rules)) {
    error_log('WARNING: No rewrite rules found - may need to flush permalinks');
  }
}

add_action('init', 'baum_check_permalink_structure');

/**
 * Force flush rewrite rules if they're missing
 */
function baum_force_flush_rewrite_rules() {
  global $wp_rewrite;

  // Check if rewrite rules are missing
  if (empty($wp_rewrite->rules)) {
    error_log('=== FORCING REWRITE RULES FLUSH ===');
    flush_rewrite_rules(false);
    error_log('Rewrite rules flushed successfully');
  }
}

add_action('init', 'baum_force_flush_rewrite_rules', 999);

/**
 * Add custom date archive rewrite rules to handle both formats
 * Using very high priority to ensure they come before post rules
 */
function baum_add_date_archive_rules() {
  // Add rules for date archives without /blog/ prefix - HIGHEST PRIORITY
  add_rewrite_rule(
    '^([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/?$',
    'index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]',
    'top'
  );

  add_rewrite_rule(
    '^([0-9]{4})/([0-9]{1,2})/?$',
    'index.php?year=$matches[1]&monthnum=$matches[2]',
    'top'
  );

  add_rewrite_rule(
    '^([0-9]{4})/?$',
    'index.php?year=$matches[1]',
    'top'
  );

  // Also add rules with /blog/ prefix for consistency - HIGHEST PRIORITY
  add_rewrite_rule(
    '^blog/([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/?$',
    'index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]',
    'top'
  );

  add_rewrite_rule(
    '^blog/([0-9]{4})/([0-9]{1,2})/?$',
    'index.php?year=$matches[1]&monthnum=$matches[2]',
    'top'
  );

  add_rewrite_rule(
    '^blog/([0-9]{4})/?$',
    'index.php?year=$matches[1]',
    'top'
  );

  error_log('=== CUSTOM DATE REWRITE RULES ADDED ===');
}

// Removed excessive rewrite rule prioritization - not needed

add_action('init', 'baum_add_date_archive_rules');

/**
 * Flush rewrite rules when theme is activated to ensure our custom rules are loaded
 */
function baum_flush_rewrite_on_theme_activation() {
  baum_add_date_archive_rules();
  flush_rewrite_rules();
  error_log('=== REWRITE RULES FLUSHED ON THEME ACTIVATION ===');
}

add_action('after_switch_theme', 'baum_flush_rewrite_on_theme_activation');

/**
 * Add admin notice and manual flush option
 */
function baum_admin_notice_flush_permalinks() {
  global $wp_rewrite;

  if (empty($wp_rewrite->rules) && current_user_can('manage_options')) {
    echo '<div class="notice notice-warning is-dismissible">';
    echo '<p><strong>Date Archives Issue:</strong> Rewrite rules are missing. ';
    echo '<a href="' . admin_url('options-permalink.php') . '">Go to Permalinks Settings</a> and click "Save Changes" to fix date archive URLs.</p>';
    echo '</div>';
  }
}

// TEMPORARILY DISABLED - TESTING FOR MEMORY ISSUES
// add_action('admin_notices', 'baum_admin_notice_flush_permalinks');

/**
 * Add manual flush option via URL parameter
 */
function baum_manual_flush_rewrite_rules() {
  if (isset($_GET['flush_rewrite_rules']) && current_user_can('manage_options')) {
    baum_add_date_archive_rules();
    flush_rewrite_rules(false);

    wp_redirect(remove_query_arg('flush_rewrite_rules'));
    exit;
  }
}

// Removed excessive flush logic - not needed

add_action('init', 'baum_manual_flush_rewrite_rules');

/**
 * Calculate holidays with relative dates (nth weekday of month)
 *
 * @param int $year Year to calculate for
 * @param int $month Month (1-12)
 * @param int $weekday Weekday (0=Sunday, 1=Monday, etc.)
 * @param int $occurrence Which occurrence (1=first, 2=second, etc.)
 * @return string Date in Y-m-d format
 */
function baum_calculate_nth_weekday($year, $month, $weekday, $occurrence) {
  // First day of the month
  $first_day = mktime(0, 0, 0, $month, 1, $year);
  $first_weekday = date('w', $first_day);

  // Calculate days to add to get to the first occurrence of the weekday
  $days_to_add = ($weekday - $first_weekday + 7) % 7;

  // Add days for the specific occurrence
  $days_to_add += ($occurrence - 1) * 7;

  // Calculate the final date
  $target_date = mktime(0, 0, 0, $month, 1 + $days_to_add, $year);

  return date('Y-m-d', $target_date);
}

/**
 * Calculate Friday the 13th dates for a year
 *
 * @param int $year Year to calculate for
 * @return array Array of Friday the 13th dates
 */
function baum_calculate_friday_13th($year) {
  $dates = [];

  for ($month = 1; $month <= 12; $month++) {
    $date = mktime(0, 0, 0, $month, 13, $year);
    if (date('w', $date) == 5) { // 5 = Friday
      $dates[] = date('Y-m-d', $date);
    }
  }

  return $dates;
}

/**
 * Calculate Jewish holidays (simplified - would need proper Hebrew calendar)
 *
 * @param int $year Year to calculate for
 * @return array Array of Jewish holiday dates
 */
function baum_calculate_jewish_holidays($year) {
  // Simplified calculation - in reality would need proper Hebrew calendar conversion
  // These are approximate dates based on typical Gregorian equivalents

  $holidays = [];

  // Rosh Hashanah (Jewish New Year) - typically September/October
  // Simplified: usually around September 15-October 15
  $rosh_hashanah_base = mktime(0, 0, 0, 9, 15, $year);
  $holidays['rosh_hashanah'] = date('Y-m-d', $rosh_hashanah_base);

  // Passover - typically March/April
  // Simplified: usually around March 15-April 15
  $passover_base = mktime(0, 0, 0, 4, 1, $year);
  $holidays['passover'] = date('Y-m-d', $passover_base);

  // Tu BiShvat (New Year of Trees) - typically January/February
  // Simplified: usually around January 15-February 15
  $tu_bishvat_base = mktime(0, 0, 0, 2, 1, $year);
  $holidays['tu_bishvat'] = date('Y-m-d', $tu_bishvat_base);

  return $holidays;
}

/**
 * Get comprehensive holiday data for a date
 *
 * @param string $date Date in Y-m-d format
 * @return array Holiday information
 */
function baum_get_comprehensive_holidays($date) {
  $year = date('Y', strtotime($date));
  $month = date('n', strtotime($date));
  $day = date('j', strtotime($date));
  $date_key = date('m-d', strtotime($date));

  $holidays = [];

  // Fixed date holidays
  $fixed_holidays = [
    '01-01' => 'New Year\'s Day',
    '04-01' => 'April Fools\' Day',
    '08-01' => 'August Fools\' Day',
    '03-14' => 'International Pi Day',
    '03-10' => 'MAR10 Day (Mario Day)',
    '07-25' => 'Saint James Day',
    '10-31' => 'Halloween',
    '11-01' => 'All Saints\' Day',
    '02-14' => 'Valentine\'s Day',
    '05-20' => 'World Bee Day',
    '05-21' => 'World Tea Day',
    '06-19' => 'Garfield Day',
    '08-08' => 'Odie Day',
    '07-25' => 'Christmas in July',
    '07-27' => 'Cowboy Day',
    '07-19' => 'Ancient Egyptian New Year'
  ];

  if (isset($fixed_holidays[$date_key])) {
    $holidays[] = [
      'name' => $fixed_holidays[$date_key],
      'type' => 'fixed',
      'date' => $date
    ];
  }

  // Calculate relative date holidays
  $relative_holidays = [];

  // Mother's Day - Second Sunday in May
  if ($month == 5) {
    $mothers_day = baum_calculate_nth_weekday($year, 5, 0, 2);
    if ($mothers_day == $date) {
      $holidays[] = [
        'name' => 'Mother\'s Day',
        'type' => 'relative',
        'date' => $date
      ];
    }
  }

  // Father's Day - Third Sunday in June
  if ($month == 6) {
    $fathers_day = baum_calculate_nth_weekday($year, 6, 0, 3);
    if ($fathers_day == $date) {
      $holidays[] = [
        'name' => 'Father\'s Day',
        'type' => 'relative',
        'date' => $date
      ];
    }
  }

  // Black Friday - Fourth Friday in November
  if ($month == 11) {
    $black_friday = baum_calculate_nth_weekday($year, 11, 5, 4);
    if ($black_friday == $date) {
      $holidays[] = [
        'name' => 'Black Friday',
        'type' => 'relative',
        'date' => $date
      ];
    }
  }

  // Friday the 13th
  $friday_13th_dates = baum_calculate_friday_13th($year);
  if (in_array($date, $friday_13th_dates)) {
    $holidays[] = [
      'name' => 'Friday the 13th',
      'type' => 'special',
      'date' => $date
    ];
  }

  // Jewish holidays (simplified)
  $jewish_holidays = baum_calculate_jewish_holidays($year);
  foreach ($jewish_holidays as $holiday_key => $holiday_date) {
    if ($holiday_date == $date) {
      $holiday_names = [
        'rosh_hashanah' => 'Jewish Head of the Year (Rosh Hashanah)',
        'passover' => 'Passover',
        'tu_bishvat' => 'New Year for Trees (Tu BiShvat)'
      ];

      $holidays[] = [
        'name' => $holiday_names[$holiday_key],
        'type' => 'jewish',
        'date' => $date
      ];
    }
  }

  return $holidays;
}

/**
 * AJAX handler for loading more historical events
 */
function baum_load_more_events_ajax() {
  if (!isset($_GET['ajax']) || $_GET['ajax'] !== '1' || $_GET['action'] !== 'load_more_events') {
    return;
  }

  $date = sanitize_text_field($_GET['date'] ?? '');
  $page = intval($_GET['page'] ?? 1);

  if (empty($date)) {
    wp_die(json_encode(['success' => false, 'error' => 'No date provided']));
  }

  // Get more historical events (offset by page)
  $offset = ($page - 1) * 10;
  $events = baum_fetch_more_historical_events($date, $offset, 10);

  wp_die(json_encode([
    'success' => true,
    'events' => $events,
    'page' => $page
  ]));
}

add_action('init', 'baum_load_more_events_ajax');

/**
 * Fetch more historical events with pagination
 *
 * @param string $date Date in Y-m-d format
 * @param int $offset Offset for pagination
 * @param int $limit Number of events to fetch
 * @return array Historical events
 */
function baum_fetch_more_historical_events($date, $offset = 0, $limit = 10) {
  $month = date('n', strtotime($date));
  $day = date('j', strtotime($date));

  $sample_events = [];

  // Generate sample historical events for demonstration
  for ($i = $offset; $i < $offset + $limit; $i++) {
    $year = 2024 - $i - 10; // Go back in time

    if ($year < 1000) break; // Don't go too far back

    // Get cosmological data for this event
    $cosmology_data = baum_get_cosmology_for_event_date($year, $month, $day);

    $sample_events[] = [
      'year' => $year,
      'text' => "Historical event #{$i} occurred on this date in {$year}",
      'html' => "<strong>Historical event #{$i}</strong> occurred on this date in {$year}",
      'source' => 'Sample Data',
      'cosmology_data' => array_values($cosmology_data), // Convert to indexed array for JSON
      'cosmology' => baum_format_cosmology_for_display($cosmology_data) // Legacy format
    ];
  }

  return $sample_events;
}

/**
 * Log any redirects that might be happening
 */
function baum_log_redirects($location, $status) {
  if (current_user_can('manage_options')) {
    error_log('=== REDIRECT DETECTED ===');
    error_log('From: ' . $_SERVER['REQUEST_URI']);
    error_log('To: ' . $location);
    error_log('Status: ' . $status);
    error_log('Backtrace: ' . wp_debug_backtrace_summary());
  }
  return $location;
}

add_filter('wp_redirect', 'baum_log_redirects', 10, 2);

/**
 * Log 404 errors that might be causing redirects
 */
function baum_log_404_errors() {
  if (is_404() && current_user_can('manage_options')) {
    error_log('=== 404 ERROR ===');
    error_log('URL: ' . $_SERVER['REQUEST_URI']);
    error_log('is_date(): ' . (is_date() ? 'TRUE' : 'FALSE'));

    global $wp_query;
    error_log('Query vars: ' . print_r($wp_query->query_vars, true));
    error_log('Posts found: ' . $wp_query->found_posts);
  }
}

add_action('wp', 'baum_log_404_errors');

/**
 * Force date archives to load even when no posts exist for that date
 */
function baum_force_date_archives() {
  global $wp_query;

  // Check if this is a 404 but should be a date archive
  if (is_404() && !empty($wp_query->query_vars)) {
    $year = get_query_var('year');
    $monthnum = get_query_var('monthnum');
    $day = get_query_var('day');

    // If we have valid date query vars, force it to be a date archive
    if ($year && $monthnum && $day) {
      // Validate the date
      if (checkdate($monthnum, $day, $year)) {
        $wp_query->is_404 = false;
        $wp_query->is_date = true;
        $wp_query->is_day = true;
        $wp_query->is_archive = true;

        // Set the found_posts to 0 but don't make it a 404
        $wp_query->found_posts = 0;
        $wp_query->post_count = 0;

        error_log('=== FORCED DATE ARCHIVE ===');
        error_log('Date: ' . $year . '-' . $monthnum . '-' . $day);
        error_log('is_date(): ' . (is_date() ? 'TRUE' : 'FALSE'));
      }
    }
  }
}

add_action('wp', 'baum_force_date_archives');

/**
 * Get comprehensive astrological data for a specific event date with caching
 *
 * @param int $year Year of the event
 * @param int $month Month of the event
 * @param int $day Day of the event
 * @return array Comprehensive astrological information
 */
function baum_get_cosmology_for_event_date($year, $month, $day) {
  $event_date = sprintf('%04d-%02d-%02d', $year, $month, $day);

  // Check cache first
  $cache_key = 'baum_event_astro_' . $event_date;
  $cached_data = get_transient($cache_key);
  if ($cached_data) {
    return $cached_data;
  }

  // Check if baum-cosmology plugin is available
  global $baum_cosmology;
  if (!$baum_cosmology || !class_exists('Baum_Cosmology')) {
    return [];
  }

  try {
    // Get comprehensive astrological data from baum-cosmology
    $astro_data = $baum_cosmology->get_comprehensive_astro_data($event_date);

    if (empty($astro_data)) {
      return [];
    }

    $cosmology_data = [];

    // Moon phase
    if (isset($astro_data['moon'])) {
      $moon = $astro_data['moon'];
      $cosmology_data['moon_phase'] = [
        'type' => 'moon_phase',
        'icon' => baum_get_moon_phase_icon($moon['phase_name']),
        'text' => $moon['phase_name'],
        'detail' => $moon['illumination'] . '% illuminated',
        'significance' => baum_get_moon_phase_significance($moon['phase_name']),
        'raw_data' => $moon
      ];
    }

    // Major planetary aspects
    if (isset($astro_data['aspects']) && !empty($astro_data['aspects'])) {
      $major_aspects = array_filter($astro_data['aspects'], function($aspect) {
        return in_array($aspect['aspect'], ['Conjunction', 'Opposition', 'Square', 'Trine', 'Sextile']) && $aspect['orb'] <= 3;
      });

      if (!empty($major_aspects)) {
        $aspect = $major_aspects[0]; // Take the tightest aspect
        $cosmology_data['major_aspect'] = [
          'type' => 'planetary_aspect',
          'icon' => baum_get_aspect_icon($aspect['aspect']),
          'text' => $aspect['planet1'] . ' ' . $aspect['aspect'] . ' ' . $aspect['planet2'],
          'detail' => 'Orb: ' . $aspect['orb'] . '°',
          'significance' => baum_get_aspect_significance($aspect['aspect']),
          'raw_data' => $aspect
        ];
      }
    }

    // Planetary ingresses (sign changes)
    if (isset($astro_data['ingresses']) && !empty($astro_data['ingresses'])) {
      $ingress = $astro_data['ingresses'][0]; // Take the closest one
      $cosmology_data['ingress'] = [
        'type' => 'planetary_ingress',
        'icon' => '🌟',
        'text' => $ingress['planet'] . ' enters ' . $ingress['to_sign'],
        'detail' => abs($ingress['days_from_target']) == 0 ? 'Today' : abs($ingress['days_from_target']) . ' days away',
        'significance' => 'Planetary sign change',
        'raw_data' => $ingress
      ];
    }

    // Lunar phases nearby
    if (isset($astro_data['lunar_phases']) && !empty($astro_data['lunar_phases'])) {
      $lunar_phase = $astro_data['lunar_phases'][0]; // Take the closest one
      if (abs($lunar_phase['days_from_target']) <= 3) { // Within 3 days
        $cosmology_data['lunar_event'] = [
          'type' => 'lunar_phase_event',
          'icon' => baum_get_lunar_phase_icon($lunar_phase['phase']),
          'text' => $lunar_phase['phase'],
          'detail' => abs($lunar_phase['days_from_target']) == 0 ? 'Today' : abs($lunar_phase['days_from_target']) . ' days away',
          'significance' => 'Lunar phase transition',
          'raw_data' => $lunar_phase
        ];
      }
    }

    // Store raw comprehensive data for comparison
    $cosmology_data['_raw_astro_data'] = $astro_data;

    // Cache for 6 hours (longer than current date cache)
    set_transient($cache_key, $cosmology_data, 6 * HOUR_IN_SECONDS);

    return $cosmology_data;

  } catch (Exception $e) {
    error_log('Comprehensive astrology calculation error for ' . $event_date . ': ' . $e->getMessage());
    return [];
  }
}

/**
 * Get moon phase icon
 */
function baum_get_moon_phase_icon($phase_name) {
  $icons = [
    'New Moon' => '🌑',
    'Waxing Crescent' => '🌒',
    'First Quarter' => '🌓',
    'Waxing Gibbous' => '🌔',
    'Full Moon' => '🌕',
    'Waning Gibbous' => '🌖',
    'Third Quarter' => '🌗',
    'Waning Crescent' => '🌘'
  ];

  return $icons[$phase_name] ?? '🌙';
}

/**
 * Get moon phase significance
 */
function baum_get_moon_phase_significance($phase_name) {
  $significance = [
    'New Moon' => 'Time of new beginnings',
    'Waxing Crescent' => 'Growth and intention setting',
    'First Quarter' => 'Decision making and action',
    'Waxing Gibbous' => 'Refinement and adjustment',
    'Full Moon' => 'Peak energy and culmination',
    'Waning Gibbous' => 'Gratitude and sharing',
    'Third Quarter' => 'Release and letting go',
    'Waning Crescent' => 'Rest and reflection'
  ];

  return $significance[$phase_name] ?? 'Lunar influence';
}

/**
 * Get aspect icon
 */
function baum_get_aspect_icon($aspect_name) {
  $icons = [
    'Conjunction' => '☌',
    'Opposition' => '☍',
    'Square' => '□',
    'Trine' => '△',
    'Sextile' => '⚹',
    'Quincunx' => '⚻',
    'Semisquare' => '∠',
    'Sesquiquadrate' => '⚼'
  ];

  return $icons[$aspect_name] ?? '⚹';
}

/**
 * Get aspect significance
 */
function baum_get_aspect_significance($aspect_name) {
  $significance = [
    'Conjunction' => 'Powerful fusion of energies',
    'Opposition' => 'Dynamic tension and balance',
    'Square' => 'Challenge and growth opportunity',
    'Trine' => 'Harmonious flow of energy',
    'Sextile' => 'Supportive and cooperative',
    'Quincunx' => 'Adjustment and adaptation needed',
    'Semisquare' => 'Minor friction and irritation',
    'Sesquiquadrate' => 'Creative tension'
  ];

  return $significance[$aspect_name] ?? 'Planetary interaction';
}

/**
 * Get lunar phase event icon
 */
function baum_get_lunar_phase_icon($phase_name) {
  $icons = [
    'New Moon' => '🌑',
    'First Quarter' => '🌓',
    'Full Moon' => '🌕',
    'Third Quarter' => '🌗'
  ];

  return $icons[$phase_name] ?? '🌙';
}

/**
 * Get planet icon
 */
function baum_get_planet_icon($planet_name) {
  $icons = [
    'Sun' => '☉',
    'Moon' => '☽',
    'Mercury' => '☿',
    'Venus' => '♀',
    'Mars' => '♂',
    'Jupiter' => '♃',
    'Saturn' => '♄',
    'Uranus' => '♅',
    'Neptune' => '♆',
    'Pluto' => '♇'
  ];

  return $icons[$planet_name] ?? '🪐';
}

/**
 * Format cosmology data for display (legacy format)
 */
function baum_format_cosmology_for_display($cosmology_data) {
  if (empty($cosmology_data)) {
    return '';
  }

  $formatted_items = [];
  foreach ($cosmology_data as $item) {
    $text = $item['icon'] . ' ' . $item['text'];
    if (!empty($item['detail'])) {
      $text .= ' (' . $item['detail'] . ')';
    }
    $formatted_items[] = '<span class="cosmo-item" title="' . esc_attr($item['significance']) . '">' . $text . '</span>';
  }

  return implode('', $formatted_items);
}

/**
 * Compare comprehensive astrological data between two dates and find similarities
 *
 * @param array $current_date_cosmology Cosmology data for current date
 * @param array $event_date_cosmology Cosmology data for event date
 * @return array Array of similarities found
 */
function baum_compare_cosmological_data($current_date_cosmology, $event_date_cosmology) {
  $similarities = [];

  if (empty($current_date_cosmology) || empty($event_date_cosmology)) {
    return $similarities;
  }

  // Get raw astrological data for deep comparison
  $current_raw = $current_date_cosmology['_raw_astro_data'] ?? [];
  $event_raw = $event_date_cosmology['_raw_astro_data'] ?? [];

  // Compare moon phases
  if (isset($current_date_cosmology['moon_phase']) && isset($event_date_cosmology['moon_phase'])) {
    $current_phase = $current_date_cosmology['moon_phase']['text'];
    $event_phase = $event_date_cosmology['moon_phase']['text'];

    if ($current_phase === $event_phase) {
      $similarities[] = [
        'type' => 'moon_phase_exact',
        'icon' => '🌙',
        'text' => 'Same moon phase',
        'detail' => $current_phase,
        'significance' => 'Identical lunar energy'
      ];
    } else {
      // Check for similar phase categories
      $current_category = $current_raw['moon']['phase_category'] ?? '';
      $event_category = $event_raw['moon']['phase_category'] ?? '';

      if ($current_category && $current_category === $event_category && $current_category !== 'new' && $current_category !== 'full') {
        $similarities[] = [
          'type' => 'moon_phase_similar',
          'icon' => '🌙',
          'text' => 'Similar moon phase',
          'detail' => 'Both ' . $current_category,
          'significance' => 'Similar lunar energy'
        ];
      }
    }
  }

  // Compare planetary positions in same signs
  if (isset($current_raw['planets']) && isset($event_raw['planets'])) {
    $current_planets = $current_raw['planets'];
    $event_planets = $event_raw['planets'];

    foreach ($current_planets as $planet => $current_pos) {
      if (isset($event_planets[$planet])) {
        $event_pos = $event_planets[$planet];

        // Same sign
        if ($current_pos['sign'] === $event_pos['sign']) {
          $similarities[] = [
            'type' => 'planet_same_sign',
            'icon' => baum_get_planet_icon($planet),
            'text' => $planet . ' in ' . $current_pos['sign'],
            'detail' => 'Same zodiac sign',
            'significance' => 'Similar planetary energy'
          ];
        }

        // Similar degree (within 5 degrees)
        $degree_diff = abs($current_pos['degree_in_sign'] - $event_pos['degree_in_sign']);
        if ($degree_diff <= 5 && $current_pos['sign'] === $event_pos['sign']) {
          $similarities[] = [
            'type' => 'planet_similar_degree',
            'icon' => baum_get_planet_icon($planet),
            'text' => $planet . ' similar position',
            'detail' => 'Within ' . round($degree_diff, 1) . '° in ' . $current_pos['sign'],
            'significance' => 'Very similar planetary influence'
          ];
        }
      }
    }
  }

  // Compare major aspects
  if (isset($current_date_cosmology['major_aspect']) && isset($event_date_cosmology['major_aspect'])) {
    $current_aspect = $current_date_cosmology['major_aspect']['raw_data'];
    $event_aspect = $event_date_cosmology['major_aspect']['raw_data'];

    // Same aspect type between same planets
    if ($current_aspect['aspect'] === $event_aspect['aspect'] &&
        $current_aspect['planet1'] === $event_aspect['planet1'] &&
        $current_aspect['planet2'] === $event_aspect['planet2']) {
      $similarities[] = [
        'type' => 'aspect_exact',
        'icon' => baum_get_aspect_icon($current_aspect['aspect']),
        'text' => 'Same planetary aspect',
        'detail' => $current_aspect['planet1'] . ' ' . $current_aspect['aspect'] . ' ' . $current_aspect['planet2'],
        'significance' => 'Identical planetary dynamic'
      ];
    }
  }

  // Compare lunar events
  if (isset($current_date_cosmology['lunar_event']) && isset($event_date_cosmology['lunar_event'])) {
    $current_lunar = $current_date_cosmology['lunar_event']['raw_data'];
    $event_lunar = $event_date_cosmology['lunar_event']['raw_data'];

    if ($current_lunar['phase'] === $event_lunar['phase']) {
      $similarities[] = [
        'type' => 'lunar_event_same',
        'icon' => baum_get_lunar_phase_icon($current_lunar['phase']),
        'text' => 'Same lunar event',
        'detail' => $current_lunar['phase'],
        'significance' => 'Powerful lunar synchronicity'
      ];
    }
  }

  // Compare planetary ingresses
  if (isset($current_date_cosmology['ingress']) && isset($event_date_cosmology['ingress'])) {
    $current_ingress = $current_date_cosmology['ingress']['raw_data'];
    $event_ingress = $event_date_cosmology['ingress']['raw_data'];

    if ($current_ingress['planet'] === $event_ingress['planet'] &&
        $current_ingress['to_sign'] === $event_ingress['to_sign']) {
      $similarities[] = [
        'type' => 'ingress_same',
        'icon' => '🌟',
        'text' => 'Same planetary ingress',
        'detail' => $current_ingress['planet'] . ' enters ' . $current_ingress['to_sign'],
        'significance' => 'Identical cosmic transition'
      ];
    }
  }

  return $similarities;
}

/**
 * Get enhanced cosmological data with comparisons for historical events
 *
 * @param string $current_date Current date being viewed
 * @param array $historical_events Array of historical events
 * @return array Enhanced events with comparison data
 */
function baum_enhance_events_with_cosmology_comparisons($current_date, $historical_events) {
  if (empty($historical_events)) {
    return $historical_events;
  }

  // Get cosmology for current date
  $current_year = date('Y', strtotime($current_date));
  $current_month = date('n', strtotime($current_date));
  $current_day = date('j', strtotime($current_date));
  $current_cosmology = baum_get_cosmology_for_event_date($current_year, $current_month, $current_day);

  $enhanced_events = [];

  foreach ($historical_events as $event) {
    $enhanced_event = $event;

    if (isset($event['year'])) {
      // Get cosmology for event date
      $event_cosmology = baum_get_cosmology_for_event_date($event['year'], $current_month, $current_day);

      // Find similarities
      $similarities = baum_compare_cosmological_data($current_cosmology, $event_cosmology);

      // Add cosmology data to event
      $enhanced_event['cosmology_data'] = array_values($event_cosmology);
      $enhanced_event['cosmology_similarities'] = $similarities;
      $enhanced_event['has_similarities'] = !empty($similarities);

      // Create display format
      $all_cosmology = array_merge(array_values($event_cosmology), $similarities);
      $enhanced_event['cosmology'] = baum_format_cosmology_for_display($all_cosmology);
    }

    $enhanced_events[] = $enhanced_event;
  }

  return $enhanced_events;
}

/**
 * Performance Monitoring and Optimization Functions
 */

/**
 * Warm up the cache for popular dates
 *
 * @return void
 */
function baum_warm_time_capsule_cache() {
  $popular_dates = [
    '01-01', // New Year's Day
    '07-04', // Independence Day
    '12-25', // Christmas
    '02-14', // Valentine's Day
    '10-31', // Halloween
    '07-20', // Moon Landing Day
    date('m-d'), // Today
  ];

  foreach ($popular_dates as $month_day) {
    $date = date('Y') . '-' . $month_day;
    baum_get_comprehensive_history($date);
  }
}

/**
 * Clear time capsule caches
 *
 * @return void
 */
function baum_clear_time_capsule_cache() {
  // Clear WordPress transients
  global $wpdb;
  $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_baum_%'");
  $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_baum_%'");

  // Clear object cache
  wp_cache_flush_group('baum_time_capsule');
}

/**
 * Get time capsule performance stats
 *
 * @return array Performance statistics
 */
function baum_get_time_capsule_stats() {
  $stats = get_option('baum_time_capsule_stats', [
    'total_requests' => 0,
    'cache_hits' => 0,
    'cache_misses' => 0,
    'avg_response_time' => 0,
    'slow_queries' => 0
  ]);

  return $stats;
}

/**
 * Update time capsule performance stats
 *
 * @param array $performance_data Performance data from query
 * @return void
 */
function baum_update_time_capsule_stats($performance_data) {
  $stats = baum_get_time_capsule_stats();

  $stats['total_requests']++;

  if (isset($performance_data['total_time'])) {
    $stats['avg_response_time'] = (($stats['avg_response_time'] * ($stats['total_requests'] - 1)) + $performance_data['total_time']) / $stats['total_requests'];

    if ($performance_data['total_time'] > 1.0) {
      $stats['slow_queries']++;
    }
  }

  update_option('baum_time_capsule_stats', $stats);
}

/**
 * Schedule cache warming
 */
function baum_schedule_cache_warming() {
  if (!wp_next_scheduled('baum_warm_cache_hook')) {
    wp_schedule_event(time(), 'daily', 'baum_warm_cache_hook');
  }
}
add_action('wp', 'baum_schedule_cache_warming');

/**
 * Hook for cache warming
 */
add_action('baum_warm_cache_hook', 'baum_warm_time_capsule_cache');

/**
 * Add admin menu for time capsule management
 */
function baum_time_capsule_admin_menu() {
  add_management_page(
    'Time Capsule Performance',
    'Time Capsule',
    'manage_options',
    'baum-time-capsule',
    'baum_time_capsule_admin_page'
  );
}
// TEMPORARILY DISABLED - TESTING FOR MEMORY ISSUES
// add_action('admin_menu', 'baum_time_capsule_admin_menu');

/**
 * Admin page for time capsule management
 */
function baum_time_capsule_admin_page() {
  if (isset($_POST['clear_cache'])) {
    baum_clear_time_capsule_cache();
    echo '<div class="notice notice-success"><p>Cache cleared successfully!</p></div>';
  }

  if (isset($_POST['warm_cache'])) {
    baum_warm_time_capsule_cache();
    echo '<div class="notice notice-success"><p>Cache warmed successfully!</p></div>';
  }

  $stats = baum_get_time_capsule_stats();

  ?>
  <div class="wrap">
    <h1>Time Capsule Performance</h1>

    <div class="card">
      <h2>Performance Statistics</h2>
      <table class="widefat">
        <tr><td>Total Requests</td><td><?php echo number_format($stats['total_requests']); ?></td></tr>
        <tr><td>Cache Hits</td><td><?php echo number_format($stats['cache_hits']); ?></td></tr>
        <tr><td>Cache Misses</td><td><?php echo number_format($stats['cache_misses']); ?></td></tr>
        <tr><td>Average Response Time</td><td><?php echo number_format($stats['avg_response_time'], 3); ?>s</td></tr>
        <tr><td>Slow Queries (>1s)</td><td><?php echo number_format($stats['slow_queries']); ?></td></tr>
        <tr><td>Cache Hit Rate</td><td><?php echo $stats['total_requests'] > 0 ? number_format(($stats['cache_hits'] / $stats['total_requests']) * 100, 1) : 0; ?>%</td></tr>
      </table>
    </div>

    <div class="card">
      <h2>Cache Management</h2>
      <form method="post">
        <p>
          <input type="submit" name="clear_cache" class="button" value="Clear Cache" />
          <input type="submit" name="warm_cache" class="button button-primary" value="Warm Cache" />
        </p>
      </form>
    </div>
  </div>
  <?php
}

/////////////////////////////////////
// Dark Mode Toggle Integration
/////////////////////////////////////

/**
 * AJAX handler to save dark mode preference
 * Integrates the frontend toggle with Kirki theme mods
 */
function baum_save_dark_mode_ajax() {
  // Verify nonce
  if (!wp_verify_nonce($_POST['nonce'], 'baum_dark_mode_nonce')) {
    wp_die('Security check failed');
  }

  $mode = sanitize_text_field($_POST['mode']);

  // Validate mode value
  if (!in_array($mode, ['auto', 'light', 'dark'])) {
    wp_die('Invalid mode');
  }

  // Convert 'light' to 'off' for Kirki compatibility
  $kirki_mode = ($mode === 'light') ? 'off' : (($mode === 'dark') ? 'on' : 'auto');

  // Save to theme mods
  set_theme_mod('baum_dark_mode', $kirki_mode);

  wp_send_json_success(['mode' => $mode, 'kirki_mode' => $kirki_mode]);
}

add_action('wp_ajax_baum_save_dark_mode', 'baum_save_dark_mode_ajax');
add_action('wp_ajax_nopriv_baum_save_dark_mode', 'baum_save_dark_mode_ajax');

/**
 * Add CSS classes to body based on dark mode setting
 * This ensures server-side rendering matches client-side state
 */
function baum_add_dark_mode_body_class($classes) {
  $dark_mode = get_theme_mod('baum_dark_mode', 'auto');

  if ($dark_mode === 'on') {
    $classes[] = 'dark';
    $classes[] = 'dark-mode';
  } elseif ($dark_mode === 'auto') {
    // Add a class to indicate auto mode - CSS will handle the rest
    $classes[] = 'dark-mode-auto';
  }

  return $classes;
}

add_filter('body_class', 'baum_add_dark_mode_body_class');

/**
 * Get meteor showers only if we're precisely on the peak date
 *
 * @param object $baum_cosmology Cosmology instance
 * @param string $date Date in Y-m-d format
 * @return array Meteor showers for exact date
 */
function baum_get_meteor_showers_for_date($baum_cosmology, $date) {
  // Get all meteor showers for a wider range
  $all_showers = $baum_cosmology->get_meteor_showers($date, 365);

  if (empty($all_showers)) {
    return [];
  }

  $target_date = date('Y-m-d', strtotime($date));
  $exact_showers = [];

  foreach ($all_showers as $shower) {
    if (isset($shower['peak_date'])) {
      $peak_date = date('Y-m-d', strtotime($shower['peak_date']));

      // Only include if we're exactly on the peak date
      if ($peak_date === $target_date) {
        $exact_showers[] = $shower;
      }
    }
  }

  return $exact_showers;
}

/**
 * Get planet symbol for display
 *
 * @param string $planet_name Planet name
 * @return string Planet symbol
 */
function baum_get_planet_symbol($planet_name) {
  $symbols = [
    'Mercury' => '☿',
    'Venus' => '♀',
    'Earth' => '🌍',
    'Mars' => '♂',
    'Jupiter' => '♃',
    'Saturn' => '♄',
    'Uranus' => '♅',
    'Neptune' => '♆',
    'Pluto' => '♇',
    'Sun' => '☉',
    'Moon' => '☽'
  ];

  return $symbols[$planet_name] ?? '●';
}

/**
 * Get Chinese zodiac animal for a given year
 *
 * @param int $year Year
 * @return array Chinese zodiac info
 */
function baum_get_chinese_zodiac($year) {
  $animals = [
    'Rat', 'Ox', 'Tiger', 'Rabbit', 'Dragon', 'Snake',
    'Horse', 'Goat', 'Monkey', 'Rooster', 'Dog', 'Pig'
  ];

  $elements = ['Wood', 'Fire', 'Earth', 'Metal', 'Water'];

  // Ensure year is a valid integer
  $year = (int) $year;
  if ($year < 1900) {
    $year = 1900; // Default to 1900 for very old dates
  }

  // Chinese zodiac starts from 1900 as Year of the Rat
  $animal_index = ($year - 1900) % 12;
  $element_index = floor(($year - 1900) / 2) % 5;

  return [
    'animal' => $animals[$animal_index],
    'element' => $elements[$element_index],
    'full_name' => $elements[$element_index] . ' ' . $animals[$animal_index]
  ];
}

/**
 * Get Western zodiac sign for a date
 *
 * @param int $month Month (1-12)
 * @param int $day Day (1-31)
 * @return array Western zodiac info
 */
function baum_get_western_zodiac($month, $day) {
  $signs = [
    ['name' => 'Capricorn', 'symbol' => '♑', 'start' => [12, 22], 'end' => [1, 19]],
    ['name' => 'Aquarius', 'symbol' => '♒', 'start' => [1, 20], 'end' => [2, 18]],
    ['name' => 'Pisces', 'symbol' => '♓', 'start' => [2, 19], 'end' => [3, 20]],
    ['name' => 'Aries', 'symbol' => '♈', 'start' => [3, 21], 'end' => [4, 19]],
    ['name' => 'Taurus', 'symbol' => '♉', 'start' => [4, 20], 'end' => [5, 20]],
    ['name' => 'Gemini', 'symbol' => '♊', 'start' => [5, 21], 'end' => [6, 20]],
    ['name' => 'Cancer', 'symbol' => '♋', 'start' => [6, 21], 'end' => [7, 22]],
    ['name' => 'Leo', 'symbol' => '♌', 'start' => [7, 23], 'end' => [8, 22]],
    ['name' => 'Virgo', 'symbol' => '♍', 'start' => [8, 23], 'end' => [9, 22]],
    ['name' => 'Libra', 'symbol' => '♎', 'start' => [9, 23], 'end' => [10, 22]],
    ['name' => 'Scorpio', 'symbol' => '♏', 'start' => [10, 23], 'end' => [11, 21]],
    ['name' => 'Sagittarius', 'symbol' => '♐', 'start' => [11, 22], 'end' => [12, 21]]
  ];

  foreach ($signs as $sign) {
    $start_month = $sign['start'][0];
    $start_day = $sign['start'][1];
    $end_month = $sign['end'][0];
    $end_day = $sign['end'][1];

    if (($month == $start_month && $day >= $start_day) ||
        ($month == $end_month && $day <= $end_day) ||
        ($start_month > $end_month && ($month > $start_month || $month < $end_month))) {
      return $sign;
    }
  }

  return ['name' => 'Unknown', 'symbol' => '?'];
}

/**
 * Get Pope for a given year
 *
 * @param int $year Year
 * @return string Pope name or empty
 */
function baum_get_pope_for_year($year) {
  $year = (int) $year;

  // Only show Pope for years where we have reliable data
  if ($year < 1878 || $year > 2030) {
    return '';
  }

  $popes = [
    [1878, 1903, 'Leo XIII'],
    [1903, 1914, 'Pius X'],
    [1914, 1922, 'Benedict XV'],
    [1922, 1939, 'Pius XI'],
    [1939, 1958, 'Pius XII'],
    [1958, 1963, 'John XXIII'],
    [1963, 1978, 'Paul VI'],
    [1978, 1978, 'John Paul I'],
    [1978, 2005, 'John Paul II'],
    [2005, 2013, 'Benedict XVI'],
    [2013, 2030, 'Francis'] // Extended to 2030 for future dates
  ];

  foreach ($popes as $pope) {
    if ($year >= $pope[0] && $year <= $pope[1]) {
      return $pope[2];
    }
  }

  return '';
}

/**
 * Get US President for a given year
 *
 * @param int $year Year
 * @return string President name or empty
 */
function baum_get_potus_for_year($year) {
  $year = (int) $year;

  // Only show POTUS for years where we have reliable data
  if ($year < 1897 || $year > 2030) {
    return '';
  }

  $presidents = [
    [1897, 1901, 'William McKinley'],
    [1901, 1909, 'Theodore Roosevelt'],
    [1909, 1913, 'William Howard Taft'],
    [1913, 1921, 'Woodrow Wilson'],
    [1921, 1923, 'Warren G. Harding'],
    [1923, 1929, 'Calvin Coolidge'],
    [1929, 1933, 'Herbert Hoover'],
    [1933, 1945, 'Franklin D. Roosevelt'],
    [1945, 1953, 'Harry S. Truman'],
    [1953, 1961, 'Dwight D. Eisenhower'],
    [1961, 1963, 'John F. Kennedy'],
    [1963, 1969, 'Lyndon B. Johnson'],
    [1969, 1974, 'Richard Nixon'],
    [1974, 1977, 'Gerald Ford'],
    [1977, 1981, 'Jimmy Carter'],
    [1981, 1989, 'Ronald Reagan'],
    [1989, 1993, 'George H.W. Bush'],
    [1993, 2001, 'Bill Clinton'],
    [2001, 2009, 'George W. Bush'],
    [2009, 2017, 'Barack Obama'],
    [2017, 2021, 'Donald Trump'],
    [2021, 2030, 'Joe Biden'] // Extended to 2030 for future dates
  ];

  foreach ($presidents as $president) {
    if ($year >= $president[0] && $year <= $president[1]) {
      return $president[2];
    }
  }

  return '';
}

/**
 * Get celebrity birthdays configuration - comprehensive database
 *
 * @return array Celebrity birthdays by date
 */
function baum_get_celebrity_birthdays_config() {
  return [
    // January
    '01-01' => [['name' => 'J.D. Salinger', 'year' => 1919, 'occupation' => ['Author'], 'nationality' => 'American', 'description' => 'Author of "The Catcher in the Rye"']],
    '01-02' => [['name' => 'Isaac Asimov', 'year' => 1920, 'occupation' => ['Author', 'Scientist'], 'nationality' => 'American', 'description' => 'Science fiction author and biochemist']],
    '01-03' => [['name' => 'J.R.R. Tolkien', 'year' => 1892, 'occupation' => ['Author'], 'nationality' => 'British', 'description' => 'Author of "The Lord of the Rings"']],
    '01-04' => [['name' => 'Louis Braille', 'year' => 1809, 'occupation' => ['Inventor'], 'nationality' => 'French', 'description' => 'Inventor of the Braille reading system']],
    '01-05' => [['name' => 'Diane Keaton', 'year' => 1946, 'occupation' => ['Actress'], 'nationality' => 'American', 'description' => 'Academy Award-winning actress']],
    '01-06' => [['name' => 'Joan of Arc', 'year' => 1412, 'occupation' => ['Military Leader'], 'nationality' => 'French', 'description' => 'French military leader and saint']],
    '01-07' => [['name' => 'Nicolas Cage', 'year' => 1964, 'occupation' => ['Actor'], 'nationality' => 'American', 'description' => 'Academy Award-winning actor']],
    '01-08' => [['name' => 'Elvis Presley', 'year' => 1935, 'occupation' => ['Musician'], 'nationality' => 'American', 'description' => 'The King of Rock and Roll']],
    '01-09' => [['name' => 'Richard Nixon', 'year' => 1913, 'occupation' => ['Politician'], 'nationality' => 'American', 'description' => '37th President of the United States']],
    '01-10' => [['name' => 'Rod Stewart', 'year' => 1945, 'occupation' => ['Musician'], 'nationality' => 'British', 'description' => 'Rock and pop singer']],
    '01-11' => [['name' => 'Alexander Hamilton', 'year' => 1755, 'occupation' => ['Politician'], 'nationality' => 'American', 'description' => 'First Secretary of the Treasury']],
    '01-12' => [['name' => 'Jack London', 'year' => 1876, 'occupation' => ['Author'], 'nationality' => 'American', 'description' => 'Author of "The Call of the Wild"']],
    '01-13' => [['name' => 'Orlando Bloom', 'year' => 1977, 'occupation' => ['Actor'], 'nationality' => 'British', 'description' => 'Actor known for "Lord of the Rings"']],
    '01-14' => [['name' => 'Albert Schweitzer', 'year' => 1875, 'occupation' => ['Doctor', 'Philosopher'], 'nationality' => 'German', 'description' => 'Nobel Peace Prize winner']],
    '01-15' => [['name' => 'Martin Luther King Jr.', 'year' => 1929, 'occupation' => ['Civil Rights Leader'], 'nationality' => 'American', 'description' => 'Civil rights leader and Nobel Peace Prize winner']],
    '01-16' => [['name' => 'Aaliyah', 'year' => 1979, 'occupation' => ['Singer'], 'nationality' => 'American', 'description' => 'R&B singer and actress']],
    '01-17' => [['name' => 'Benjamin Franklin', 'year' => 1706, 'occupation' => ['Inventor', 'Politician'], 'nationality' => 'American', 'description' => 'Founding Father and inventor']],
    '01-18' => [['name' => 'Kevin Costner', 'year' => 1955, 'occupation' => ['Actor'], 'nationality' => 'American', 'description' => 'Academy Award-winning actor and director']],
    '01-19' => [['name' => 'Edgar Allan Poe', 'year' => 1809, 'occupation' => ['Author'], 'nationality' => 'American', 'description' => 'Master of horror and mystery fiction']],
    '01-20' => [['name' => 'Buzz Aldrin', 'year' => 1930, 'occupation' => ['Astronaut'], 'nationality' => 'American', 'description' => 'Second person to walk on the Moon']],
    '01-21' => [['name' => 'Geena Davis', 'year' => 1956, 'occupation' => ['Actress'], 'nationality' => 'American', 'description' => 'Academy Award-winning actress']],
    '01-22' => [['name' => 'Lord Byron', 'year' => 1788, 'occupation' => ['Poet'], 'nationality' => 'British', 'description' => 'Romantic poet']],
    '01-23' => [['name' => 'Humphrey Bogart', 'year' => 1899, 'occupation' => ['Actor'], 'nationality' => 'American', 'description' => 'Classic Hollywood actor']],
    '01-24' => [['name' => 'Neil Diamond', 'year' => 1941, 'occupation' => ['Musician'], 'nationality' => 'American', 'description' => 'Singer-songwriter']],
    '01-25' => [['name' => 'Virginia Woolf', 'year' => 1882, 'occupation' => ['Author'], 'nationality' => 'British', 'description' => 'Modernist writer']],
    '01-26' => [['name' => 'Paul Newman', 'year' => 1925, 'occupation' => ['Actor'], 'nationality' => 'American', 'description' => 'Academy Award-winning actor']],
    '01-27' => [['name' => 'Wolfgang Amadeus Mozart', 'year' => 1756, 'occupation' => ['Composer'], 'nationality' => 'Austrian', 'description' => 'Classical composer']],
    '01-28' => [['name' => 'Jackson Pollock', 'year' => 1912, 'occupation' => ['Artist'], 'nationality' => 'American', 'description' => 'Abstract expressionist painter']],
    '01-29' => [['name' => 'Oprah Winfrey', 'year' => 1954, 'occupation' => ['Media Mogul'], 'nationality' => 'American', 'description' => 'Media executive and talk show host']],
    '01-30' => [['name' => 'Franklin D. Roosevelt', 'year' => 1882, 'occupation' => ['Politician'], 'nationality' => 'American', 'description' => '32nd President of the United States']],
    '01-31' => [['name' => 'Jackie Robinson', 'year' => 1919, 'occupation' => ['Baseball Player'], 'nationality' => 'American', 'description' => 'First African American in Major League Baseball']],

    // February
    '02-01' => [['name' => 'Langston Hughes', 'year' => 1902, 'occupation' => ['Poet'], 'nationality' => 'American', 'description' => 'Harlem Renaissance poet']],
    '02-02' => [['name' => 'James Joyce', 'year' => 1882, 'occupation' => ['Author'], 'nationality' => 'Irish', 'description' => 'Author of "Ulysses"']],
    '02-03' => [['name' => 'Norman Rockwell', 'year' => 1894, 'occupation' => ['Artist'], 'nationality' => 'American', 'description' => 'American painter and illustrator']],
    '02-04' => [['name' => 'Rosa Parks', 'year' => 1913, 'occupation' => ['Civil Rights Activist'], 'nationality' => 'American', 'description' => 'Mother of the Civil Rights Movement']],
    '02-05' => [['name' => 'Hank Aaron', 'year' => 1934, 'occupation' => ['Baseball Player'], 'nationality' => 'American', 'description' => 'Baseball Hall of Famer']],
    '02-06' => [['name' => 'Ronald Reagan', 'year' => 1911, 'occupation' => ['Politician', 'Actor'], 'nationality' => 'American', 'description' => '40th President of the United States']],
    '02-07' => [['name' => 'Charles Dickens', 'year' => 1812, 'occupation' => ['Author'], 'nationality' => 'British', 'description' => 'Victorian novelist']],
    '02-08' => [['name' => 'Jules Verne', 'year' => 1828, 'occupation' => ['Author'], 'nationality' => 'French', 'description' => 'Science fiction pioneer']],
    '02-09' => [['name' => 'Alice Walker', 'year' => 1944, 'occupation' => ['Author'], 'nationality' => 'American', 'description' => 'Author of "The Color Purple"']],
    '02-10' => [['name' => 'Laura Ingalls Wilder', 'year' => 1867, 'occupation' => ['Author'], 'nationality' => 'American', 'description' => 'Author of "Little House" series']],
    '02-11' => [['name' => 'Thomas Edison', 'year' => 1847, 'occupation' => ['Inventor'], 'nationality' => 'American', 'description' => 'Inventor of the light bulb']],
    '02-12' => [['name' => 'Abraham Lincoln', 'year' => 1809, 'occupation' => ['Politician'], 'nationality' => 'American', 'description' => '16th President of the United States']],
    '02-13' => [['name' => 'Chuck Yeager', 'year' => 1923, 'occupation' => ['Pilot'], 'nationality' => 'American', 'description' => 'First person to break the sound barrier']],
    '02-14' => [['name' => 'Frederick Douglass', 'year' => 1818, 'occupation' => ['Abolitionist'], 'nationality' => 'American', 'description' => 'Abolitionist and social reformer']],
    '02-15' => [['name' => 'Galileo Galilei', 'year' => 1564, 'occupation' => ['Scientist'], 'nationality' => 'Italian', 'description' => 'Astronomer and physicist']],
    '02-16' => [['name' => 'John McEnroe', 'year' => 1959, 'occupation' => ['Tennis Player'], 'nationality' => 'American', 'description' => 'Tennis champion']],
    '02-17' => [['name' => 'Michael Jordan', 'year' => 1963, 'occupation' => ['Basketball Player'], 'nationality' => 'American', 'description' => 'Basketball legend']],
    '02-18' => [['name' => 'John Travolta', 'year' => 1954, 'occupation' => ['Actor'], 'nationality' => 'American', 'description' => 'Actor and dancer']],
    '02-19' => [['name' => 'Copernicus', 'year' => 1473, 'occupation' => ['Astronomer'], 'nationality' => 'Polish', 'description' => 'Astronomer who proposed heliocentric model']],
    '02-20' => [['name' => 'Ansel Adams', 'year' => 1902, 'occupation' => ['Photographer'], 'nationality' => 'American', 'description' => 'Landscape photographer']],
    '02-21' => [['name' => 'W.H. Auden', 'year' => 1907, 'occupation' => ['Poet'], 'nationality' => 'British', 'description' => 'Anglo-American poet']],
    '02-22' => [['name' => 'George Washington', 'year' => 1732, 'occupation' => ['Politician'], 'nationality' => 'American', 'description' => '1st President of the United States']],
    '02-23' => [['name' => 'W.E.B. Du Bois', 'year' => 1868, 'occupation' => ['Civil Rights Leader'], 'nationality' => 'American', 'description' => 'Civil rights activist and scholar']],
    '02-24' => [['name' => 'Steve Jobs', 'year' => 1955, 'occupation' => ['Entrepreneur'], 'nationality' => 'American', 'description' => 'Co-founder of Apple Inc.']],
    '02-25' => [['name' => 'George Harrison', 'year' => 1943, 'occupation' => ['Musician'], 'nationality' => 'British', 'description' => 'The Beatles guitarist']],
    '02-26' => [['name' => 'Johnny Cash', 'year' => 1932, 'occupation' => ['Musician'], 'nationality' => 'American', 'description' => 'Country music legend']],
    '02-27' => [['name' => 'John Steinbeck', 'year' => 1902, 'occupation' => ['Author'], 'nationality' => 'American', 'description' => 'Nobel Prize-winning author']],
    '02-28' => [['name' => 'Mario Andretti', 'year' => 1940, 'occupation' => ['Race Car Driver'], 'nationality' => 'American', 'description' => 'Formula One champion']],
    '02-29' => [['name' => 'Gioachino Rossini', 'year' => 1792, 'occupation' => ['Composer'], 'nationality' => 'Italian', 'description' => 'Opera composer']],

    // March - July (abbreviated for space, but covering key dates)
    '03-01' => [['name' => 'Justin Bieber', 'year' => 1994, 'occupation' => ['Musician'], 'nationality' => 'Canadian', 'description' => 'Pop singer']],
    '03-15' => [['name' => 'Julius Caesar', 'year' => -100, 'occupation' => ['Emperor'], 'nationality' => 'Roman', 'description' => 'Roman general and statesman']],
    '03-31' => [['name' => 'René Descartes', 'year' => 1596, 'occupation' => ['Philosopher'], 'nationality' => 'French', 'description' => 'Philosopher and mathematician']],

    '04-01' => [['name' => 'Sergei Rachmaninoff', 'year' => 1873, 'occupation' => ['Composer'], 'nationality' => 'Russian', 'description' => 'Classical composer and pianist']],
    '04-15' => [['name' => 'Leonardo da Vinci', 'year' => 1452, 'occupation' => ['Artist', 'Inventor'], 'nationality' => 'Italian', 'description' => 'Renaissance polymath']],
    '04-23' => [['name' => 'William Shakespeare', 'year' => 1564, 'occupation' => ['Playwright'], 'nationality' => 'English', 'description' => 'Greatest playwright in English literature']],

    '05-01' => [['name' => 'Kate Smith', 'year' => 1907, 'occupation' => ['Singer'], 'nationality' => 'American', 'description' => 'Singer known for "God Bless America"']],
    '05-04' => [['name' => 'Audrey Hepburn', 'year' => 1929, 'occupation' => ['Actress'], 'nationality' => 'British', 'description' => 'Iconic actress and humanitarian']],
    '05-25' => [['name' => 'Ralph Waldo Emerson', 'year' => 1803, 'occupation' => ['Philosopher'], 'nationality' => 'American', 'description' => 'Transcendentalist philosopher']],

    '06-01' => [['name' => 'Marilyn Monroe', 'year' => 1926, 'occupation' => ['Actress'], 'nationality' => 'American', 'description' => 'Iconic actress and model']],
    '06-11' => [['name' => 'Jacques Cousteau', 'year' => 1910, 'occupation' => ['Explorer'], 'nationality' => 'French', 'description' => 'Marine explorer and filmmaker']],
    '06-20' => [['name' => 'Lionel Richie', 'year' => 1949, 'occupation' => ['Musician'], 'nationality' => 'American', 'description' => 'Singer and songwriter']],

    '07-04' => [['name' => 'Nathaniel Hawthorne', 'year' => 1804, 'occupation' => ['Author'], 'nationality' => 'American', 'description' => 'Author of "The Scarlet Letter"']],
    '07-20' => [['name' => 'Sir Edmund Hillary', 'year' => 1919, 'occupation' => ['Explorer'], 'nationality' => 'New Zealand', 'description' => 'First to summit Mount Everest']],
    '07-31' => [['name' => 'J.K. Rowling', 'year' => 1965, 'occupation' => ['Author'], 'nationality' => 'British', 'description' => 'Author of Harry Potter series']],

    // August - December (key dates)
    '08-01' => [['name' => 'Herman Melville', 'year' => 1819, 'occupation' => ['Author'], 'nationality' => 'American', 'description' => 'Author of "Moby Dick"']],
    '08-15' => [['name' => 'Napoleon Bonaparte', 'year' => 1769, 'occupation' => ['Emperor'], 'nationality' => 'French', 'description' => 'French military leader and emperor']],
    '08-31' => [['name' => 'Maria Montessori', 'year' => 1870, 'occupation' => ['Educator'], 'nationality' => 'Italian', 'description' => 'Educational innovator']],

    '09-01' => [['name' => 'Edgar Rice Burroughs', 'year' => 1875, 'occupation' => ['Author'], 'nationality' => 'American', 'description' => 'Creator of Tarzan']],
    '09-15' => [['name' => 'Agatha Christie', 'year' => 1890, 'occupation' => ['Author'], 'nationality' => 'British', 'description' => 'Mystery novelist']],
    '09-30' => [['name' => 'Truman Capote', 'year' => 1924, 'occupation' => ['Author'], 'nationality' => 'American', 'description' => 'Author of "In Cold Blood"']],

    '10-01' => [['name' => 'Jimmy Carter', 'year' => 1924, 'occupation' => ['Politician'], 'nationality' => 'American', 'description' => '39th President of the United States']],
    '10-15' => [['name' => 'Friedrich Nietzsche', 'year' => 1844, 'occupation' => ['Philosopher'], 'nationality' => 'German', 'description' => 'Influential philosopher']],
    '10-31' => [['name' => 'John Keats', 'year' => 1795, 'occupation' => ['Poet'], 'nationality' => 'British', 'description' => 'Romantic poet']],

    '11-01' => [['name' => 'Stephen Crane', 'year' => 1871, 'occupation' => ['Author'], 'nationality' => 'American', 'description' => 'Author of "The Red Badge of Courage"']],
    '11-15' => [['name' => 'Georgia O\'Keeffe', 'year' => 1887, 'occupation' => ['Artist'], 'nationality' => 'American', 'description' => 'American modernist painter']],
    '11-30' => [['name' => 'Mark Twain', 'year' => 1835, 'occupation' => ['Author'], 'nationality' => 'American', 'description' => 'Author of "The Adventures of Tom Sawyer"']],

    '12-01' => [['name' => 'Woody Allen', 'year' => 1935, 'occupation' => ['Director'], 'nationality' => 'American', 'description' => 'Filmmaker and comedian']],
    '12-15' => [['name' => 'Gustave Eiffel', 'year' => 1832, 'occupation' => ['Engineer'], 'nationality' => 'French', 'description' => 'Designer of the Eiffel Tower']],
    '12-25' => [['name' => 'Isaac Newton', 'year' => 1642, 'occupation' => ['Scientist'], 'nationality' => 'English', 'description' => 'Physicist and mathematician']],
    '12-31' => [['name' => 'Henri Matisse', 'year' => 1869, 'occupation' => ['Artist'], 'nationality' => 'French', 'description' => 'Fauvist painter']]
  ];
}

/**
 * Generic helper to render time capsule items with consistent styling
 *
 * @param array $items Array of items to display
 * @param string $type Type of items (for CSS classes)
 * @param array $options Display options
 * @return string HTML output
 */
function baum_render_time_capsule_items($items, $type, $options = []) {
  if (empty($items)) {
    return '';
  }

  $defaults = [
    'show_year' => true,
    'show_more_limit' => 3,
    'container_class' => $type . '-container',
    'item_class' => $type . '-item',
    'year_class' => $type . '-year',
    'content_class' => $type . '-content'
  ];

  $options = array_merge($defaults, $options);

  ob_start();
  ?>
  <div class="<?php echo esc_attr($options['container_class']); ?>">
    <?php
    $item_count = 0;
    foreach ($items as $item):
      $item_count++;
      $hidden_class = $item_count > $options['show_more_limit'] ? 'hidden' : '';
    ?>
    <div class="<?php echo esc_attr($options['item_class'] . ' ' . $hidden_class); ?>">
      <?php if ($options['show_year'] && !empty($item['year'])): ?>
      <span class="<?php echo esc_attr($options['year_class']); ?>"><?php echo esc_html($item['year']); ?></span>
      <?php endif; ?>
      <div class="<?php echo esc_attr($options['content_class']); ?>">
        <?php echo wp_kses_post($item['html'] ?? $item['text'] ?? ''); ?>
      </div>
    </div>
    <?php endforeach; ?>

    <?php if (count($items) > $options['show_more_limit']): ?>
    <button class="show-more-btn" data-target="<?php echo esc_attr($type); ?>">
      Show More <?php echo esc_html(ucfirst($type)); ?>
    </button>
    <?php endif; ?>
  </div>
  <?php
  return ob_get_clean();
}

add_shortcode("time_capsule", "baum_time_capsule_shortcode");

/**
 * Time Capsule Test Shortcode
 *
 * Usage: [time_capsule_test] or [time_capsule_test date="07-20"]
 */
function baum_time_capsule_test_shortcode($atts) {
  $atts = shortcode_atts([
    'date' => date('Y-m-d')
  ], $atts);

  $date = $atts['date'];
  if (strlen($date) === 5 && strpos($date, '-') === 2) {
    // Convert MM-DD to current year
    $date = date('Y') . '-' . $date;
  }

  ob_start();
  ?>
  <div class="time-capsule-test" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h3>Time Capsule Test - <?php echo esc_html($date); ?></h3>

    <?php
    if (function_exists('baum_get_comprehensive_history')) {
      $data = baum_get_comprehensive_history($date);

      echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";

      // Events
      if (!empty($data['events'])) {
        echo "<div style='background: white; padding: 15px; border-radius: 6px; border: 1px solid #e9ecef;'>";
        echo "<h4 style='margin: 0 0 10px 0; color: #495057;'>📅 Events (" . count($data['events']) . ")</h4>";
        foreach (array_slice($data['events'], 0, 3) as $event) {
          echo "<p style='margin: 5px 0; font-size: 13px;'><strong>" . esc_html($event['year']) . ":</strong> " . wp_kses_post(substr($event['html'] ?? $event['text'], 0, 100)) . "...</p>";
        }
        echo "</div>";
      }

      // Celebrities
      if (!empty($data['celebrities'])) {
        echo "<div style='background: white; padding: 15px; border-radius: 6px; border: 1px solid #e9ecef;'>";
        echo "<h4 style='margin: 0 0 10px 0; color: #495057;'>⭐ Celebrities (" . count($data['celebrities']) . ")</h4>";
        foreach (array_slice($data['celebrities'], 0, 3) as $celeb) {
          echo "<p style='margin: 5px 0; font-size: 13px;'><strong>" . esc_html($celeb['name']) . "</strong>";
          if (!empty($celeb['occupation'])) {
            echo " - " . esc_html(implode(', ', (array)$celeb['occupation']));
          }
          echo "</p>";
        }
        echo "</div>";
      }

      // Performance info
      if (isset($data['performance'])) {
        echo "<p style='font-size: 12px; color: #6c757d; margin-top: 15px;'>";
        echo "⚡ Loaded in " . number_format($data['performance']['total_time'], 3) . "s";
        echo " | Memory: " . size_format($data['performance']['memory_usage']);
        echo "</p>";
      }

      echo "</div>";

    } else {
      echo "<p style='color: #dc3545;'>❌ Time capsule functions not available</p>";
    }
    ?>

    <p style="margin-top: 15px; font-size: 12px; color: #6c757d;">
      <strong>Usage:</strong> [time_capsule_test] or [time_capsule_test date="07-20"]
    </p>
  </div>
  <?php
  return ob_get_clean();
}
add_shortcode('time_capsule_test', 'baum_time_capsule_test_shortcode');

/**
 * Time Capsule Configuration Constants
 */
define('BAUM_TIME_CAPSULE_API_NINJAS_KEY', 'POSYz0dZXmSaJFy0trshow==liKp5hLYjdJgsED8');
define('BAUM_TIME_CAPSULE_DEFAULT_LAT', 40.7128); // NYC latitude
define('BAUM_TIME_CAPSULE_DEFAULT_LON', -74.0060); // NYC longitude
define('BAUM_TIME_CAPSULE_CACHE_TIME', 3600); // 1 hour cache
define('BAUM_TIME_CAPSULE_API_TIMEOUT', 10); // 10 second timeout

/**
 * Generic API request helper for Time Capsule
 *
 * @param string $url API endpoint URL
 * @param array $headers Optional headers
 * @param int $timeout Optional timeout
 * @return array|false API response data or false on error
 */
function baum_time_capsule_api_request($url, $headers = [], $timeout = null) {
  $timeout = $timeout ?? BAUM_TIME_CAPSULE_API_TIMEOUT;

  $default_headers = [
    'User-Agent' => 'BaumPress Time Capsule/1.0'
  ];

  $headers = array_merge($default_headers, $headers);

  $response = wp_remote_get($url, [
    'timeout' => $timeout,
    'headers' => $headers
  ]);

  if (is_wp_error($response)) {
    error_log('Time Capsule API Error: ' . $response->get_error_message() . ' for URL: ' . $url);
    return false;
  }

  $body = wp_remote_retrieve_body($response);
  $data = json_decode($body, true);

  if (!$data) {
    error_log('Time Capsule API: Invalid JSON response from ' . $url);
    return false;
  }

  return $data;
}

/**
 * API Ninjas request helper
 *
 * @param string $endpoint API endpoint (without base URL)
 * @param array $params Query parameters
 * @return array|false API response data or false on error
 */
function baum_api_ninjas_request($endpoint, $params = []) {
  // Use the API key we know works
  $api_key = 'POSYz0dZXmSaJFy0trshow==liKp5hLYjdJgsED8';

  $base_url = 'https://api.api-ninjas.com/v1/';
  $url = $base_url . $endpoint;

  if (!empty($params)) {
    $url .= '?' . http_build_query($params);
  }

  $response = wp_remote_get($url, [
    'timeout' => 10,
    'headers' => [
      'X-Api-Key' => $api_key,
      'User-Agent' => 'BaumPress Time Capsule/1.0'
    ]
  ]);

  if (is_wp_error($response)) {
    error_log('API Ninjas request failed: ' . $response->get_error_message());
    return false;
  }

  $body = wp_remote_retrieve_body($response);
  $data = json_decode($body, true);

  if (json_last_error() !== JSON_ERROR_NONE) {
    error_log('API Ninjas JSON decode error: ' . json_last_error_msg());
    return false;
  }

  return $data;
}

/**
 * Time Capsule URL Rewrite Rules
 *
 * Handles custom URL structures for the time capsule page
 */
function baum_time_capsule_rewrite_rules() {
  // Get the time capsule page ID
  $time_capsule_page = get_page_by_path('time-capsule');
  if (!$time_capsule_page) {
    return; // No time capsule page found
  }

  $page_slug = $time_capsule_page->post_name;

  // Add rewrite rules for various date formats
  add_rewrite_rule(
    '^' . $page_slug . '/([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/?$',
    'index.php?page_id=' . $time_capsule_page->ID . '&tc_year=$matches[1]&tc_month=$matches[2]&tc_day=$matches[3]',
    'top'
  );

  add_rewrite_rule(
    '^' . $page_slug . '/([0-9]{4})/([0-9]{1,2})/?$',
    'index.php?page_id=' . $time_capsule_page->ID . '&tc_year=$matches[1]&tc_month=$matches[2]',
    'top'
  );

  add_rewrite_rule(
    '^' . $page_slug . '/([0-9]{4})/?$',
    'index.php?page_id=' . $time_capsule_page->ID . '&tc_year=$matches[1]',
    'top'
  );

  add_rewrite_rule(
    '^' . $page_slug . '/([0-9]{1,2})/([0-9]{1,2})/?$',
    'index.php?page_id=' . $time_capsule_page->ID . '&tc_month=$matches[1]&tc_day=$matches[2]',
    'top'
  );

  add_rewrite_rule(
    '^' . $page_slug . '/([0-9]{1,2})-([0-9]{1,2})/?$',
    'index.php?page_id=' . $time_capsule_page->ID . '&tc_month=$matches[1]&tc_day=$matches[2]',
    'top'
  );

  add_rewrite_rule(
    '^' . $page_slug . '/(monday|tuesday|wednesday|thursday|friday|saturday|sunday)/?$',
    'index.php?page_id=' . $time_capsule_page->ID . '&tc_weekday=$matches[1]',
    'top'
  );

  add_rewrite_rule(
    '^' . $page_slug . '/(today|random)/?$',
    'index.php?page_id=' . $time_capsule_page->ID . '&tc_special=$matches[1]',
    'top'
  );
}
add_action('init', 'baum_time_capsule_rewrite_rules');

/**
 * Add query vars for time capsule
 */
function baum_time_capsule_query_vars($vars) {
  $vars[] = 'tc_year';
  $vars[] = 'tc_month';
  $vars[] = 'tc_day';
  $vars[] = 'tc_weekday';
  $vars[] = 'tc_special';
  return $vars;
}
add_filter('query_vars', 'baum_time_capsule_query_vars');

/**
 * Flush rewrite rules when theme is activated
 */
function baum_time_capsule_flush_rules() {
  baum_time_capsule_rewrite_rules();
  flush_rewrite_rules();
}
add_action('after_switch_theme', 'baum_time_capsule_flush_rules');

/**
 * Add admin notice and button to flush rewrite rules
 */
function baum_time_capsule_admin_notice() {
  if (isset($_GET['flush_time_capsule_rules'])) {
    baum_time_capsule_rewrite_rules();
    flush_rewrite_rules();
    echo '<div class="notice notice-success is-dismissible"><p>Time Capsule rewrite rules have been flushed!</p></div>';
  }

  if (current_user_can('manage_options')) {
    echo '<div class="notice notice-info"><p>';
    echo 'Time Capsule URL routing: ';
    echo '<a href="' . admin_url('?flush_time_capsule_rules=1') . '" class="button button-small">Flush Rewrite Rules</a>';
    echo ' (Click this if time capsule date URLs are not working)';
    echo '</p></div>';
  }
}
// TEMPORARILY DISABLED - TESTING FOR MEMORY ISSUES
// add_action('admin_notices', 'baum_time_capsule_admin_notice');

/////////////////////////////////////
// Baum Featured Images System
/////////////////////////////////////

/**
 * Include the new Baum Featured Images system
 *
 * This replaces WordPress's default featured image/thumbnail functionality
 * with a comprehensive multiple featured images system.
 */
require_once get_template_directory() . '/functions-featured-images.php';

/**
 * Include the Image Grid Concatenation system
 *
 * Functions to combine multiple images into standard grid layouts
 * for social media posts and baum card thumbnails.
 */
require_once get_template_directory() . '/functions-image-grid.php';



/**
 * Build a CSS output from a color array with an optional rotation for gradients.
 *
 * @param mixed         $colors   Either a string or an array of CSS variable values.
 * @param string        $context  Either 'background' or 'color'. For 'color', extra properties are added.
 * @param int|float|null $rotation The rotation angle in degrees. Defaults to 90 if not provided or invalid.
 * @return string                 The CSS output.
 */
function get_css_output($colors, $context = 'background', $rotation = 90) {
    // Ensure $colors is an array.
    if (!is_array($colors)) {
        $colors = $colors ? [$colors] : [];
    }

    // Fallback if no color is selected.
    if (empty($colors)) {
        $colors[] = 'var(--color-white)';
    }

    // Validate the rotation value; if not numeric, default to 90.
    if (!is_numeric($rotation)) {
        $rotation = 90;
    }

    // If only one color is chosen, output a simple CSS rule.
    if (count($colors) === 1) {
        $color = $colors[0];
        return ($context === 'background') ? "background: {$color};" : "color: {$color};";
    }

    // If multiple colors are chosen, build a linear gradient using the rotation.
    $gradient_colors = implode(', ', $colors);
    $gradient = "linear-gradient({$rotation}deg, {$gradient_colors})";

    // For background, output the gradient as a background-image.
    if ($context === 'background') {
        return "background-image: {$gradient};";
    }

    // For text, use the gradient and clip it to the text.
    if ($context === 'color') {
        return "background-image: {$gradient}; -webkit-background-clip: text; color: transparent;";
    }
}

/**
 * Include post layout functions
 *
 * Load the helper functions for the new post layout system
 */
require_once get_template_directory() . '/inc/post-layout-functions.php';

/**
 * Include hierarchical byline system
 *
 * Load the hierarchical byline system with category/topic > global fallback
 */
require_once get_template_directory() . '/inc/hierarchical-byline-system.php';

/**
 * Include related content system
 *
 * Load the related content system for linking various content types to posts
 */
require_once get_template_directory() . '/inc/related-content-system.php';