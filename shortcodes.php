<?php

/** 
 * [baum_bookmark] — Baum Bookmark shortcode 
 * 
 */ 

 function baum_bookmark_shortcode ($atts, $content, $shortcode_tag) { 
  $icon = get_theme_mod('baum_bookmark_icon', 'star'); 
  if ($icon === 'bookmark') { 
    $title = 'Bookmark'; 
    $r_title = 'Bookmarked'; 
  } else if ($icon === 'gem') { 
    $title = 'Gem this'; 
    $r_title = 'Remove Gem'; 
  } else if ($icon === 'heart') { 
    $title = 'Love this'; 
    $r_title = 'Loved'; 
  } else if ($icon === 'fire') { 
    $title = 'This is Hot!'; 
    $r_title = 'Hot'; 
  } else if ($icon === 'star') { 
    $title = 'Star'; 
    $r_title = 'Starred'; 
  } else if ($icon === 'thumbs-up') { 
    $title = 'Like'; 
    $r_title = 'Liked'; 
  } 

  if ($atts && isset($atts['css'])) $css = $atts['css']; 
  else $css = ''; 

  if ($atts && isset($atts['id']) && isset($atts['button'])) { 
    $html = '<button class="baum-bookmark baum-unbookmarked ' . $css 
      // . '" title="' . $title 
      . '" data-baum-id="' . $atts['id'] 

      . '" data-title="' . $title 
      . '" data-reverse-title="' . $r_title 

      . '"><i class="far fa-' . $icon . '"></i> &nbsp; ' 
      . ' <span>' . $title . '</span>' 
      . '</button>'; 
  } else if ($atts && isset($atts['id'])) { 
    $html = '<a href="#" class="baum-bookmark baum-unbookmarked ' . $css 
      // . '" title="' . $title 
      . '" data-baum-id="' . $atts['id'] 
      . '" data-title="' . $title 
      . '" data-reverse-title="' . $r_title 
      . '"><i class="far fa-' . $icon . '"></i>' 
      . '</a>'; 
  } else { 
    $html = '<i class="fas fa-question" title="ID attribute not set"></i>'; 
  } 
  return $html; 
} 

add_shortcode('baum_bookmark', 'baum_bookmark_shortcode'); 

/** 
 * [baum_bookmark_icon] — Get the Baum Bookmark icon as text/html 
 * 
 */ 

 function baum_bookmark_icon_shortcode ($atts, $content, $shortcode_tag) { 
  $icon = get_theme_mod('baum_bookmark_icon', 'star'); 
  if ($icon === 'bookmark') { 
    $title = 'Bookmark'; 
    $r_title = 'Bookmarked'; 
  } else if ($icon === 'gem') { 
    $title = 'Gem this'; 
    $r_title = 'Remove Gem'; 
  } else if ($icon === 'heart') { 
    $title = 'Love this'; 
    $r_title = 'Loved'; 
  } else if ($icon === 'fire') { 
    $title = 'This is Hot!'; 
    $r_title = 'Hot'; 
  } else if ($icon === 'star') { 
    $title = 'Star'; 
    $r_title = 'Starred'; 
  } else if ($icon === 'thumbs-up') { 
    $title = 'Like'; 
    $r_title = 'Liked'; 
  } 
  return '<span style="margin:0;padding:0;display:inline-block;">' 
    . '<span class="title">' . $title . '</span>' 
    . '<span class="reverse" style="display:none;">' . $r_title . '</span>' 
    . '</span>'; 
} 

add_shortcode('baum_bookmark_icon', 'baum_bookmark_icon_shortcode'); 

/** 
 * [baum_bookmark_icon_text] — Get the Baum Bookmark icon as text 
 * 
 */ 

function baum_bookmark_icon_text_shortcode ($atts, $content, $shortcode_tag) { 
  return get_theme_mod('baum_bookmark_icon', 'star');  
} 

add_shortcode('baum_bookmark_icon_text', 'baum_bookmark_icon_text_shortcode'); 
