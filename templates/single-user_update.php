<?php
/**
 * Single User Update
 *
 * Minimal single template that reuses the DRY update card.
 *
 * @package BaumGroups
 */

if (!defined('ABSPATH')) { exit; }

get_header();

the_post();
$activity_post = get_post();
$show_context = true; $show_actions = true; $compact_mode = false;
$template_path = BAUM_GROUPS_PLUGIN_DIR . 'parts/update-card.php';
?>
<main class="site-main container">
  <div class="baum-post-list">
    <?php if (file_exists($template_path)) { include $template_path; } else { the_content(); } ?>
  </div>
  <?php if (comments_open()) { comments_template(); } ?>
</main>

<?php get_footer(); ?>

