<?php
/**
 * BaumPress Notifications System
 *
 * Handles notifications, activity, and user interactions.
 * Separated from alerts (which are now in baum-alerts plugin).
 *
 * @package BaumPress
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Display license types for file info
 *
 * @since 1.0.0
 */
function baumpress_display_license_types() {
  $licenses = [
    'All Rights Reserved',
    'Editorial Use Only',
    'Creative Commons (CC)',
    'CC BY - Attribution',
    'CC BY-SA - Attribution + Share Alike',
    'CC BY-ND - Attribution + No Derivatives',
    'CC BY-NC - Attribution + Non-Commercial',
    'Public Domain (CC0)',
    'Royalty-Free',
    'Rights-Managed',
    'Personal Use Only',
    'Exclusive License',
    'Work-for-Hire',
  ];

  echo '<ul class="baum-file-info" style="list-style-type:none;">';
  foreach ($licenses as $license) {
    echo '<li>' . esc_html($license) . '</li>';
  }
  echo '</ul>';
}

//
//
//

function baum_award_xp($user_id, $points = 10, $action = 'follow') {
  if (function_exists('gamipress_add_points_to_user')) {
      gamipress_add_points_to_user($user_id, $points, 'gamipress_points_type');
  }
}

//
//
//

function baum_deduct_xp($user_id, $points = 10, $action = 'unfollow') {
  if (function_exists('gamipress_deduct_points_from_user')) {
      gamipress_deduct_points_from_user($user_id, $points, 'gamipress_points_type');
  }
}

/**
 * ===== CUSTOM POST TYPES =====
 */

/**
 * Register custom post types for notifications and activity
 *
 * @since 1.0.0
 */
/*
function baum_register_notification_post_types() {
  // Register Notifications CPT
  register_post_type('baum_notifications', array(
    'labels' => array(
      'name' => __('Notifications', 'baum'),
      'singular_name' => __('Notification', 'baum'),
      'menu_name' => __('Notifications', 'baum'),
      'add_new' => __('Add New', 'baum'),
      'add_new_item' => __('Add New Notification', 'baum'),
      'edit_item' => __('Edit Notification', 'baum'),
      'new_item' => __('New Notification', 'baum'),
      'view_item' => __('View Notification', 'baum'),
      'search_items' => __('Search Notifications', 'baum'),
      'not_found' => __('No notifications found', 'baum'),
      'not_found_in_trash' => __('No notifications found in trash', 'baum'),
    ),
    'public' => false,
    'publicly_queryable' => false,
    'show_ui' => true,
    'show_in_menu' => true,
    'show_in_admin_bar' => false,
    'show_in_nav_menus' => false,
    'can_export' => true,
    'has_archive' => false,
    'exclude_from_search' => true,
    'capability_type' => 'post',
    'supports' => array('title', 'editor', 'custom-fields'),
    'menu_icon' => 'dashicons-bell',
    'menu_position' => 25,
    'rewrite' => false
  ));

  // Register Activity CPT
  register_post_type('baum_activity', array(
    'labels' => array(
      'name' => __('Activity', 'baum'),
      'singular_name' => __('Activity', 'baum'),
      'menu_name' => __('Activity', 'baum'),
      'add_new' => __('Add New', 'baum'),
      'add_new_item' => __('Add New Activity', 'baum'),
      'edit_item' => __('Edit Activity', 'baum'),
      'new_item' => __('New Activity', 'baum'),
      'view_item' => __('View Activity', 'baum'),
      'search_items' => __('Search Activity', 'baum'),
      'not_found' => __('No activity found', 'baum'),
      'not_found_in_trash' => __('No activity found in trash', 'baum'),
    ),
    'public' => false,
    'publicly_queryable' => false,
    'show_ui' => true,
    'show_in_menu' => true,
    'show_in_admin_bar' => false,
    'show_in_nav_menus' => false,
    'can_export' => true,
    'has_archive' => false,
    'exclude_from_search' => true,
    'capability_type' => 'post',
    'supports' => array('title', 'editor', 'custom-fields'),
    'menu_icon' => 'dashicons-admin-users',
    'menu_position' => 26,
    'rewrite' => false
  ));
}

add_action('init', 'baum_register_notification_post_types');
*/

/**
 * ===== ALERTS SYSTEM MOVED =====
 *
 * Alert functions have been moved to the baum-alerts plugin.
 * Use these functions instead:
 *
 * baum_alert($type, $message, $icon, $options)
 * baum_alert_success($message, $icon, $options)
 * baum_alert_error($message, $icon, $options)
 * baum_alert_warning($message, $icon, $options)
 * baum_alert_info($message, $icon, $options)
 *
 * Legacy support: baum_notify($icon, $message) still works
 */

/**
 * ===== NOTIFICATIONS SYSTEM =====
 *
 * Handles actionable notifications that appear in the bell dropdown.
 * Uses the baum_notifications CPT from the plugin.
 */

/**
 * Create a notification for a user
 *
 * @param string $type Notification type (comment, follow, mention, like, etc.)
 * @param string $message Notification message
 * @param int $target_user_id User who should receive the notification
 * @param int $source_user_id User who triggered the notification (optional)
 * @param string $action_url URL to take action (optional)
 * @param array $meta Additional metadata (optional)
 * @return int|false Post ID on success, false on failure
 * @since 1.0.0
 */
function baum_create_notification($type, $message, $target_user_id, $source_user_id = null, $action_url = '', $meta = array()) {
  // Validate required parameters
  if (empty($type) || empty($message) || empty($target_user_id)) {
    return false;
  }

  // Set default source user
  if (!$source_user_id) {
    $source_user_id = get_current_user_id();
  }

  // Don't create notifications for actions on your own content
  if ($source_user_id === $target_user_id) {
    return false;
  }

  // Prepare notification data
  $notification_data = array(
    'post_type' => 'baum_notifications',
    'post_status' => 'publish',
    'post_title' => sanitize_text_field($type . '_' . time()),
    'post_content' => wp_kses_post($message),
    'meta_input' => array(
      'notification_type' => sanitize_text_field($type),
      'target_user_id' => intval($target_user_id),
      'source_user_id' => intval($source_user_id),
      'action_url' => esc_url_raw($action_url),
      'read_status' => 'unread',
      'dismissed' => 'no',
      'created_at' => current_time('mysql'),
    )
  );

  // Add any additional metadata
  if (!empty($meta) && is_array($meta)) {
    foreach ($meta as $key => $value) {
      $notification_data['meta_input']['meta_' . sanitize_key($key)] = sanitize_text_field($value);
    }
  }

  // Insert the notification
  $notification_id = wp_insert_post($notification_data);

  if ($notification_id && !is_wp_error($notification_id)) {
    // Log the notification creation
    error_log("📬 Created notification: {$type} for user {$target_user_id} from user {$source_user_id}");
    return $notification_id;
  }

  return false;
}

/**
 * Get notifications for a user
 *
 * @param int $user_id User ID (default: current user)
 * @param array $args Query arguments
 * @return array Array of notification objects
 * @since 1.0.0
 */
function baum_get_notifications($user_id = null, $args = array()) {
  if (!$user_id) {
    $user_id = get_current_user_id();
  }

  if (!$user_id) {
    return array();
  }

  // Default query arguments
  $default_args = array(
    'post_type' => 'baum_notifications',
    'post_status' => 'publish',
    'posts_per_page' => 10,
    'orderby' => 'date',
    'order' => 'DESC',
    'meta_query' => array(
      array(
        'key' => 'target_user_id',
        'value' => $user_id,
        'compare' => '='
      ),
      array(
        'key' => 'dismissed',
        'value' => 'no',
        'compare' => '='
      )
    )
  );

  // Merge with provided arguments
  $query_args = wp_parse_args($args, $default_args);

  $query = new WP_Query($query_args);
  $notifications = array();

  if ($query->have_posts()) {
    while ($query->have_posts()) {
      $query->the_post();
      $post_id = get_the_ID();

      $notifications[] = array(
        'id' => $post_id,
        'type' => get_post_meta($post_id, 'notification_type', true),
        'message' => get_the_content(),
        'target_user_id' => get_post_meta($post_id, 'target_user_id', true),
        'source_user_id' => get_post_meta($post_id, 'source_user_id', true),
        'action_url' => get_post_meta($post_id, 'action_url', true),
        'read_status' => get_post_meta($post_id, 'read_status', true),
        'dismissed' => get_post_meta($post_id, 'dismissed', true),
        'created_at' => get_post_meta($post_id, 'created_at', true),
        'date' => get_the_date('c'),
        'time_ago' => human_time_diff(get_the_time('U'), current_time('timestamp')) . ' ago'
      );
    }
  }

  wp_reset_postdata();
  return $notifications;
}

/**
 * Get unread notification count for a user
 *
 * @param int $user_id User ID (default: current user)
 * @return int Number of unread notifications
 * @since 1.0.0
 */
function baum_get_unread_notification_count($user_id = null) {
  if (!$user_id) {
    $user_id = get_current_user_id();
  }

  if (!$user_id) {
    return 0;
  }

  $args = array(
    'post_type' => 'baum_notifications',
    'post_status' => 'publish',
    'posts_per_page' => -1,
    'fields' => 'ids',
    'meta_query' => array(
      array(
        'key' => 'target_user_id',
        'value' => $user_id,
        'compare' => '='
      ),
      array(
        'key' => 'read_status',
        'value' => 'unread',
        'compare' => '='
      ),
      array(
        'key' => 'dismissed',
        'value' => 'no',
        'compare' => '='
      )
    )
  );

  $query = new WP_Query($args);
  return $query->found_posts;
}

/**
 * Mark notification as read
 *
 * @param int $notification_id Notification post ID
 * @param int $user_id User ID (for security check)
 * @return bool Success
 * @since 1.0.0
 */
function baum_mark_notification_read($notification_id, $user_id = null) {
  if (!$user_id) {
    $user_id = get_current_user_id();
  }

  // Security check: ensure user can only mark their own notifications as read
  $target_user = get_post_meta($notification_id, 'target_user_id', true);
  if ($target_user != $user_id) {
    return false;
  }

  return update_post_meta($notification_id, 'read_status', 'read');
}

/**
 * Dismiss notification
 *
 * @param int $notification_id Notification post ID
 * @param int $user_id User ID (for security check)
 * @return bool Success
 * @since 1.0.0
 */
function baum_dismiss_notification($notification_id, $user_id = null) {
  if (!$user_id) {
    $user_id = get_current_user_id();
  }

  // Security check: ensure user can only dismiss their own notifications
  $target_user = get_post_meta($notification_id, 'target_user_id', true);
  if ($target_user != $user_id) {
    return false;
  }

  return update_post_meta($notification_id, 'dismissed', 'yes');
}

/**
 * ===== AJAX HANDLERS =====
 */

/**
 * AJAX handler to load notifications
 *
 * @since 1.0.0
 */
function baum_ajax_load_notifications() {
  // Verify nonce
  if (!wp_verify_nonce($_POST['nonce'], 'baum_notifications_nonce')) {
    wp_send_json_error('Security check failed');
  }

  if (!is_user_logged_in()) {
    wp_send_json_error('User not logged in');
  }

  $page = intval($_POST['page']) ?: 1;
  $per_page = intval($_POST['per_page']) ?: 10;
  $user_id = get_current_user_id();

  // Get notifications
  $args = array(
    'posts_per_page' => $per_page,
    'paged' => $page
  );

  $notifications = baum_get_notifications($user_id, $args);

  // Format notifications for frontend
  $formatted_notifications = array();
  foreach ($notifications as $notification) {
    $formatted_notifications[] = array(
      'id' => $notification['id'],
      'text' => $notification['message'],
      'time' => $notification['time_ago'],
      'icon' => baum_get_notification_icon($notification['type']),
      'icon_color' => baum_get_notification_color($notification['type']),
      'link' => $notification['action_url'],
      'read' => $notification['read_status'] === 'read'
    );
  }

  // Check if there are more notifications
  $total_notifications = baum_get_total_notification_count($user_id);
  $has_more = ($page * $per_page) < $total_notifications;

  wp_send_json_success(array(
    'notifications' => $formatted_notifications,
    'has_more' => $has_more,
    'total' => $total_notifications,
    'page' => $page
  ));
}

add_action('wp_ajax_baum_load_notifications', 'baum_ajax_load_notifications');
add_action('wp_ajax_nopriv_baum_load_notifications', 'baum_ajax_load_notifications');

/**
 * AJAX handler to mark notification as read
 *
 * @since 1.0.0
 */
function baum_ajax_mark_notification_read() {
  // Verify nonce
  if (!wp_verify_nonce($_POST['nonce'], 'baum_notifications_nonce')) {
    wp_send_json_error('Security check failed');
  }

  if (!is_user_logged_in()) {
    wp_send_json_error('User not logged in');
  }

  $notification_id = intval($_POST['notification_id']);
  $user_id = get_current_user_id();

  if (baum_mark_notification_read($notification_id, $user_id)) {
    wp_send_json_success('Notification marked as read');
  } else {
    wp_send_json_error('Failed to mark notification as read');
  }
}

add_action('wp_ajax_baum_mark_notification_read', 'baum_ajax_mark_notification_read');

/**
 * AJAX handler to dismiss notification
 *
 * @since 1.0.0
 */
function baum_ajax_dismiss_notification() {
  // Verify nonce
  if (!wp_verify_nonce($_POST['nonce'], 'baum_notifications_nonce')) {
    wp_send_json_error('Security check failed');
  }

  if (!is_user_logged_in()) {
    wp_send_json_error('User not logged in');
  }

  $notification_id = intval($_POST['notification_id']);
  $user_id = get_current_user_id();

  if (baum_dismiss_notification($notification_id, $user_id)) {
    wp_send_json_success('Notification dismissed');
  } else {
    wp_send_json_error('Failed to dismiss notification');
  }
}

add_action('wp_ajax_baum_dismiss_notification', 'baum_ajax_dismiss_notification');

/**
 * AJAX handler to clear all notifications
 *
 * @since 1.0.0
 */
function baum_ajax_clear_all_notifications() {
  // Verify nonce
  if (!wp_verify_nonce($_POST['nonce'], 'baum_notifications_nonce')) {
    wp_send_json_error('Security check failed');
  }

  if (!is_user_logged_in()) {
    wp_send_json_error('User not logged in');
  }

  $user_id = get_current_user_id();

  // Get all user's notifications
  $args = array(
    'posts_per_page' => -1,
    'fields' => 'ids'
  );

  $notifications = baum_get_notifications($user_id, $args);
  $dismissed_count = 0;

  foreach ($notifications as $notification) {
    if (baum_dismiss_notification($notification['id'], $user_id)) {
      $dismissed_count++;
    }
  }

  wp_send_json_success(array(
    'message' => "Dismissed {$dismissed_count} notifications",
    'count' => $dismissed_count
  ));
}

add_action('wp_ajax_baum_clear_all_notifications', 'baum_ajax_clear_all_notifications');


/**
 * ===== HELPER FUNCTIONS =====
 */

/**
 * Get total notification count for a user
 *
 * @param int $user_id User ID
 * @return int Total notification count
 * @since 1.0.0
 */
function baum_get_total_notification_count($user_id) {
  $args = array(
    'post_type' => 'baum_notifications',
    'post_status' => 'publish',
    'posts_per_page' => -1,
    'fields' => 'ids',
    'meta_query' => array(
      array(
        'key' => 'target_user_id',
        'value' => $user_id,
        'compare' => '='
      ),
      array(
        'key' => 'dismissed',
        'value' => 'no',
        'compare' => '='
      )
    )
  );

  $query = new WP_Query($args);
  return $query->found_posts;
}

/**
 * Get icon for notification type
 *
 * @param string $type Notification type
 * @return string FontAwesome icon class
 * @since 1.0.0
 */
function baum_get_notification_icon($type) {
  $icons = array(
    'comment' => 'fas fa-comment',
    'reply' => 'fas fa-reply',
    'like' => 'fas fa-heart',
    'follow' => 'fas fa-user-plus',
    'unfollow' => 'fas fa-user-minus',
    'mention' => 'fas fa-at',
    'share' => 'fas fa-share',
    'star' => 'fas fa-star',
    'unstar' => 'fas fa-star-o',
    'watch' => 'fas fa-eye',
    'unwatch' => 'fas fa-eye-slash',
    'post' => 'fas fa-file-alt',
    'message' => 'fas fa-envelope',
    'system' => 'fas fa-cog',
    'achievement' => 'fas fa-trophy',
    'warning' => 'fas fa-exclamation-triangle',
    'info' => 'fas fa-info-circle'
  );

  return isset($icons[$type]) ? $icons[$type] : 'fas fa-bell';
}

/**
 * Get color for notification type
 *
 * @param string $type Notification type
 * @return string Hex color code
 * @since 1.0.0
 */
function baum_get_notification_color($type) {
  $colors = array(
    'comment' => '#007cba',
    'reply' => '#007cba',
    'like' => '#e74c3c',
    'follow' => '#27ae60',
    'unfollow' => '#95a5a6',
    'mention' => '#9b59b6',
    'share' => '#f39c12',
    'star' => '#f1c40f',
    'unstar' => '#95a5a6',
    'watch' => '#3498db',
    'unwatch' => '#95a5a6',
    'post' => '#2ecc71',
    'message' => '#e67e22',
    'system' => '#34495e',
    'achievement' => '#f39c12',
    'warning' => '#e74c3c',
    'info' => '#3498db'
  );

  return isset($colors[$type]) ? $colors[$type] : '#007cba';
}

/**
 * ===== NOTIFICATION TYPE EXAMPLES =====
 *
 * This section documents all possible notification types with examples.
 * Add new types here when implementing new features.
 */

/**
 * Example notification types and their usage:
 *
 * SOCIAL INTERACTIONS:
 * - comment: "John commented on your post"
 * - reply: "Jane replied to your comment"
 * - like: "Mike liked your post"
 * - follow: "Sarah started following you"
 * - unfollow: "Tom stopped following you"
 * - mention: "You were mentioned in a post by Alex"
 * - share: "Your post was shared by Lisa"
 *
 * CONTENT INTERACTIONS:
 * - star: "Your story was starred by David"
 * - unstar: "Someone unstarred your story"
 * - watch: "New activity in a topic you're watching"
 * - unwatch: "You stopped watching OpenAI topic"
 * - post: "New post in a group you follow"
 *
 * SYSTEM NOTIFICATIONS:
 * - message: "You have a new private message"
 * - system: "Your account settings were updated"
 * - achievement: "You earned a new badge!"
 * - warning: "Your post was flagged for review"
 * - info: "New features are available"
 *
 * USAGE EXAMPLES:
 *
 * // User commented on a post
 * baum_create_notification(
 *   'comment',
 *   'John commented on your post "Hello World"',
 *   $post_author_id,
 *   $commenter_id,
 *   get_permalink($post_id) . '#comment-' . $comment_id
 * );
 *
 * // User followed another user
 * baum_create_notification(
 *   'follow',
 *   'Sarah started following you',
 *   $followed_user_id,
 *   $follower_id,
 *   get_author_posts_url($follower_id)
 * );
 *
 * // User mentioned in a post
 * baum_create_notification(
 *   'mention',
 *   'You were mentioned in a post by Alex',
 *   $mentioned_user_id,
 *   $post_author_id,
 *   get_permalink($post_id),
 *   array('post_id' => $post_id)
 * );
 *
 * // System achievement
 * baum_create_notification(
 *   'achievement',
 *   'Congratulations! You earned the "First Post" badge',
 *   $user_id,
 *   0, // System notification
 *   '/achievements',
 *   array('badge_id' => $badge_id)
 * );
 */

// 
// Create Activity 
// - baum_create_activity(get_current_user_id(), 'follow', $followed_user_id, 'Followed a user.');
// 

function baum_create_activity($user_id, $action, $reference_id = null, $message = '') {
  $content = "{$action} activity from user ID: {$user_id}";
  if ($reference_id) {
      $content .= " Referenced post ID: {$reference_id}";
  }

  if (!empty($message)) {
      $content .= "\n\n$message";
  }

  $args = [
      'post_type'   => 'baum_activity',
      'post_status' => 'publish',
      'post_author' => $user_id,
      'post_content' => wp_kses_post($content),
  ];

  $activity_id = wp_insert_post($args);

  // Automatically close comments for certain actions (e.g., stars)
  if (in_array($action, ['star', 'unstar'])) {
      wp_update_post(['ID' => $activity_id, 'comment_status' => 'closed']);
  }

  return $activity_id;
}

// 
// Like Post 
// 

function baum_star_story($user_id, $story_id) {
  $starred_stories = get_user_meta($user_id, 'baum_starred_stories', true) ?: [];
  if (!in_array($story_id, $starred_stories)) {
      $starred_stories[] = $story_id;
      update_user_meta($user_id, 'baum_starred_stories', $starred_stories);

      // Alert, activity, notification, and XP
      if (function_exists('baum_alert_success')) {
        baum_alert_success('You starred this story.');
      }
      baum_create_activity($user_id, 'star', $story_id, 'Starred a story.');
      baum_create_notification('star', 'Your story was starred!', get_post_field('post_author', $story_id), $user_id, get_permalink($story_id));

      if (function_exists('gamipress_add_points_to_user')) {
          gamipress_add_points_to_user($user_id, 10, 'star_action');
      }

      // Recalculate the rank of the starred story
      baum_recalculate_post_rank($story_id);
  }
}

// 
// Unlike Post 
// - baum_unstar_story(get_current_user_id(), $story_id); 
// 

function baum_unstar_story($user_id, $story_id) {
  $starred_stories = get_user_meta($user_id, 'baum_starred_stories', true) ?: [];
  if (($key = array_search($story_id, $starred_stories)) !== false) {
      unset($starred_stories[$key]);
      update_user_meta($user_id, 'baum_starred_stories', $starred_stories);

      if (function_exists('baum_alert_info')) {
        baum_alert_info('You unstarred this story.');
      }
      baum_create_activity($user_id, 'unstar', $story_id, 'Unstarred a story.');
      baum_create_notification('unstar', 'Someone unstarred your story.', get_post_field('post_author', $story_id), $user_id, get_permalink($story_id));

      if (function_exists('gamipress_deduct_points_from_user')) {
          gamipress_deduct_points_from_user($user_id, 10, 'unstar_action');
      }

      // Recalculate the rank of the unstarred story
      baum_recalculate_post_rank($story_id);
  }
}

// 
// Follow User 
// 

function baum_follow_user($user_id, $followed_user_id) {
  $followed_users = get_user_meta($user_id, 'baum_followed_users', true) ?: [];
  if (!in_array($followed_user_id, $followed_users)) {
      $followed_users[] = $followed_user_id;
      update_user_meta($user_id, 'baum_followed_users', $followed_users);

      // Alert, activity, notification, and XP
      if (function_exists('baum_alert_success')) {
        baum_alert_success('You are now following this user.');
      }
      baum_create_activity($user_id, 'follow', $followed_user_id, 'Followed a user.');
      baum_create_notification('follow', 'You have a new follower!', $followed_user_id, $user_id, get_author_posts_url($user_id));

      if (function_exists('gamipress_add_points_to_user')) {
          gamipress_add_points_to_user($user_id, 5, 'follow_action');
      }

      // Recalculate the affected user's posts
      $args = ['author' => $followed_user_id, 'post_type' => 'post', 'posts_per_page' => -1];
      $query = new WP_Query($args);

      if ($query->have_posts()) {
          while ($query->have_posts()) {
              $query->the_post();
              baum_recalculate_post_rank(get_the_ID());
          }
      }
      wp_reset_postdata();
  }
}

// 
// Unfolllow User 
// - baum_unfollow_user(get_current_user_id(), $unfollowed_user_id);
// 

function baum_unfollow_user($user_id, $unfollowed_user_id) {
  $followed_users = get_user_meta($user_id, 'baum_followed_users', true) ?: [];
  if (($key = array_search($unfollowed_user_id, $followed_users)) !== false) {
      unset($followed_users[$key]);
      update_user_meta($user_id, 'baum_followed_users', $followed_users);

      if (function_exists('baum_alert_info')) {
        baum_alert_info('You unfollowed this user.');
      }
      baum_create_activity($user_id, 'unfollow', $unfollowed_user_id, 'Unfollowed a user.');
      baum_create_notification('unfollow', 'You lost a follower.', $unfollowed_user_id, $user_id, get_author_posts_url($user_id));

      if (function_exists('gamipress_deduct_points_from_user')) {
          gamipress_deduct_points_from_user($user_id, 5, 'unfollow_action');
      }

      // Recalculate the affected user's posts
      $args = ['author' => $unfollowed_user_id, 'post_type' => 'post', 'posts_per_page' => -1];
      $query = new WP_Query($args);

      if ($query->have_posts()) {
          while ($query->have_posts()) {
              $query->the_post();
              baum_recalculate_post_rank(get_the_ID());
          }
      }
      wp_reset_postdata();
  }
}

// 
// Watch Taxonomy Term
// 

function baum_watch_term($user_id, $term_id) {
  $watched_terms = get_user_meta($user_id, 'baum_watched_terms', true) ?: [];
  if (!in_array($term_id, $watched_terms)) {
      $watched_terms[] = $term_id;
      update_user_meta($user_id, 'baum_watched_terms', $watched_terms);

      // Alert, activity, notification, and XP
      if (function_exists('baum_alert_success')) {
        baum_alert_success('You are now watching this collection/topic.');
      }
      baum_create_activity($user_id, 'watch', $term_id, 'Started watching a taxonomy term.');

      if (function_exists('gamipress_add_points_to_user')) {
          gamipress_add_points_to_user($user_id, 5, 'watch_action');
      }
  }
}

// 
// Unwatch Taxonomy Term
// 

function baum_unwatch_term($user_id, $term_id) {
  $watched_terms = get_user_meta($user_id, 'baum_watched_terms', true) ?: [];
  if (($key = array_search($term_id, $watched_terms)) !== false) {
      unset($watched_terms[$key]);
      update_user_meta($user_id, 'baum_watched_terms', $watched_terms);

      // Alert, activity, notification, and XP
      if (function_exists('baum_alert_info')) {
        baum_alert_info('You stopped watching this collection/topic.');
      }
      baum_create_activity($user_id, 'unwatch', $term_id, 'Stopped watching a taxonomy term.');

      if (function_exists('gamipress_deduct_points_from_user')) {
          gamipress_deduct_points_from_user($user_id, 5, 'unwatch_action');
      }
  }
}

// 
// Recalculate Post Rank 
// 

function baum_recalculate_post_rank($post_id) {
  if (!$post_id) return;

  $vote_count = get_post_meta($post_id, 'baumpress_vote_score', true) ?: 0;
  $post_date = get_post_time('U', false, $post_id);

  // Use the Hacker News sorting formula
  $current_time = time();
  $hours_ago = ($current_time - $post_date) / 3600;
  $rank = ($vote_count - 1) / pow(($hours_ago + 2), 1.5); // Hacker News style formula

  update_post_meta($post_id, 'baumpress_rank', $rank);
}
