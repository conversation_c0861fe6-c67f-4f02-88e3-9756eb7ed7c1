# Log File Management and Monitoring

## Overview

Log file management is crucial for monitoring, troubleshooting, and maintaining the Baum Mail plugin's email services. This document covers all log files used by the email system, how to monitor them, analyze issues, and implement proper log rotation and retention policies.

## Log File Locations

### System Email Logs

**Main Mail Log:**
- **Location**: `/var/log/mail.log`
- **Content**: All email-related system events
- **Services**: Postfix, Dovecot, authentication events
- **Rotation**: Daily via logrotate

**Postfix Logs:**
- **Location**: `/var/log/postfix.log` (if configured separately)
- **Content**: SMTP server events, delivery status, errors
- **Format**: Syslog format with timestamps

**Dovecot Logs:**
- **Location**: `/var/log/dovecot.log`, `/var/log/dovecot-info.log`
- **Content**: IMAP/POP3 connections, authentication, errors
- **Debug**: `/var/log/dovecot-debug.log` (when enabled)

### Security and Filtering Logs

**SpamAssassin Logs:**
- **Location**: `/var/log/spamassassin.log`
- **Content**: Spam detection events, rule updates, training
- **Plugin Log**: `plugins/baum-mail/logs/spamassassin.log`

**ClamAV Logs:**
- **Location**: `/var/log/clamav/clamav.log`
- **Content**: Virus scanning events, database updates
- **Freshclam**: `/var/log/clamav/freshclam.log`
- **Plugin Log**: `plugins/baum-mail/logs/clamav.log`

### Plugin-Specific Logs

**Baum Mail Main Log:**
- **Location**: `plugins/baum-mail/logs/baum-mail.log`
- **Content**: Plugin events, API calls, errors
- **Format**: WordPress-style logging with timestamps

**GPG Encryption Log:**
- **Location**: `plugins/baum-mail/logs/gpg.log`
- **Content**: GPG key operations, encryption/decryption events
- **Security**: Contains no private key data

## How Baum Mail Uses Logs

### Centralized Logging System

```php
// Baum Mail logging system
class BaumMail_Logger {
  
  private $log_dir;
  private $log_level;
  
  public function __construct() {
    $this->log_dir = BAUM_MAIL_PLUGIN_DIR . 'logs/';
    $this->log_level = get_option('baum_mail_log_level', 'info');
    
    // Ensure log directory exists
    if (!is_dir($this->log_dir)) {
      wp_mkdir_p($this->log_dir);
    }
  }
  
  /**
   * Log message with level and context
   */
  public function log($level, $message, $context = array()) {
    if (!$this->should_log($level)) {
      return;
    }
    
    $timestamp = current_time('Y-m-d H:i:s');
    $context_str = !empty($context) ? ' ' . json_encode($context) : '';
    $log_entry = "[{$timestamp}] {$level}: {$message}{$context_str}\n";
    
    // Write to main log
    file_put_contents($this->log_dir . 'baum-mail.log', $log_entry, FILE_APPEND | LOCK_EX);
    
    // Write to specific service log if context provided
    if (isset($context['service'])) {
      $service_log = $this->log_dir . $context['service'] . '.log';
      file_put_contents($service_log, $log_entry, FILE_APPEND | LOCK_EX);
    }
  }
  
  /**
   * Log levels: debug, info, warning, error, critical
   */
  public function debug($message, $context = array()) {
    $this->log('DEBUG', $message, $context);
  }
  
  public function info($message, $context = array()) {
    $this->log('INFO', $message, $context);
  }
  
  public function warning($message, $context = array()) {
    $this->log('WARNING', $message, $context);
  }
  
  public function error($message, $context = array()) {
    $this->log('ERROR', $message, $context);
  }
  
  public function critical($message, $context = array()) {
    $this->log('CRITICAL', $message, $context);
  }
}
```

### Log Analysis and Monitoring

```php
/**
 * Log analysis tools
 */
class BaumMail_LogAnalyzer {
  
  /**
   * Parse mail log for delivery statistics
   */
  public function get_delivery_stats($date = null) {
    $date = $date ?: date('Y-m-d');
    $log_file = '/var/log/mail.log';
    
    if (!file_exists($log_file)) {
      return array('error' => 'Log file not found');
    }
    
    $stats = array(
      'sent' => 0,
      'delivered' => 0,
      'bounced' => 0,
      'deferred' => 0,
      'rejected' => 0
    );
    
    $handle = fopen($log_file, 'r');
    if ($handle) {
      while (($line = fgets($handle)) !== false) {
        if (strpos($line, $date) === false) continue;
        
        if (strpos($line, 'status=sent') !== false) {
          $stats['sent']++;
        } elseif (strpos($line, 'status=delivered') !== false) {
          $stats['delivered']++;
        } elseif (strpos($line, 'status=bounced') !== false) {
          $stats['bounced']++;
        } elseif (strpos($line, 'status=deferred') !== false) {
          $stats['deferred']++;
        } elseif (strpos($line, 'reject') !== false) {
          $stats['rejected']++;
        }
      }
      fclose($handle);
    }
    
    return $stats;
  }
  
  /**
   * Get recent errors from logs
   */
  public function get_recent_errors($hours = 24) {
    $errors = array();
    $cutoff_time = time() - ($hours * 3600);
    
    $log_files = array(
      '/var/log/mail.log',
      BAUM_MAIL_PLUGIN_DIR . 'logs/baum-mail.log'
    );
    
    foreach ($log_files as $log_file) {
      if (!file_exists($log_file)) continue;
      
      $handle = fopen($log_file, 'r');
      if ($handle) {
        while (($line = fgets($handle)) !== false) {
          if (preg_match('/(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/', $line, $matches)) {
            $log_time = strtotime($matches[1]);
            if ($log_time < $cutoff_time) continue;
          }
          
          if (stripos($line, 'error') !== false || stripos($line, 'warning') !== false) {
            $errors[] = array(
              'timestamp' => $matches[1] ?? 'Unknown',
              'message' => trim($line),
              'file' => basename($log_file)
            );
          }
        }
        fclose($handle);
      }
    }
    
    return array_slice($errors, -50); // Return last 50 errors
  }
}
```

## Command Line Usage

### Log Viewing and Monitoring

```bash
# View real-time mail log
tail -f /var/log/mail.log

# View last 100 lines
tail -n 100 /var/log/mail.log

# View logs for specific date
grep "$(date '+%b %d')" /var/log/mail.log

# View logs for specific service
grep "postfix" /var/log/mail.log
grep "dovecot" /var/log/mail.log

# Search for specific email address
grep "<EMAIL>" /var/log/mail.log

# View errors only
grep -i error /var/log/mail.log

# View authentication failures
grep "authentication failed" /var/log/mail.log
```

### Log Analysis Commands

```bash
# Count emails sent today
grep "$(date '+%b %d')" /var/log/mail.log | grep "status=sent" | wc -l

# Count bounced emails
grep "status=bounced" /var/log/mail.log | wc -l

# Top sender domains
grep "from=<" /var/log/mail.log | sed 's/.*from=<[^@]*@\([^>]*\)>.*/\1/' | sort | uniq -c | sort -nr | head -10

# Top recipient domains
grep "to=<" /var/log/mail.log | sed 's/.*to=<[^@]*@\([^>]*\)>.*/\1/' | sort | uniq -c | sort -nr | head -10

# Failed login attempts
grep "authentication failed" /var/log/mail.log | awk '{print $6}' | sort | uniq -c | sort -nr

# Spam detection statistics
grep "X-Spam-Score" /var/log/mail.log | wc -l
```

### Log Rotation Management

```bash
# Manual log rotation
sudo logrotate -f /etc/logrotate.d/rsyslog

# Test log rotation configuration
sudo logrotate -d /etc/logrotate.d/rsyslog

# View log rotation status
sudo cat /var/lib/logrotate/status

# Force rotation of specific log
sudo logrotate -f /etc/logrotate.d/baum-mail
```

### Log Cleanup Commands

```bash
# Clean old logs (older than 30 days)
find /var/log -name "*.log" -mtime +30 -delete

# Compress old logs
find /var/log -name "*.log" -mtime +7 -exec gzip {} \;

# Clean plugin logs
find plugins/baum-mail/logs/ -name "*.log" -mtime +14 -delete

# Archive logs by date
tar -czf mail-logs-$(date +%Y%m%d).tar.gz /var/log/mail.log*
```

## Programmatic API Usage

### WordPress Integration

```php
// WordPress admin log viewer
add_action('admin_menu', function() {
  add_submenu_page(
    'baum-mail',
    'Log Viewer',
    'Logs',
    'manage_options',
    'baum-mail-logs',
    'baum_mail_logs_page'
  );
});

function baum_mail_logs_page() {
  $analyzer = new BaumMail_LogAnalyzer();
  
  // Get recent errors
  $errors = $analyzer->get_recent_errors(24);
  
  // Get delivery stats
  $stats = $analyzer->get_delivery_stats();
  
  ?>
  <div class="wrap">
    <h1>Email System Logs</h1>
    
    <div class="card">
      <h2>Delivery Statistics (Today)</h2>
      <table class="widefat">
        <tr><td>Sent:</td><td><?php echo $stats['sent']; ?></td></tr>
        <tr><td>Delivered:</td><td><?php echo $stats['delivered']; ?></td></tr>
        <tr><td>Bounced:</td><td><?php echo $stats['bounced']; ?></td></tr>
        <tr><td>Deferred:</td><td><?php echo $stats['deferred']; ?></td></tr>
        <tr><td>Rejected:</td><td><?php echo $stats['rejected']; ?></td></tr>
      </table>
    </div>
    
    <div class="card">
      <h2>Recent Errors (Last 24 Hours)</h2>
      <table class="widefat">
        <thead>
          <tr><th>Time</th><th>Message</th><th>File</th></tr>
        </thead>
        <tbody>
          <?php foreach ($errors as $error): ?>
          <tr>
            <td><?php echo esc_html($error['timestamp']); ?></td>
            <td><?php echo esc_html($error['message']); ?></td>
            <td><?php echo esc_html($error['file']); ?></td>
          </tr>
          <?php endforeach; ?>
        </tbody>
      </table>
    </div>
  </div>
  <?php
}
```

### REST API Integration

```php
// Log management REST API endpoints
add_action('rest_api_init', function() {
  register_rest_route('baum-mail/v1', '/logs/stats', array(
    'methods' => 'GET',
    'callback' => function($request) {
      $analyzer = new BaumMail_LogAnalyzer();
      $date = $request->get_param('date');
      return $analyzer->get_delivery_stats($date);
    },
    'permission_callback' => 'baum_mail_check_permissions'
  ));
  
  register_rest_route('baum-mail/v1', '/logs/errors', array(
    'methods' => 'GET',
    'callback' => function($request) {
      $analyzer = new BaumMail_LogAnalyzer();
      $hours = $request->get_param('hours') ?: 24;
      return $analyzer->get_recent_errors($hours);
    },
    'permission_callback' => 'baum_mail_check_permissions'
  ));
  
  register_rest_route('baum-mail/v1', '/logs/download', array(
    'methods' => 'GET',
    'callback' => function($request) {
      $log_type = $request->get_param('type');
      $log_file = baum_mail_get_log_file($log_type);
      
      if (!file_exists($log_file)) {
        return new WP_Error('log_not_found', 'Log file not found');
      }
      
      header('Content-Type: text/plain');
      header('Content-Disposition: attachment; filename="' . basename($log_file) . '"');
      readfile($log_file);
      exit;
    },
    'permission_callback' => 'baum_mail_check_permissions'
  ));
});
```

## End-User Usage

### WordPress Admin Log Interface

**Log Viewer Features:**
- Real-time log monitoring
- Filter by date range and log level
- Search functionality
- Download log files
- Email delivery statistics
- Error alerts and notifications

**Dashboard Widgets:**
- Recent email activity
- Error count alerts
- System health status
- Performance metrics

### Email Notifications for Critical Events

```php
// Automated log monitoring and alerts
class BaumMail_LogMonitor {
  
  /**
   * Check for critical errors and send alerts
   */
  public function check_critical_errors() {
    $analyzer = new BaumMail_LogAnalyzer();
    $errors = $analyzer->get_recent_errors(1); // Last hour
    
    $critical_errors = array_filter($errors, function($error) {
      return stripos($error['message'], 'critical') !== false ||
             stripos($error['message'], 'fatal') !== false;
    });
    
    if (!empty($critical_errors)) {
      $this->send_alert_email($critical_errors);
    }
  }
  
  /**
   * Send alert email to administrators
   */
  private function send_alert_email($errors) {
    $subject = 'Critical Email System Error Alert';
    $message = "Critical errors detected in email system:\n\n";
    
    foreach ($errors as $error) {
      $message .= "Time: {$error['timestamp']}\n";
      $message .= "Error: {$error['message']}\n\n";
    }
    
    wp_mail(get_option('admin_email'), $subject, $message);
  }
}

// Schedule hourly error checking
wp_schedule_event(time(), 'hourly', 'baum_mail_check_errors');
add_action('baum_mail_check_errors', function() {
  $monitor = new BaumMail_LogMonitor();
  $monitor->check_critical_errors();
});
```

## Configuration Files

### Log Rotation Configuration (/etc/logrotate.d/baum-mail)

```
# Baum Mail log rotation configuration

/var/log/mail.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 640 syslog adm
    postrotate
        systemctl reload rsyslog > /dev/null 2>&1 || true
    endscript
}

/var/www/html/wp-content/plugins/baum-mail/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        # No service restart needed for plugin logs
    endscript
}

/var/log/clamav/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 clamav clamav
    postrotate
        systemctl reload clamav-daemon > /dev/null 2>&1 || true
    endscript
}
```

### Rsyslog Configuration (/etc/rsyslog.d/50-baum-mail.conf)

```
# Baum Mail rsyslog configuration

# Postfix logs
:programname, isequal, "postfix" /var/log/postfix.log
& stop

# Dovecot logs
:programname, isequal, "dovecot" /var/log/dovecot.log
& stop

# SpamAssassin logs
:programname, isequal, "spamd" /var/log/spamassassin.log
& stop

# All mail-related logs
mail.* /var/log/mail.log

# High priority mail errors to separate file
mail.err /var/log/mail-errors.log
```

### WordPress Logging Configuration

```php
// wp-config.php logging settings
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);

// Baum Mail specific logging
define('BAUM_MAIL_DEBUG', true);
define('BAUM_MAIL_LOG_LEVEL', 'info'); // debug, info, warning, error, critical
define('BAUM_MAIL_LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('BAUM_MAIL_LOG_RETENTION_DAYS', 30);
```

## Performance and Storage Management

### Log File Size Management

```php
/**
 * Manage log file sizes and rotation
 */
class BaumMail_LogManager {
  
  /**
   * Rotate log file if it exceeds size limit
   */
  public function rotate_if_needed($log_file) {
    $max_size = get_option('baum_mail_log_max_size', 10 * 1024 * 1024); // 10MB
    
    if (file_exists($log_file) && filesize($log_file) > $max_size) {
      $backup_file = $log_file . '.' . date('Y-m-d-H-i-s');
      rename($log_file, $backup_file);
      
      // Compress old log
      if (function_exists('gzencode')) {
        $content = file_get_contents($backup_file);
        file_put_contents($backup_file . '.gz', gzencode($content));
        unlink($backup_file);
      }
      
      // Create new empty log file
      touch($log_file);
      chmod($log_file, 0644);
    }
  }
  
  /**
   * Clean old log files
   */
  public function cleanup_old_logs() {
    $retention_days = get_option('baum_mail_log_retention_days', 30);
    $cutoff_time = time() - ($retention_days * 24 * 3600);
    
    $log_dir = BAUM_MAIL_PLUGIN_DIR . 'logs/';
    $files = glob($log_dir . '*.log.*');
    
    foreach ($files as $file) {
      if (filemtime($file) < $cutoff_time) {
        unlink($file);
      }
    }
  }
}
```

## Troubleshooting

### Common Log Issues

**Log Files Not Created:**
```bash
# Check directory permissions
ls -la /var/log/
ls -la plugins/baum-mail/logs/

# Create missing directories
sudo mkdir -p /var/log/baum-mail
sudo chown syslog:adm /var/log/baum-mail

# Check rsyslog configuration
sudo rsyslogd -N1
```

**Log Rotation Not Working:**
```bash
# Test logrotate configuration
sudo logrotate -d /etc/logrotate.d/baum-mail

# Force rotation
sudo logrotate -f /etc/logrotate.d/baum-mail

# Check logrotate status
sudo cat /var/lib/logrotate/status
```

**High Disk Usage:**
```bash
# Find large log files
find /var/log -name "*.log" -size +100M

# Compress old logs
find /var/log -name "*.log" -mtime +7 -exec gzip {} \;

# Clean up old compressed logs
find /var/log -name "*.gz" -mtime +30 -delete
```

### Log Analysis Tools

```bash
# Install log analysis tools
sudo apt-get install goaccess logwatch pflogsumm

# Generate mail statistics with pflogsumm
pflogsumm /var/log/mail.log

# Real-time log analysis with goaccess
goaccess /var/log/mail.log --log-format=COMBINED

# Daily log summary with logwatch
logwatch --service postfix --range today
```

## Integration with Monitoring Systems

### Elasticsearch and Kibana Integration

```php
// Send logs to Elasticsearch
class BaumMail_ElasticsearchLogger {
  
  public function send_to_elasticsearch($log_data) {
    $elasticsearch_url = get_option('baum_mail_elasticsearch_url');
    
    if (!$elasticsearch_url) return;
    
    $data = array(
      '@timestamp' => date('c'),
      'service' => $log_data['service'],
      'level' => $log_data['level'],
      'message' => $log_data['message'],
      'context' => $log_data['context']
    );
    
    wp_remote_post($elasticsearch_url . '/baum-mail-logs/_doc', array(
      'headers' => array('Content-Type' => 'application/json'),
      'body' => json_encode($data)
    ));
  }
}
```

### Prometheus Metrics Integration

```php
// Export metrics for Prometheus
add_action('init', function() {
  if ($_SERVER['REQUEST_URI'] === '/metrics') {
    header('Content-Type: text/plain');
    
    $analyzer = new BaumMail_LogAnalyzer();
    $stats = $analyzer->get_delivery_stats();
    
    echo "# HELP baum_mail_emails_sent Total emails sent\n";
    echo "# TYPE baum_mail_emails_sent counter\n";
    echo "baum_mail_emails_sent {$stats['sent']}\n";
    
    echo "# HELP baum_mail_emails_bounced Total emails bounced\n";
    echo "# TYPE baum_mail_emails_bounced counter\n";
    echo "baum_mail_emails_bounced {$stats['bounced']}\n";
    
    exit;
  }
});
```
