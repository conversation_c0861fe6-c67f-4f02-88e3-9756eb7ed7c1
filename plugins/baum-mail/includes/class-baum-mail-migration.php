<?php
/**
 * Database migration class for Baum Mail plugin
 * Handles updates to clarify SSL/TLS vs GPG encryption terminology
 *
 * @package BaumMail
 * @since 1.1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class BaumMail_Migration {
  
  /**
   * Current database version
   */
  const DB_VERSION = '1.1.0';
  
  /**
   * Initialize migration
   */
  public static function init() {
    add_action('plugins_loaded', array(__CLASS__, 'check_migration'));
  }
  
  /**
   * Check if migration is needed
   */
  public static function check_migration() {
    $current_version = get_option('baum_mail_db_version', '1.0.0');
    
    if (version_compare($current_version, self::DB_VERSION, '<')) {
      self::run_migration($current_version);
    }
  }
  
  /**
   * Run migration based on current version
   */
  public static function run_migration($from_version) {
    global $wpdb;
    
    // Migration from 1.0.0 to 1.1.0 - Clarify encryption terminology
    if (version_compare($from_version, '1.1.0', '<')) {
      self::migrate_to_1_1_0();
    }
    
    // Update database version
    update_option('baum_mail_db_version', self::DB_VERSION);
  }
  
  /**
   * Migration to version 1.1.0
   * Adds new fields to clarify SSL/TLS vs GPG encryption
   */
  private static function migrate_to_1_1_0() {
    global $wpdb;
    
    $charset_collate = $wpdb->get_charset_collate();
    
    // Add new fields to domains table
    $domains_table = $wpdb->prefix . 'baum_mail_domains';
    
    // Check if new fields already exist
    $columns = $wpdb->get_col("DESCRIBE {$domains_table}");
    
    if (!in_array('gpg_encryption_enabled', $columns)) {
      $wpdb->query("ALTER TABLE {$domains_table} ADD COLUMN gpg_encryption_enabled tinyint(1) DEFAULT 1 AFTER active");
    }
    
    if (!in_array('ssl_tls_enabled', $columns)) {
      $wpdb->query("ALTER TABLE {$domains_table} ADD COLUMN ssl_tls_enabled tinyint(1) DEFAULT 1 AFTER gpg_encryption_enabled");
    }
    
    if (!in_array('max_accounts', $columns)) {
      $wpdb->query("ALTER TABLE {$domains_table} ADD COLUMN max_accounts int(11) DEFAULT 100 AFTER ssl_tls_enabled");
    }
    
    if (!in_array('daily_send_limit', $columns)) {
      $wpdb->query("ALTER TABLE {$domains_table} ADD COLUMN daily_send_limit int(11) DEFAULT 1000 AFTER max_accounts");
    }
    
    if (!in_array('daily_receive_limit', $columns)) {
      $wpdb->query("ALTER TABLE {$domains_table} ADD COLUMN daily_receive_limit int(11) DEFAULT 5000 AFTER daily_send_limit");
    }
    
    // Migrate existing encryption_enabled data if it exists
    if (in_array('encryption_enabled', $columns)) {
      $wpdb->query("UPDATE {$domains_table} SET gpg_encryption_enabled = encryption_enabled WHERE gpg_encryption_enabled IS NULL");
    }
    
    // Add new fields to accounts table
    $accounts_table = $wpdb->prefix . 'baum_mail_accounts';
    $account_columns = $wpdb->get_col("DESCRIBE {$accounts_table}");
    
    if (!in_array('gpg_encryption_enabled', $account_columns)) {
      $wpdb->query("ALTER TABLE {$accounts_table} ADD COLUMN gpg_encryption_enabled tinyint(1) DEFAULT 0 AFTER active");
    }
    
    if (!in_array('gpg_public_key', $account_columns)) {
      $wpdb->query("ALTER TABLE {$accounts_table} ADD COLUMN gpg_public_key text DEFAULT NULL AFTER gpg_encryption_enabled");
    }
    
    if (!in_array('gpg_key_fingerprint', $account_columns)) {
      $wpdb->query("ALTER TABLE {$accounts_table} ADD COLUMN gpg_key_fingerprint varchar(40) DEFAULT NULL AFTER gpg_public_key");
    }
    
    // Migrate existing encryption_enabled data if it exists
    if (in_array('encryption_enabled', $account_columns)) {
      $wpdb->query("UPDATE {$accounts_table} SET gpg_encryption_enabled = encryption_enabled WHERE gpg_encryption_enabled IS NULL");
    }
    
    // Create GPG keys table if it doesn't exist
    $gpg_keys_table = $wpdb->prefix . 'baum_mail_gpg_keys';
    $gpg_keys_sql = "CREATE TABLE IF NOT EXISTS {$gpg_keys_table} (
      id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
      email varchar(255) NOT NULL,
      key_id varchar(16) NOT NULL,
      public_key text NOT NULL,
      private_key text DEFAULT NULL,
      fingerprint varchar(40) NOT NULL,
      created_at datetime DEFAULT CURRENT_TIMESTAMP,
      updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (id),
      UNIQUE KEY email_key_id (email, key_id),
      KEY email (email),
      KEY fingerprint (fingerprint)
    ) {$charset_collate};";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($gpg_keys_sql);
    
    // Create quarantine table for virus/spam management
    $quarantine_table = $wpdb->prefix . 'baum_mail_quarantine';
    $quarantine_sql = "CREATE TABLE IF NOT EXISTS {$quarantine_table} (
      id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
      original_path varchar(500) NOT NULL,
      quarantine_path varchar(500) NOT NULL,
      threat_name varchar(255) NOT NULL,
      threat_type enum('virus', 'spam', 'malware') DEFAULT 'virus',
      account_id bigint(20) unsigned DEFAULT NULL,
      domain_id bigint(20) unsigned DEFAULT NULL,
      quarantined_at datetime DEFAULT CURRENT_TIMESTAMP,
      released_at datetime DEFAULT NULL,
      status enum('quarantined', 'released', 'deleted') DEFAULT 'quarantined',
      PRIMARY KEY (id),
      KEY account_id (account_id),
      KEY domain_id (domain_id),
      KEY quarantined_at (quarantined_at),
      KEY status (status),
      KEY threat_type (threat_type)
    ) {$charset_collate};";
    
    dbDelta($quarantine_sql);
    
    // Update WordPress options to use new naming convention
    $old_options = array(
      'baum_mail_encryption_enabled' => 'baum_mail_gpg_encryption_enabled',
      'baum_mail_default_encryption' => 'baum_mail_default_gpg_encryption'
    );
    
    foreach ($old_options as $old_option => $new_option) {
      $old_value = get_option($old_option);
      if ($old_value !== false && get_option($new_option) === false) {
        add_option($new_option, $old_value);
      }
    }
    
    // Add new SSL/TLS specific options
    $ssl_defaults = array(
      'baum_mail_ssl_tls_enabled' => true,
      'baum_mail_force_ssl_tls' => true,
      'baum_mail_ssl_cert_auto_renew' => false,
      'baum_mail_ssl_cert_expiry_warning_days' => 30
    );
    
    foreach ($ssl_defaults as $option => $value) {
      if (get_option($option) === false) {
        add_option($option, $value);
      }
    }
    
    // Log migration completion
    error_log('Baum Mail: Database migration to version 1.1.0 completed successfully');
  }
  
  /**
   * Get migration status
   */
  public static function get_migration_status() {
    $current_version = get_option('baum_mail_db_version', '1.0.0');
    $needs_migration = version_compare($current_version, self::DB_VERSION, '<');
    
    return array(
      'current_version' => $current_version,
      'target_version' => self::DB_VERSION,
      'needs_migration' => $needs_migration,
      'migration_available' => true
    );
  }
  
  /**
   * Force migration (for admin use)
   */
  public static function force_migration() {
    if (!current_user_can('manage_options')) {
      return new WP_Error('insufficient_permissions', 'Insufficient permissions to run migration');
    }
    
    $current_version = get_option('baum_mail_db_version', '1.0.0');
    self::run_migration($current_version);
    
    return array(
      'success' => true,
      'message' => 'Migration completed successfully',
      'new_version' => self::DB_VERSION
    );
  }
}

// Initialize migration
BaumMail_Migration::init();
