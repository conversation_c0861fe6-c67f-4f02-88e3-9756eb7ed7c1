#!/bin/bash

# MapSCII WebSocket Proxy Setup Script
# This script sets up a WebSocket-to-Telnet proxy for connecting to mapscii.me

echo "🗺️  MapSCII WebSocket Proxy Setup"
echo "=================================="
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first:"
    echo "   https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js found: $(node --version)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ npm found: $(npm --version)"
echo ""

# Ask user about SSL requirements
echo "🔒 SSL Configuration"
echo "===================="
echo ""
echo "Is your WordPress site running on HTTPS?"
echo "1) Yes - I need SSL/WSS (https://baumpress.localhost)"
echo "2) No - HTTP is fine (http://baumpress.localhost)"
echo ""
read -p "Choose option (1 or 2): " ssl_choice

if [ "$ssl_choice" = "1" ]; then
    echo ""
    echo "🔒 Setting up SSL proxy..."

    # Check if SSL certificates exist
    if [ ! -f "cert.pem" ] || [ ! -f "key.pem" ]; then
        echo "🔧 Generating SSL certificates with multiple hostnames..."
        echo ""

        # Create config file for certificate with multiple hostnames
        cat > cert.conf << EOF
[req]
default_bits = 4096
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
CN=localhost

[v3_req]
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = baumpress.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

        openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes -config cert.conf -extensions v3_req

        if [ $? -eq 0 ]; then
            echo "✅ SSL certificates generated successfully"
            echo "   Hostnames: localhost, baumpress.localhost, 127.0.0.1"
            rm cert.conf
        else
            echo "❌ Failed to generate SSL certificates"
            exit 1
        fi
    else
        echo "✅ SSL certificates already exist"
    fi

    echo ""
    echo "🚀 Starting secure MapSCII proxy server..."
    echo "   Local URL: wss://localhost:8443"
    echo "   Target: mapscii.me:23"
    echo ""
    echo "🔒 Important: You must accept the self-signed certificate!"
    echo "   1. Visit https://localhost:8443 in your browser"
    echo "   2. Click 'Advanced' → 'Proceed to localhost'"
    echo "   3. Then refresh your WordPress page"
    echo ""
    echo "🌐 Starting secure proxy..."

    # Start SSL proxy
    node mapscii-proxy-ssl.js

else
    echo ""
    echo "🚀 Starting standard MapSCII proxy server..."
    echo "   Local URL: ws://localhost:8080"
    echo "   Target: mapscii.me:23"
    echo ""
    echo "💡 Tips:"
    echo "   • Keep this terminal window open"
    echo "   • Press Ctrl+C to stop the proxy"
    echo "   • Refresh your browser page after starting"
    echo ""
    echo "🌐 Starting proxy..."

    # Start standard proxy
    node mapscii-proxy.js
fi
