#!/usr/bin/env node

/**
 * MapSCII WebSocket-to-Telnet Proxy with SSL Support
 * For HTTPS sites that require secure WebSocket connections
 */

const WebSocket = require('ws');
const https = require('https');
const fs = require('fs');
const net = require('net');
const path = require('path');

const WEBSOCKET_PORT = 8443;
const MAPSCII_HOST = 'mapscii.me';
const MAPSCII_PORT = 23;

console.log('🗺️  MapSCII WebSocket Proxy (SSL)');
console.log('==================================');
console.log(`🔒 Secure WebSocket Server: wss://localhost:${WEBSOCKET_PORT}`);
console.log(`🎯 Target Server: ${MAPSCII_HOST}:${MAPSCII_PORT}`);
console.log('');

// Check for SSL certificates
const certPath = path.join(__dirname, 'cert.pem');
const keyPath = path.join(__dirname, 'key.pem');

if (!fs.existsSync(certPath) || !fs.existsSync(keyPath)) {
  console.log('❌ SSL certificates not found!');
  console.log('');
  console.log('🔧 Generate SSL certificates with:');
  console.log('   openssl req -x509 -newkey rsa:4096 -keyout key.pem \\');
  console.log('     -out cert.pem -days 365 -nodes');
  console.log('');
  console.log('💡 When prompted, use "localhost" as Common Name');
  process.exit(1);
}

// Load SSL certificates
const serverOptions = {
  cert: fs.readFileSync(certPath),
  key: fs.readFileSync(keyPath)
};

// Create HTTPS server with request handler
const server = https.createServer(serverOptions, (req, res) => {
  // Handle HTTP requests (for certificate acceptance)
  res.writeHead(200, { 'Content-Type': 'text/html' });
  res.end(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>MapSCII Proxy Server</title>
      <style>
        body { font-family: monospace; background: #000; color: #0f0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { color: #0f0; }
        .success { color: #0f0; }
        .info { color: #ff0; }
        pre { background: #111; padding: 10px; border: 1px solid #333; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>🗺️ MapSCII WebSocket Proxy (SSL)</h1>
        <div class="success">✅ SSL Certificate Accepted Successfully!</div>
        <br>
        <div class="status">🔒 Secure WebSocket Server: wss://localhost:${WEBSOCKET_PORT}</div>
        <div class="status">🎯 Target Server: ${MAPSCII_HOST}:${MAPSCII_PORT}</div>
        <br>
        <div class="info">📋 Next Steps:</div>
        <ol>
          <li>Keep this proxy server running</li>
          <li>Go back to your WordPress page</li>
          <li>Refresh the page</li>
          <li>The MapSCII terminal should now connect!</li>
        </ol>
        <br>
        <div class="info">🔧 Connection Details:</div>
        <pre>WebSocket URL: wss://localhost:${WEBSOCKET_PORT}
Protocol: Secure WebSocket (WSS)
Status: Server Running
Certificate: Self-Signed (Accepted)</pre>
        <br>
        <div class="info">💡 Troubleshooting:</div>
        <ul>
          <li>If connection fails, check the proxy server console</li>
          <li>Make sure this page loaded without certificate warnings</li>
          <li>Refresh your WordPress page after accepting this certificate</li>
        </ul>
      </div>
    </body>
    </html>
  `);
});

// Create WebSocket server
const wss = new WebSocket.Server({ 
  server: server,
  perMessageDeflate: false
});

console.log(`✅ SSL certificates loaded`);
console.log(`🔒 Secure WebSocket server starting on port ${WEBSOCKET_PORT}`);

wss.on('connection', function connection(ws, req) {
  const clientIP = req.socket.remoteAddress;
  console.log(`🔗 New secure WebSocket connection from ${clientIP}`);
  
  // Create Telnet connection to mapscii.me
  const telnetSocket = new net.Socket();

  // Set socket options for better connection handling
  telnetSocket.setKeepAlive(true, 30000); // Keep alive every 30 seconds
  telnetSocket.setTimeout(60000); // 60 second timeout
  
  // Connect to mapscii.me
  telnetSocket.connect(MAPSCII_PORT, MAPSCII_HOST, function() {
    console.log(`📡 Connected to ${MAPSCII_HOST}:${MAPSCII_PORT}`);
    console.log(`🔄 Telnet connection established, waiting for data...`);

    // Send aggressive initial data to prevent timeout
    setTimeout(() => {
      if (telnetSocket.writable) {
        console.log(`🚀 Sending initial data to mapscii...`);
        telnetSocket.write('\r\n'); // Send newline to trigger mapscii

        // Send follow-up commands quickly
        setTimeout(() => {
          if (telnetSocket.writable) {
            console.log(`🚀 Sending follow-up commands...`);
            telnetSocket.write('\r'); // Another enter
            telnetSocket.write(' ');  // Space
          }
        }, 1000);

        // Very aggressive keep-alive to prevent timeout
        const keepAlive = setInterval(() => {
          if (telnetSocket.writable) {
            console.log(`💓 Sending aggressive keep-alive to mapscii...`);
            telnetSocket.write('w'); // Move north
            setTimeout(() => {
              if (telnetSocket.writable) {
                telnetSocket.write('s'); // Move back south
              }
            }, 100);
          } else {
            clearInterval(keepAlive);
          }
        }, 4000); // Every 4 seconds - very aggressive
      }
    }, 200); // Start much faster
  });
  
  // Forward data from WebSocket to Telnet
  ws.on('message', function message(data) {
    console.log(`📥 Received ${data.length} bytes from WebSocket`);
    if (telnetSocket.writable) {
      telnetSocket.write(data);
      console.log(`📤 Forwarded ${data.length} bytes to mapscii`);
    } else {
      console.log(`⚠️  Telnet socket not writable, cannot forward data`);
    }
  });
  
  // Forward data from Telnet to WebSocket
  telnetSocket.on('data', function(data) {
    console.log(`📥 Received ${data.length} bytes from mapscii`);
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(data);
      console.log(`📤 Forwarded ${data.length} bytes to WebSocket`);
    } else {
      console.log(`⚠️  WebSocket not open, cannot forward data`);
    }
  });
  
  // Handle WebSocket close
  ws.on('close', function close() {
    console.log(`❌ Secure WebSocket connection closed from ${clientIP}`);
    if (telnetSocket.writable) {
      telnetSocket.end();
    }
  });
  
  // Handle Telnet close
  telnetSocket.on('close', function(hadError) {
    console.log(`📡 Telnet connection to ${MAPSCII_HOST} closed (hadError: ${hadError})`);
    if (ws.readyState === WebSocket.OPEN) {
      console.log(`🔌 Closing WebSocket due to Telnet disconnect`);
      ws.close();
    }
  });
  
  // Handle timeout
  telnetSocket.on('timeout', function() {
    console.log(`⏰ Telnet connection timeout to ${MAPSCII_HOST}`);
    telnetSocket.destroy();
  });

  // Handle errors
  telnetSocket.on('error', function(err) {
    console.error(`❌ Telnet error: ${err.message}`);
    if (ws.readyState === WebSocket.OPEN) {
      ws.close();
    }
  });
  
  ws.on('error', function(err) {
    console.error(`❌ WebSocket error: ${err.message}`);
    if (telnetSocket.writable) {
      telnetSocket.end();
    }
  });
});

// Start the server
server.listen(WEBSOCKET_PORT, '0.0.0.0', function() {
  console.log(`🚀 Secure proxy server is running!`);
  console.log(`   HTTPS: https://localhost:${WEBSOCKET_PORT}`);
  console.log(`   WSS: wss://localhost:${WEBSOCKET_PORT}`);
  console.log('');
  console.log('🔒 Important: Accept the self-signed certificate');
  console.log(`   1. Visit: https://localhost:${WEBSOCKET_PORT}`);
  console.log('   2. Click "Advanced" → "Proceed to localhost"');
  console.log('   3. You should see a green "Certificate Accepted" page');
  console.log('   4. Then refresh your WordPress page');
  console.log('');
  console.log('   Press Ctrl+C to stop');
});

// Handle server errors
server.on('error', function(err) {
  if (err.code === 'EADDRINUSE') {
    console.error(`❌ Port ${WEBSOCKET_PORT} is already in use!`);
    console.error('   Try stopping other services or use a different port');
  } else if (err.code === 'EACCES') {
    console.error(`❌ Permission denied on port ${WEBSOCKET_PORT}`);
    console.error('   Try using a port above 1024 or run with sudo');
  } else {
    console.error(`❌ HTTPS server error: ${err.message}`);
  }
  process.exit(1);
});

wss.on('error', function(err) {
  console.error(`❌ WebSocket server error: ${err.message}`);
});

// Graceful shutdown
process.on('SIGINT', function() {
  console.log('\n🛑 Shutting down secure proxy server...');
  server.close(function() {
    console.log('✅ Secure proxy server stopped');
    process.exit(0);
  });
});
