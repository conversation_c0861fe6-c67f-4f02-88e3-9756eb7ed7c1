# Baum Terminal Activation Guide

Since this plugin is located in your theme directory rather than the standard WordPress plugins directory, you need to activate it manually.

## Quick Activation

Add this single line to your theme's `functions.php` file:

```php
// Activate Baum Terminal
require_once get_template_directory() . '/plugins/baum-terminal/init.php';
```

## Alternative: Direct Activation

If you prefer to activate the main plugin directly, add this to `functions.php`:

```php
// Activate Baum Terminal (direct)
require_once get_template_directory() . '/plugins/baum-terminal/baum-terminal.php';
```

## Verification

After adding either line to `functions.php`:

1. **Check Admin**: Go to WordPress Admin
2. **Look for Notice**: You should see a success notice about Baum Terminal being active
3. **Settings**: Go to Settings > Baum Terminal to configure
4. **Frontend**: Visit any page and look for the terminal button in bottom-left corner

## Configuration

Once activated:

1. Go to **Settings > Baum Terminal**
2. Add your **Claude API Key** (required)
3. Optionally add **Arya API Key** for dual AI support
4. Set **Rate Limits** (default: 20 requests/hour per user)
5. Choose **Default AI Model** (Claude or Arya)
6. Save settings

## Testing

1. Visit any page on your site
2. Look for floating terminal button in **bottom-left corner**
3. Click it - screen should go full black with terminal
4. Type `help` to see available commands
5. Press ESC or click × to exit

## Troubleshooting

### Button Not Appearing
- Check browser console for JavaScript errors
- Verify `functions.php` activation line was added correctly
- Clear any caching plugins

### 500 Errors on Assets
- Verify the plugin files are in the correct location
- Check file permissions (should be readable)
- Clear any caching

### API Errors
- Verify Claude API key is correct
- Check API key has proper permissions
- Test with simple message like "hello"

## File Structure

Your theme should have this structure:
```
wp-content/themes/baumpress/
├── functions.php (add activation line here)
├── plugins/
│   └── baum-terminal/
│       ├── baum-terminal.php (main plugin)
│       ├── init.php (activation helper)
│       ├── assets/
│       │   ├── terminal.css
│       │   └── terminal.js
│       ├── includes/
│       │   └── ascii-art-generator.php
│       ├── mcp/
│       │   ├── government-data.json
│       │   ├── news-aggregation.json
│       │   └── web-search.json
│       └── docs/
│           ├── ARYA-INTEGRATION.md
│           └── GOVERNMENT-DATA-INTEGRATION.md
```

## Success Indicators

When properly activated, you should see:
- ✅ Admin notice: "Baum Terminal is now active!"
- ✅ Settings page: Settings > Baum Terminal
- ✅ Floating button: Bottom-left corner on frontend
- ✅ Full-screen terminal: Click button to test

That's it! The terminal should now be fully functional with the macOS-style full-screen experience.
