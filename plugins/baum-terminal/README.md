# Baum Terminal

A WordPress plugin that provides a terminal-style interface for interacting with Claude AI, featuring web search and news aggregation capabilities.

## Features

- **Terminal Interface**: Full-screen black terminal with ASCII art site name
- **Claude AI Integration**: Chat with <PERSON> AI through a terminal interface
- **Web Search**: Search the web and get formatted results
- **News Aggregation**: Get news headlines and articles in monospace format
- **Rate Limiting**: Configurable rate limiting per user
- **Command History**: Navigate through previous commands
- **Keyboard Shortcuts**: Terminal-style keyboard navigation
- **Responsive Design**: Works on desktop and mobile devices

## Installation

1. Upload the `baum-terminal` folder to your `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to Settings > Baum Terminal to configure your API keys

## Configuration

### Claude API Setup

1. Get your Claude API key from [Anthropic](https://console.anthropic.com/)
2. Go to WordPress Admin > Settings > Baum Terminal
3. Enter your Claude API key
4. Select your preferred Claude model (Sonnet recommended for balance of speed/quality)
5. Configure rate limiting (default: 20 requests per hour per user)

### Optional: News API Setup

For enhanced news capabilities, you can add:
- [NewsAPI](https://newsapi.org/) key for comprehensive news search
- [NewsData.io](https://newsdata.io/) key for additional news sources

## Usage

### Opening the Terminal

- **Click** the terminal button in the bottom-right corner
- **Keyboard**: Press `Ctrl + ` ` (backtick) to toggle
- **Close**: Press `Escape` or click the red close button

### Available Commands

#### Built-in Commands
- `help` - Show available commands
- `clear` or `cls` - Clear the terminal
- `history` - Show command history
- `about` - Show terminal information
- `exit` or `quit` - Close terminal

#### AI-Powered Commands
- `what's the news in San Francisco?` - Get local news
- `search for latest tech trends` - Web search
- `latest headlines` - Current news headlines
- `breaking news` - Breaking news alerts

#### Example Prompts
```bash
user@site:~$ what's the news in New York?
user@site:~$ search for artificial intelligence developments
user@site:~$ latest tech headlines
user@site:~$ tell me about climate change
user@site:~$ what happened today in sports?
```

### Keyboard Shortcuts

- `Ctrl + ` ` - Toggle terminal
- `Escape` - Close terminal
- `Ctrl + L` - Clear terminal
- `↑/↓` - Navigate command history
- `Tab` - Auto-complete commands
- `Enter` - Execute command

## Scalability

This plugin is designed to handle **100+ concurrent users** by:

1. **Using Claude API**: Scales better than local Claude Code
2. **Rate Limiting**: Prevents API abuse (configurable per user)
3. **Caching**: Reduces API calls for repeated requests
4. **Efficient AJAX**: Non-blocking requests with proper error handling
5. **Session Management**: Per-user rate limiting using WordPress transients

### Rate Limiting Details

- Default: 20 requests per hour per user (IP-based)
- Configurable in admin settings
- Uses WordPress transients for efficient storage
- Automatic cleanup of expired limits

## API Costs Estimation

For 100 users with average usage:
- **Light usage** (5 requests/hour): ~$50-100/month
- **Medium usage** (15 requests/hour): ~$150-300/month  
- **Heavy usage** (20 requests/hour): ~$200-400/month

*Costs depend on Claude model chosen and request complexity*

## Customization

### Colors and Styling

Edit `assets/terminal.css` to customize:
- Terminal colors
- Font family/size
- Animation effects
- Responsive breakpoints

### ASCII Art

The site name ASCII art is auto-generated. To customize:
1. Edit the `generate_ascii_art()` method in `baum-terminal.php`
2. Use online ASCII art generators for complex designs

### MCP Configuration

The plugin includes MCP (Model Context Protocol) configurations:
- `mcp/web-search.json` - Web search capabilities
- `mcp/news-aggregation.json` - News aggregation settings

## Security Features

- **Nonce Verification**: All AJAX requests verified
- **Input Sanitization**: User input properly sanitized
- **Rate Limiting**: Prevents abuse and API overuse
- **IP-based Tracking**: Rate limits per IP address
- **Capability Checks**: Admin settings require proper permissions

## Troubleshooting

### Common Issues

1. **Terminal not opening**: Check for JavaScript errors in browser console
2. **API errors**: Verify Claude API key in settings
3. **Rate limit exceeded**: Wait or increase limit in settings
4. **Styling issues**: Check CSS file loading and theme compatibility

### Debug Mode

Add to `wp-config.php` for debugging:
```php
define('BAUM_TERMINAL_DEBUG', true);
```

## Requirements

- WordPress 5.0+
- PHP 7.4+
- jQuery (included with WordPress)
- FontAwesome (for terminal icon)
- Claude API key from Anthropic

## License

GPL v2 or later

## Support

For support and feature requests, visit the plugin documentation or contact the developer.

## Changelog

### 1.0.0
- Initial release
- Claude AI integration
- Terminal interface
- Web search capabilities
- News aggregation
- Rate limiting
- Command history
- Keyboard shortcuts
