# Arya by Gab Integration Guide

This guide explains how to integrate Arya by <PERSON>ab as an alternative AI model in the Baum Terminal, providing users with choice between Claude AI and Arya.

## 🤖 **Dual AI System**

The terminal now supports both:
- **Claude AI** - Anthrop<PERSON>'s advanced AI assistant
- **Arya by Gab** - <PERSON><PERSON>'s free speech-focused AI assistant

Users can switch between them seamlessly without losing their conversation context.

## 🔧 **Setup Instructions**

### 1. Get Arya API Access
1. Visit [Gab.com Developer Portal](https://developers.gab.com)
2. Create an account or sign in
3. Generate an API key for Arya
4. Note the API endpoint URL

### 2. Configure in WordPress
1. Go to **WordPress Admin > Settings > Baum Terminal**
2. Scroll to **"Arya by Gab Configuration"** section
3. Enter your **Arya API Key**
4. Set **Arya API URL** (default: `https://api.gab.com/v1/chat/completions`)
5. Choose **Arya Model** (arya-1, arya-2, or arya-pro)
6. Set **Default AI Model** (<PERSON> or <PERSON>rya)
7. Save settings

## 🎮 **How to Switch AI Models**

### Method 1: Dropdown Selector
- Use the dropdown in the terminal header
- Select "Claude AI" or "Arya by Gab"
- Switch happens immediately without clearing screen

### Method 2: Terminal Commands
```bash
user@site:~$ ai claude
[GREEN]Switched to Claude AI[/GREEN]
[CYAN]You can now chat with Claude AI[/CYAN]

user@site:~$ ai arya  
[GREEN]Switched to Arya by Gab[/GREEN]
[CYAN]You can now chat with Arya by Gab[/CYAN]
```

### Method 3: Tab Completion
- Type `ai ` and press Tab to see options
- Auto-completes to `ai claude` or `ai arya`

## 💬 **Usage Examples**

### Switching Between AIs
```bash
user@site:~$ Hello, who are you?
[Claude responds...]

user@site:~$ ai arya
[GREEN]Switched to Arya by Gab[/GREEN]

user@site:~$ Hello, who are you?
[Arya responds...]

user@site:~$ ai claude
[GREEN]Switched to Claude AI[/GREEN]
```

### Different AI Perspectives
```bash
# Ask Claude about a topic
user@site:~$ ai claude
user@site:~$ What do you think about free speech online?
[Claude's response...]

# Get Arya's perspective on the same topic
user@site:~$ ai arya
user@site:~$ What do you think about free speech online?
[Arya's response...]
```

## 🎨 **Visual Indicators**

### AI Status Display
- **● Claude Active** (green) - Claude AI is selected
- **● Arya Active** (cyan) - Arya by Gab is selected

### Terminal Prompt
The terminal prompt remains the same, but the AI status indicator shows which model is active.

### Color Coding
- **Green** - Claude AI responses and status
- **Cyan** - Arya by Gab responses and status
- **Yellow** - System messages and switching notifications

## ⚙️ **Configuration Options**

### Admin Settings

#### Default AI Model
Choose which AI loads by default when users open the terminal:
- **Claude AI** - Anthropic's assistant (default)
- **Arya by Gab** - Gab's free speech assistant

#### Arya API Settings
- **API Key** - Your Arya API key from Gab
- **API URL** - Arya API endpoint (usually `https://api.gab.com/v1/chat/completions`)
- **Model** - Arya model version (arya-1, arya-2, arya-pro)

#### Rate Limiting
Rate limits apply per AI model:
- Each AI has separate rate limit counters
- Switching AIs doesn't reset rate limits
- Configure limits in admin settings

## 🔒 **Security & Privacy**

### API Key Storage
- Arya API keys stored securely in WordPress options
- Keys encrypted in database
- HTTPS-only API communication

### Data Handling
- No conversation data stored permanently
- Each AI processes requests independently
- Rate limiting prevents abuse

### User Privacy
- IP-based rate limiting only
- No personal data collection
- Conversations not logged

## 💰 **Cost Considerations**

### Arya by Gab Pricing
- Check current pricing at [Gab.com](https://gab.com/pricing)
- May offer free tier or credits
- Pricing typically per request or token

### Dual AI Costs
- Users can choose more cost-effective option
- Distribute load between two providers
- Fallback option if one service is down

### Cost Optimization Tips
1. Set appropriate rate limits
2. Use Arya for general queries
3. Use Claude for complex analysis
4. Monitor usage in admin dashboard

## 🚀 **Advanced Features**

### Smart AI Routing
Future enhancement could include:
- Auto-route based on query type
- Load balancing between AIs
- Fallback to secondary AI if primary fails

### Conversation Context
- Each AI maintains separate context
- Switching preserves conversation history
- Users can compare responses

### Custom Prompts
- Different system prompts for each AI
- Arya optimized for free speech discussions
- Claude optimized for analytical tasks

## 🛠️ **Troubleshooting**

### Common Issues

#### "Arya API key not configured"
- Check API key in WordPress admin
- Verify key is valid and active
- Ensure proper permissions

#### "Failed to connect to Arya API"
- Check API URL is correct
- Verify network connectivity
- Check Gab.com service status

#### AI switching not working
- Clear browser cache
- Check JavaScript console for errors
- Verify nonce tokens are valid

### Debug Mode
Enable debug mode in `wp-config.php`:
```php
define('BAUM_TERMINAL_DEBUG', true);
```

## 📊 **Usage Analytics**

### Tracking AI Usage
Monitor which AI users prefer:
- Request counts per AI
- Response times comparison
- User satisfaction metrics
- Error rates per provider

### Admin Dashboard
Future enhancement could show:
- AI usage statistics
- Cost breakdown per AI
- Performance metrics
- User preferences

## 🔮 **Future Enhancements**

### Planned Features
1. **AI Comparison Mode** - Side-by-side responses
2. **Smart Routing** - Auto-select best AI for query type
3. **Custom AI Personalities** - User-defined system prompts
4. **Conversation Export** - Save chats with AI attribution
5. **Multi-AI Discussions** - Have AIs discuss topics together

### Integration Possibilities
- **Local AI Models** - Ollama, GPT4All integration
- **Other Providers** - OpenAI, Anthropic, Cohere
- **Specialized AIs** - Code-focused, creative writing, etc.

## 📚 **Resources**

### Documentation
- [Gab Developer Portal](https://developers.gab.com)
- [Arya API Documentation](https://docs.gab.com/arya-api)
- [Gab AI Pricing](https://gab.com/pricing)

### Community
- [Gab AI Community](https://gab.com/groups/ai)
- [BaumPress Support](https://baumpress.com/support)

### Code Examples
```javascript
// Switch AI programmatically
terminal.switchAI('arya');

// Get current AI
const currentAI = terminal.currentAI;

// Check AI availability
const isAryaConfigured = baumTerminal.availableAIs.arya;
```

This dual AI system gives users choice and flexibility while maintaining a seamless terminal experience. Users can leverage the strengths of both Claude AI and Arya by Gab depending on their needs and preferences!
