# Government Data Integration Guide

This guide explains how to integrate government data sources for geopolitics, economics, and demographics into the Baum Terminal.

## 🏛️ Available Data Sources

### USA Government APIs

#### 1. US Census Bureau API
- **URL**: `https://api.census.gov/data`
- **API Key**: Required (free)
- **Rate Limit**: 500 requests/day
- **Data**: Demographics, population, housing, economics

**Example Queries:**
```bash
user@site:~$ population growth in California
user@site:~$ demographic trends in Texas
user@site:~$ housing market data for Florida
```

**Setup:**
1. Register at: https://api.census.gov/data/key_signup.html
2. Add key to WordPress admin: Settings > Baum Terminal
3. Enable Census data in MCP settings

#### 2. Federal Reserve Economic Data (FRED)
- **URL**: `https://api.stlouisfed.org/fred`
- **API Key**: Required (free)
- **Rate Limit**: 120 requests/minute
- **Data**: Economic indicators, interest rates, employment

**Popular Series IDs:**
- `UNRATE` - Unemployment Rate
- `CPIAUCSL` - Consumer Price Index
- `GDP` - Gross Domestic Product
- `FEDFUNDS` - Federal Funds Rate

**Example Queries:**
```bash
user@site:~$ unemployment rates by state
user@site:~$ inflation trends over time
user@site:~$ GDP growth by region
user@site:~$ interest rate changes
```

#### 3. Bureau of Labor Statistics (BLS)
- **URL**: `https://api.bls.gov/publicAPI/v2`
- **API Key**: Required (free)
- **Rate Limit**: 500 requests/day
- **Data**: Employment, wages, prices, productivity

**Example Queries:**
```bash
user@site:~$ employment statistics for tech sector
user@site:~$ wage growth by industry
user@site:~$ consumer price trends
```

#### 4. State Department Travel Advisories
- **URL**: `https://travel.state.gov/content/travel/en/traveladvisories`
- **API Key**: Not required
- **Data**: Travel warnings, diplomatic status

**Example Queries:**
```bash
user@site:~$ travel advisories for Japan
user@site:~$ diplomatic status with China
user@site:~$ embassy locations in Europe
```

### UK Government APIs

#### 1. Office for National Statistics (ONS)
- **URL**: `https://api.ons.gov.uk`
- **API Key**: Not required
- **Rate Limit**: 20 requests/second
- **Data**: UK demographics, economy, society

**Example Queries:**
```bash
user@site:~$ UK population statistics
user@site:~$ British economic indicators
user@site:~$ unemployment in London
```

#### 2. Gov.UK APIs
- **URL**: `https://www.gov.uk/api`
- **API Key**: Not required
- **Data**: Various government services and data

## 🎨 ASCII Art Integration

The terminal now includes intelligent ASCII art generation that adapts to:

### 1. Site Name Art
- **Auto-sizing**: Chooses font based on name length
- **Caching**: Results cached for 1 hour
- **Styles**: Block, Banner, Small fonts available

### 2. Contextual Art
- **News**: Newspaper-style headers
- **Economic**: Dollar signs and charts
- **Search**: Magnifying glass themes
- **Weather**: Cloud and sun symbols
- **Error/Success**: Appropriate indicators

### 3. Data Visualization
- **ASCII Charts**: Bar charts from numeric data
- **ASCII Maps**: Geographic data representation
- **Tables**: Formatted data tables

## 📊 Terminal Data Display

### Economic Data Formatting
```
╔═══════════════════════════════════════╗
║           UNEMPLOYMENT RATES          ║
╠═══════════════════════════════════════╣
║ California    │ 4.2% ████████░░       ║
║ Texas         │ 3.8% ███████░░░       ║
║ New York      │ 4.5% █████████░       ║
║ Florida       │ 3.1% ██████░░░░       ║
╚═══════════════════════════════════════╝
```

### News Headlines
```
[CYAN]═══════════════ BREAKING NEWS ═══════════════[/CYAN]

[YELLOW]• Federal Reserve Raises Interest Rates[/YELLOW]
  [GRAY]Source: Reuters | 2 hours ago[/GRAY]

[YELLOW]• GDP Growth Exceeds Expectations[/YELLOW]
  [GRAY]Source: Bloomberg | 4 hours ago[/GRAY]
```

### Geographic Data
```
POPULATION DENSITY BY STATE
California  ████████████████████ 254/sq mi
Texas       ██████████░░░░░░░░░░ 109/sq mi
Florida     ███████████████░░░░░ 397/sq mi
New York    ████████████████████ 421/sq mi
```

## 🔧 Setup Instructions

### 1. API Keys Configuration
Add these to your WordPress admin (Settings > Baum Terminal):

```php
// US Census Bureau
CENSUS_API_KEY=your_census_key_here

// Federal Reserve FRED
FRED_API_KEY=your_fred_key_here

// Bureau of Labor Statistics
BLS_API_KEY=your_bls_key_here

// News API (optional)
NEWS_API_KEY=your_news_key_here
```

### 2. Enable Government Data Features
In the admin settings:
- ✅ Enable Economic Data
- ✅ Enable Demographic Data
- ✅ Enable Geopolitical Data
- ✅ Enable ASCII Charts
- ✅ Enable Data Suggestions

### 3. Configure Rate Limits
Recommended settings:
- **Government APIs**: 50 requests/hour per user
- **News APIs**: 30 requests/hour per user
- **General Chat**: 20 requests/hour per user

## 💡 Example Conversations

### Economic Analysis
```bash
user@site:~$ unemployment rates by state
[CYAN]Fetching latest unemployment data...[/CYAN]

Current Unemployment Rates (Seasonally Adjusted):
California: 4.2% (↑0.1% from last month)
Texas: 3.8% (↓0.2% from last month)
New York: 4.5% (→ no change)
National Average: 3.9%

[ASCII CHART SHOWING TRENDS]

user@site:~$ inflation trends over time
[CYAN]Analyzing inflation data from Federal Reserve...[/CYAN]

Consumer Price Index (Last 12 Months):
Current: 3.2% annual inflation
Peak: 9.1% (June 2022)
Target: 2.0% (Federal Reserve goal)

[ASCII CHART SHOWING HISTORICAL TRENDS]
```

### Geopolitical Information
```bash
user@site:~$ travel advisories for Japan
[CYAN]Checking State Department advisories...[/CYAN]

TRAVEL ADVISORY: JAPAN
Level: 1 - Exercise Normal Precautions
Last Updated: March 15, 2024

Summary: Exercise normal precautions when traveling to Japan.

Key Points:
• No significant security concerns
• Standard health precautions recommended
• Embassy: Tokyo, Osaka consulates available
```

### Demographic Data
```bash
user@site:~$ population growth in Texas
[CYAN]Retrieving Census Bureau data...[/CYAN]

TEXAS POPULATION TRENDS
Current Population: 30.5 million (2023 est.)
Growth Rate: ****% annually
Net Migration: +230,000 people/year

Top Growing Cities:
Austin: ****% annually
Houston: ****% annually
Dallas: ****% annually

[ASCII MAP SHOWING GROWTH PATTERNS]
```

## 🚀 Advanced Features

### 1. Intelligent Suggestions
The terminal now provides contextual suggestions:
- Economic topics when discussing finance
- Geographic data when mentioning locations
- Political information for international queries

### 2. Data Caching
- Government data cached for 1 hour
- ASCII art cached for 1 hour
- News data cached for 15 minutes

### 3. Error Handling
- API failures gracefully handled
- Fallback to cached data when available
- Clear error messages for users

### 4. Mobile Optimization
- Responsive ASCII art sizing
- Touch-friendly interface
- Optimized data display for small screens

## 📈 Cost Estimation

### Government APIs (Free Tiers)
- Census Bureau: Free (500 requests/day)
- Federal Reserve: Free (120 requests/minute)
- BLS: Free (500 requests/day)
- State Department: Free (no limits)

### Claude API Costs (for 100 users)
- Light usage: $50-100/month
- Medium usage: $150-300/month
- Heavy usage: $200-400/month

### Total Monthly Cost
- **Government data**: $0 (free tiers)
- **Claude API**: $50-400/month
- **Total**: $50-400/month for 100 users

## 🔒 Security & Privacy

### Data Handling
- No personal data stored
- Government data cached temporarily
- Rate limiting prevents abuse
- IP-based tracking only

### API Security
- Keys stored securely in WordPress options
- HTTPS-only API calls
- Input sanitization and validation
- Nonce verification for all requests

## 📚 Resources

### API Documentation
- [Census Bureau API](https://www.census.gov/data/developers/data-sets.html)
- [Federal Reserve FRED API](https://fred.stlouisfed.org/docs/api/fred/)
- [Bureau of Labor Statistics API](https://www.bls.gov/developers/api_signature_v2.htm)
- [UK ONS API](https://developer.ons.gov.uk/)

### ASCII Art Libraries
- [figlet.js](https://github.com/patorjk/figlet.js/) - JavaScript ASCII art
- [ascii-art](https://github.com/khrome/ascii-art) - Node.js ASCII art
- [art-template](https://github.com/aui/art-template) - Template-based art

This integration transforms your terminal into a powerful tool for exploring government data, economic trends, and geopolitical information with beautiful ASCII visualizations!
