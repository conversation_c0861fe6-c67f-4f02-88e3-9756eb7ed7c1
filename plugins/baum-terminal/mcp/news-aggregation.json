{"name": "news-aggregation", "version": "1.0.0", "description": "News aggregation and reading capabilities for Baum Terminal", "capabilities": {"get_headlines": {"description": "Get latest news headlines", "parameters": {"category": {"type": "string", "enum": ["general", "business", "entertainment", "health", "science", "sports", "technology"], "default": "general", "description": "News category"}, "country": {"type": "string", "description": "Country code (e.g., 'us', 'uk', 'ca')", "default": "us"}, "sources": {"type": "array", "items": {"type": "string"}, "description": "Specific news sources to include"}, "limit": {"type": "integer", "minimum": 1, "maximum": 20, "default": 10, "description": "Number of headlines to return"}}}, "search_news": {"description": "Search for news articles", "parameters": {"query": {"type": "string", "description": "Search query", "required": true}, "from_date": {"type": "string", "format": "date", "description": "Start date for search (YYYY-MM-DD)"}, "to_date": {"type": "string", "format": "date", "description": "End date for search (YYYY-MM-DD)"}, "sort_by": {"type": "string", "enum": ["relevancy", "popularity", "publishedAt"], "default": "publishedAt", "description": "Sort order"}}}, "get_local_news": {"description": "Get news for a specific location", "parameters": {"location": {"type": "string", "description": "City, state, or country", "required": true}, "radius": {"type": "integer", "description": "Search radius in kilometers", "default": 50}}}}, "tools": [{"name": "fetch_headlines", "description": "Fetch latest news headlines", "input_schema": {"type": "object", "properties": {"category": {"type": "string", "enum": ["general", "business", "entertainment", "health", "science", "sports", "technology"], "default": "general"}, "country": {"type": "string", "default": "us"}, "limit": {"type": "integer", "minimum": 1, "maximum": 20, "default": 10}}}}, {"name": "search_news_articles", "description": "Search for specific news articles", "input_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query"}, "language": {"type": "string", "default": "en"}, "sort_by": {"type": "string", "enum": ["relevancy", "popularity", "publishedAt"], "default": "publishedAt"}, "page_size": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, "required": ["query"]}}, {"name": "get_article_content", "description": "Get full content of a news article", "input_schema": {"type": "object", "properties": {"url": {"type": "string", "description": "Article URL"}, "format": {"type": "string", "enum": ["text", "markdown", "html"], "default": "text"}}, "required": ["url"]}}, {"name": "get_location_news", "description": "Get news for a specific geographic location", "input_schema": {"type": "object", "properties": {"location": {"type": "string", "description": "Location (city, state, country)"}, "category": {"type": "string", "enum": ["general", "business", "entertainment", "health", "science", "sports", "technology"], "default": "general"}}, "required": ["location"]}}], "prompts": [{"name": "daily_briefing", "description": "Generate a daily news briefing", "arguments": [{"name": "location", "description": "Location for local news", "required": false}, {"name": "interests", "description": "Topics of interest", "required": false}]}, {"name": "breaking_news_alert", "description": "Format breaking news for terminal display", "arguments": [{"name": "urgency", "description": "Urgency level (low, medium, high)", "required": true}]}], "resources": [{"uri": "news://headlines", "name": "News Headlines", "description": "Access to current news headlines", "mimeType": "application/json"}, {"uri": "news://search", "name": "News Search", "description": "Search news articles", "mimeType": "application/json"}], "server": {"command": "node", "args": ["news-server.js"], "env": {"NEWS_API_KEY": "${NEWS_API_KEY}", "NEWSDATA_API_KEY": "${NEWSDATA_API_KEY}"}}, "settings": {"sources": {"primary": ["bbc-news", "cnn", "reuters", "associated-press", "the-new-york-times", "the-washington-post"], "tech": ["techcrunch", "ars-technica", "the-verge", "wired", "hacker-news"], "business": ["bloomberg", "financial-times", "wall-street-journal", "cnbc"]}, "rate_limit": {"requests_per_minute": 15, "requests_per_hour": 200}, "cache": {"enabled": true, "headlines_ttl": 300, "articles_ttl": 1800}, "formatting": {"terminal_width": 80, "monospace": true, "colors": {"headline": "cyan", "source": "green", "timestamp": "gray", "breaking": "red"}}}}