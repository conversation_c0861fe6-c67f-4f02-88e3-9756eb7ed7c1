{"name": "web-search", "version": "1.0.0", "description": "Web search capabilities for Baum Terminal", "capabilities": {"search": {"description": "Search the web for information", "parameters": {"query": {"type": "string", "description": "Search query", "required": true}, "num_results": {"type": "integer", "description": "Number of results to return", "default": 5, "minimum": 1, "maximum": 10}, "safe_search": {"type": "boolean", "description": "Enable safe search", "default": true}}}, "get_page_content": {"description": "Get content from a specific webpage", "parameters": {"url": {"type": "string", "description": "URL to fetch content from", "required": true}, "extract_text": {"type": "boolean", "description": "Extract only text content", "default": true}}}}, "tools": [{"name": "search_web", "description": "Search the web using various search engines", "input_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "The search query"}, "engine": {"type": "string", "enum": ["google", "bing", "duckduck<PERSON>"], "default": "google", "description": "Search engine to use"}, "num_results": {"type": "integer", "minimum": 1, "maximum": 10, "default": 5}}, "required": ["query"]}}, {"name": "fetch_webpage", "description": "Fetch and extract content from a webpage", "input_schema": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to fetch"}, "extract_main_content": {"type": "boolean", "default": true, "description": "Extract only main content, removing navigation and ads"}}, "required": ["url"]}}], "prompts": [{"name": "search_and_summarize", "description": "Search for information and provide a summary", "arguments": [{"name": "topic", "description": "Topic to search for", "required": true}]}], "resources": [{"uri": "search://web", "name": "Web Search", "description": "Access to web search functionality", "mimeType": "application/json"}], "server": {"command": "node", "args": ["web-search-server.js"], "env": {"SEARCH_API_KEY": "${SEARCH_API_KEY}", "SEARCH_ENGINE_ID": "${SEARCH_ENGINE_ID}"}}, "settings": {"rate_limit": {"requests_per_minute": 10, "requests_per_hour": 100}, "cache": {"enabled": true, "ttl": 3600}, "filters": {"safe_search": true, "language": "en", "region": "us"}}}