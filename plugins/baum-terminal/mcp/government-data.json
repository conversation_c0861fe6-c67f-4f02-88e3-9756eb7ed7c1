{"name": "government-data", "version": "1.0.0", "description": "Government data sources for geopolitics, economics, and public information", "capabilities": {"census_data": {"description": "Access US Census Bureau data", "parameters": {"dataset": {"type": "string", "enum": ["population", "demographics", "economics", "housing"], "description": "Type of census data"}, "geography": {"type": "string", "description": "Geographic area (state, county, city)"}, "year": {"type": "integer", "description": "Data year", "minimum": 2010, "maximum": 2024}}}, "economic_indicators": {"description": "Federal Reserve and BLS economic data", "parameters": {"indicator": {"type": "string", "enum": ["unemployment", "inflation", "gdp", "interest_rates", "consumer_price_index"], "description": "Economic indicator type"}, "period": {"type": "string", "enum": ["monthly", "quarterly", "yearly"], "default": "monthly"}, "region": {"type": "string", "description": "Geographic region or 'national'"}}}, "geopolitical_data": {"description": "State Department and international data", "parameters": {"data_type": {"type": "string", "enum": ["travel_advisories", "diplomatic_relations", "trade_data", "sanctions"], "description": "Type of geopolitical data"}, "country": {"type": "string", "description": "Country code or name"}}}, "uk_government_data": {"description": "UK ONS and gov.uk data", "parameters": {"dataset": {"type": "string", "enum": ["population", "economy", "crime", "health", "education"], "description": "UK government dataset"}, "region": {"type": "string", "description": "UK region or nation"}}}}, "tools": [{"name": "get_census_data", "description": "Fetch US Census Bureau data", "input_schema": {"type": "object", "properties": {"dataset": {"type": "string", "description": "Census dataset identifier"}, "variables": {"type": "array", "items": {"type": "string"}, "description": "Variables to retrieve"}, "geography": {"type": "string", "description": "Geographic level (state, county, etc.)"}}, "required": ["dataset", "variables", "geography"]}}, {"name": "get_economic_data", "description": "Fetch Federal Reserve economic data", "input_schema": {"type": "object", "properties": {"series_id": {"type": "string", "description": "FRED series identifier"}, "start_date": {"type": "string", "format": "date"}, "end_date": {"type": "string", "format": "date"}}, "required": ["series_id"]}}, {"name": "get_travel_advisories", "description": "Get State Department travel advisories", "input_schema": {"type": "object", "properties": {"country": {"type": "string", "description": "Country name or code"}}}}, {"name": "get_uk_statistics", "description": "Fetch UK ONS statistics", "input_schema": {"type": "object", "properties": {"dataset": {"type": "string", "description": "ONS dataset identifier"}, "dimensions": {"type": "object", "description": "Dataset dimensions and filters"}}, "required": ["dataset"]}}], "data_sources": {"usa": {"census": {"base_url": "https://api.census.gov/data", "key_required": true, "rate_limit": "500/day", "datasets": ["2021/acs/acs1", "2020/dec/pl", "timeseries/poverty/saipe"]}, "federal_reserve": {"base_url": "https://api.stlouisfed.org/fred", "key_required": true, "rate_limit": "120/minute", "popular_series": ["UNRATE", "CPIAUCSL", "GDP", "FEDFUNDS"]}, "bls": {"base_url": "https://api.bls.gov/publicAPI/v2", "key_required": true, "rate_limit": "500/day"}, "state_dept": {"base_url": "https://travel.state.gov/content/travel/en/traveladvisories", "key_required": false, "format": "json"}}, "uk": {"ons": {"base_url": "https://api.ons.gov.uk", "key_required": false, "rate_limit": "20/second"}, "gov_uk": {"base_url": "https://www.gov.uk/api", "key_required": false}}}, "terminal_formatting": {"charts": {"ascii_charts": true, "max_width": 80, "colors": {"positive": "green", "negative": "red", "neutral": "cyan", "highlight": "yellow"}}, "tables": {"max_columns": 6, "truncate_long_values": true, "alignment": "left"}, "maps": {"ascii_maps": true, "symbols": {"high": "█", "medium": "▓", "low": "░", "none": " "}}}, "suggestions": {"economic_topics": ["unemployment rates by state", "inflation trends over time", "GDP growth by region", "interest rate changes", "consumer spending patterns"], "geopolitical_topics": ["travel advisories for [country]", "trade relationships with [country]", "diplomatic status updates", "international sanctions list", "embassy locations and contacts"], "demographic_topics": ["population growth by city", "age distribution trends", "income inequality statistics", "education levels by region", "housing market data"]}, "prompts": [{"name": "economic_briefing", "description": "Generate economic briefing with charts", "template": "Show me the latest economic indicators including unemployment, inflation, and GDP growth. Format as terminal-friendly charts and highlight key trends."}, {"name": "geopolitical_update", "description": "Get geopolitical situation for a region", "template": "What's the current geopolitical situation for {region}? Include travel advisories, trade status, and diplomatic relations."}, {"name": "demographic_analysis", "description": "Analyze demographic trends", "template": "Analyze demographic trends for {location} including population, age distribution, and economic indicators."}]}