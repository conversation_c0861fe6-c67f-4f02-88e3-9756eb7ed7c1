#!/usr/bin/env node

/**
 * MapSCII WebSocket-to-Telnet Proxy
 * Bridges WebSocket connections from browsers to mapscii.me Telnet server
 */

const WebSocket = require('ws');
const net = require('net');

const WEBSOCKET_PORT = 8080;
const MAPSCII_HOST = 'mapscii.me';
const MAPSCII_PORT = 23;

console.log('🗺️  MapSCII WebSocket Proxy');
console.log('============================');
console.log(`📡 WebSocket Server: ws://localhost:${WEBSOCKET_PORT}`);
console.log(`🎯 Target Server: ${MAPSCII_HOST}:${MAPSCII_PORT}`);
console.log('');

// Create WebSocket server
const wss = new WebSocket.Server({ 
  port: WEBSOCKET_PORT,
  perMessageDeflate: false
});

console.log(`✅ WebSocket server listening on port ${WEBSOCKET_PORT}`);
console.log('💡 Refresh your browser page to connect');
console.log('');

wss.on('connection', function connection(ws, req) {
  const clientIP = req.socket.remoteAddress;
  console.log(`🔗 New WebSocket connection from ${clientIP}`);
  
  // Create Telnet connection to mapscii.me
  const telnetSocket = new net.Socket();
  
  // Connect to mapscii.me
  telnetSocket.connect(MAPSCII_PORT, MAPSCII_HOST, function() {
    console.log(`📡 Connected to ${MAPSCII_HOST}:${MAPSCII_PORT}`);
  });
  
  // Forward data from WebSocket to Telnet
  ws.on('message', function message(data) {
    if (telnetSocket.writable) {
      telnetSocket.write(data);
    }
  });
  
  // Forward data from Telnet to WebSocket
  telnetSocket.on('data', function(data) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(data);
    }
  });
  
  // Handle WebSocket close
  ws.on('close', function close() {
    console.log(`❌ WebSocket connection closed from ${clientIP}`);
    if (telnetSocket.writable) {
      telnetSocket.end();
    }
  });
  
  // Handle Telnet close
  telnetSocket.on('close', function() {
    console.log(`📡 Telnet connection to ${MAPSCII_HOST} closed`);
    if (ws.readyState === WebSocket.OPEN) {
      ws.close();
    }
  });
  
  // Handle errors
  telnetSocket.on('error', function(err) {
    console.error(`❌ Telnet error: ${err.message}`);
    if (ws.readyState === WebSocket.OPEN) {
      ws.close();
    }
  });
  
  ws.on('error', function(err) {
    console.error(`❌ WebSocket error: ${err.message}`);
    if (telnetSocket.writable) {
      telnetSocket.end();
    }
  });
});

// Handle server errors
wss.on('error', function(err) {
  console.error(`❌ WebSocket server error: ${err.message}`);
});

// Graceful shutdown
process.on('SIGINT', function() {
  console.log('\n🛑 Shutting down proxy server...');
  wss.close(function() {
    console.log('✅ Proxy server stopped');
    process.exit(0);
  });
});

console.log('🚀 Proxy server is running!');
console.log('   Press Ctrl+C to stop');
