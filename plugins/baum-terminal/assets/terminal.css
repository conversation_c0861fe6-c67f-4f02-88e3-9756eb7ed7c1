/* Baum Terminal Styles */

/* Floating Terminal Button - Bottom Left */
.baum-terminal-button {
  position: fixed;
  bottom: 20px;
  left: 20px;
  width: 60px;
  height: 60px;
  background: #1d1d1d;
  border: 1px solid #3c3c3c;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 9998;
  transition: all 0.2s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
}

.baum-terminal-button:hover {
  background: #2d2d2d;
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.5);
}

.baum-terminal-button i {
  color: #ffffff;
  font-size: 24px;
}

/* Full Screen Terminal Overlay */
.baum-terminal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000000;
  z-index: 99999;
  display: none;
  opacity: 0;
  transition: opacity 0.2s ease;
  overflow: hidden;
}

.baum-terminal-overlay.active {
  display: block;
  opacity: 1;
}

/* macOS Terminal Container - Full Screen */
.baum-terminal-container {
  width: 100%;
  height: 100%;
  background: #000000;
  display: flex;
  flex-direction: column;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
  font-size: 14px;
  line-height: 1.2;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0;
}

/* Exit Button - Top Right Corner */
.baum-terminal-exit {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 30px;
  height: 30px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 100000;
  transition: all 0.2s ease;
  font-size: 16px;
  color: #ffffff;
}

.baum-terminal-exit:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* macOS Terminal Header - Hidden for full screen experience */
.baum-terminal-header {
  display: none;
}

/* macOS Terminal Content - Full Screen */
.baum-terminal-content {
  flex: 1;
  padding: 30px 40px;
  overflow-y: auto;
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.4;
  color: #ffffff;
  background: #000000;
  min-height: 100vh;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0;
}

/* ASCII Art - macOS Terminal Style */
.baum-terminal-ascii {
  margin-bottom: 20px;
  margin-top: 0;
}

.baum-terminal-ascii pre {
  color: #54C7FC;
  font-size: 14px;
  font-weight: normal;
  margin: 0;
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  white-space: pre;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0;
}

/* Welcome Message - macOS Terminal Style */
.baum-terminal-welcome {
  margin-bottom: 30px;
}

.baum-terminal-welcome p {
  margin: 8px 0;
  font-size: 13px;
}

/* AI Selector - Minimal macOS Style */
.baum-terminal-ai-selector {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
  font-size: 12px;
}

.baum-terminal-ai-label {
  color: #ffffff;
  font-weight: normal;
}

.baum-terminal-select {
  background: #000000 !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
  padding: 4px 8px;
  border-radius: 0;
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  font-size: 12px;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  box-shadow: none !important;
}

.baum-terminal-select, 
.baum-terminal-select:focus {
  outline: none !important;
  /* border-color: #007AFF !important; */
  background: #000000 !important;
  color: #ffffff !important;
  box-shadow: none !important;
}

.baum-terminal-select option {
  background: #000000 !important;
  color: #ffffff !important;
  padding: 4px 8px;
}

.baum-terminal-select option:hover {
  background: #333333 !important;
  color: #ffffff !important;
}

.baum-terminal-select option:selected {
  background: #007AFF !important;
  color: #ffffff !important;
}
}

.baum-terminal-ai-status {
  font-size: 12px;
  font-weight: normal;
  color: #ffffff;
}

/* macOS Terminal Colors */
.baum-terminal-green {
  color: #28CA42;
}

.baum-terminal-cyan {
  color: #54C7FC;
}

.baum-terminal-yellow {
  color: #F4BF75;
}

.baum-terminal-red {
  color: #FF6B68;
}

.baum-terminal-blue {
  color: #007AFF;
}

.baum-terminal-magenta {
  color: #FF2D92;
}

.baum-terminal-gray {
  color: #8E8E93;
}

.baum-terminal-white {
  color: #ffffff;
}

/* Terminal Output */
.baum-terminal-output {
  margin-bottom: 30px;
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0;
}

.baum-terminal-line {
  margin: 0;
  font-family: inherit;
  white-space: pre;
  overflow-x: auto;
  width: 80ch;
  max-width: 80ch;
}

.baum-terminal-line pre {
  margin: 0;
  padding: 0;
  font-family: inherit;
  white-space: pre;
  overflow-x: auto;
  width: 80ch;
  max-width: 80ch;
}

/* Animation container */
.baum-terminal-animation {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}

.baum-terminal-animation pre {
  margin: 0;
  padding: 0;
  font-family: inherit;
  white-space: pre;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

/* Live/Real-time elements */
.baum-terminal-live-chart,
.baum-terminal-live-drawing,
.baum-terminal-live-monitor {
  margin: 10px 0;
  padding: 10px;
  border-left: 3px solid #007AFF;
  background: rgba(0, 122, 255, 0.1);
}

.baum-terminal-live-chart pre,
.baum-terminal-live-drawing pre,
.baum-terminal-live-monitor pre {
  margin: 0;
  padding: 0;
  font-family: inherit;
  white-space: pre;
  color: #ffffff;
}

.baum-terminal-user-input {
  color: #ffffff;
  font-family: inherit;
}

.baum-terminal-user-input::before {
  content: "user@terminal:~$ ";
  color: #28CA42;
  font-family: inherit;
}

.baum-terminal-response {
  color: #ffffff;
  margin-left: 0;
  white-space: pre-wrap;
  font-family: inherit;
}

.baum-terminal-error {
  color: #FF6B68;
  font-family: inherit;
}

.baum-terminal-help {
  color: #F4BF75;
  font-family: inherit;
}

.baum-terminal-timestamp {
  color: #8E8E93;
  font-size: 12px;
  margin-left: 10px;
  font-family: inherit;
}

/* macOS Terminal Input Line */
.baum-terminal-input-line input, 
.baum-terminal-input-line {
  /* display: flex; */
  align-items: flex-start;
  margin-top: 0px;
  position: relative;
  min-height: 10px;
  background: #000000 !important;
  border: none !important; 
  padding: 0px;
}

.baum-terminal-prompt {
  color: #28CA42;
  font-weight: normal;
  white-space: nowrap;
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  font-size: 14px;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0;
  line-height: 1.4;
  margin-right: 0;
}

/* Visible terminal input - using regular input field */
.baum-terminal-input {
  flex: 1;
  background: #000000 !important;
  border: 1px solid #333333 !important;
  outline: none !important;
  color: #ffffff !important;
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  font-size: 14px;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0;
  line-height: 1.4;
  caret-color: #ffffff !important;
  padding: 4px 8px;
  margin: 0;
  border-radius: 0;
  box-shadow: none !important;
}

.baum-terminal-input:focus {
  background: #000000 !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.baum-terminal-input::placeholder {
  color: #8E8E93 !important;
  font-family: inherit;
}

/* Override any theme or browser defaults */
.baum-terminal-input,
.baum-terminal-input:focus,
.baum-terminal-input:active,
.baum-terminal-input:hover {
  background-color: #000000 !important;
  background: #000000 !important;
  color: #ffffff !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
}

.baum-terminal-input:focus {
  border-color: none !important;
}

/* Dropdown styling overrides */
.baum-terminal-select,
.baum-terminal-select:focus,
.baum-terminal-select:active,
.baum-terminal-select:hover {
  background-color: #000000 !important;
  background: #000000 !important;
  color: #ffffff !important;
  border: 1px solid #333333 !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
}

.baum-terminal-select:focus {
  border-color: #007AFF !important;
}

/* Loading Animation */
.baum-terminal-loading {
  color: #00ff00;
}

.baum-terminal-loading::after {
  content: '';
  animation: baum-terminal-blink 1s infinite;
}

@keyframes baum-terminal-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.baum-terminal-cursor {
  display: inline-block;
  width: 8px;
  height: 14px;
  background: #00ff00;
  animation: baum-terminal-cursor-blink 1s infinite;
  margin-left: 2px;
}

@keyframes baum-terminal-cursor-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* macOS Terminal Scrollbar */
.baum-terminal-content::-webkit-scrollbar {
  width: 12px;
}

.baum-terminal-content::-webkit-scrollbar-track {
  background: #000000;
}

.baum-terminal-content::-webkit-scrollbar-thumb {
  background: #333333;
  border-radius: 6px;
  border: 2px solid #000000;
}

.baum-terminal-content::-webkit-scrollbar-thumb:hover {
  background: #555555;
}

/* News and Search Specific Styles */
.baum-terminal-news-header {
  color: #ffff00;
  font-weight: bold;
  border-bottom: 1px solid #333;
  padding-bottom: 5px;
  margin-bottom: 10px;
}

.baum-terminal-news-item {
  margin-bottom: 15px;
  padding-left: 10px;
  border-left: 2px solid #00ff00;
}

.baum-terminal-news-title {
  color: #00ffff;
  font-weight: bold;
}

.baum-terminal-news-summary {
  color: #fff;
  margin-top: 5px;
}

.baum-terminal-news-meta {
  color: #808080;
  font-size: 12px;
  margin-top: 5px;
}

.baum-terminal-search-result {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #333;
  border-radius: 4px;
}

.baum-terminal-search-title {
  color: #00ffff;
  font-weight: bold;
}

.baum-terminal-search-url {
  color: #00ff00;
  font-size: 12px;
}

.baum-terminal-search-snippet {
  color: #fff;
  margin-top: 5px;
}

/* Rate Limit Warning */
.baum-terminal-rate-limit {
  color: #ff0000;
  background: rgba(255, 0, 0, 0.1);
  padding: 10px;
  border: 1px solid #ff0000;
  border-radius: 4px;
  margin: 10px 0;
}

/* Mobile Responsive - Full Screen Terminal */
@media (max-width: 768px) {
  .baum-terminal-content {
    padding: 15px 20px;
    font-size: 12px;
  }

  .baum-terminal-button {
    width: 50px;
    height: 50px;
    bottom: 15px;
    left: 15px;
  }

  .baum-terminal-button i {
    font-size: 20px;
  }

  .baum-terminal-input {
    font-size: 12px;
  }

  .baum-terminal-prompt {
    font-size: 12px;
  }
}

/* Full Screen Terminal Animation */
.baum-terminal-overlay {
  animation: baum-terminal-fade-in 0.2s ease-out;
}

@keyframes baum-terminal-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Remove old cursor styles - using native caret now */

/* Ensure all terminal elements use monospace */
.baum-terminal-content,
.baum-terminal-content *,
.baum-terminal-line,
.baum-terminal-line *,
.baum-terminal-response,
.baum-terminal-response *,
.baum-terminal-user-input,
.baum-terminal-user-input *,
.baum-terminal-ascii,
.baum-terminal-ascii *,
.baum-terminal-welcome,
.baum-terminal-welcome * {
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace !important;
  font-variant-ligatures: none !important;
  font-feature-settings: "liga" 0 !important;
}

/* Text selection */
.baum-terminal-content {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.baum-terminal-prompt {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Full screen body lock */
body.baum-terminal-active {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}

/* Command History */
.baum-terminal-history-item {
  opacity: 0.7;
}

.baum-terminal-history-item:hover {
  opacity: 1;
  cursor: pointer;
}

/* Status Indicators */
.baum-terminal-status {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 12px;
}

.baum-terminal-status-online {
  color: #00ff00;
}

.baum-terminal-status-offline {
  color: #ff0000;
}

.baum-terminal-status-connecting {
  color: #ffff00;
}
