/**
 * Mapscii Integration Styles
 * Terminal-style interactive map interface
 */

/* Modal Overlay */
.baum-mapscii-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(5px);
  z-index: 999999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
}

/* Modal Content */
.baum-mapscii-modal-content {
  background: #000000;
  border: 2px solid #333333;
  border-radius: 8px;
  width: 90vw;
  height: 90vh;
  max-width: 1200px;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  color: #ffffff;
  overflow: hidden;
}

/* Modal Header */
.baum-mapscii-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #333333;
  background: #111111;
}

.baum-mapscii-header h3 {
  margin: 0;
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
}

.baum-mapscii-close {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.baum-mapscii-close:hover {
  background: #333333;
}

/* Controls Section */
.baum-mapscii-controls {
  padding: 15px 20px;
  border-bottom: 1px solid #333333;
  background: #111111;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.baum-mapscii-search {
  display: flex;
  gap: 10px;
  flex: 1;
  min-width: 300px;
}

.baum-mapscii-search input {
  flex: 1;
  background: #000000;
  border: 1px solid #333333;
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
}

.baum-mapscii-search input:focus {
  outline: none;
  border-color: #007AFF;
}

.baum-mapscii-search input::placeholder {
  color: #888888;
}

.baum-mapscii-search button,
.baum-mapscii-actions button {
  background: #007AFF;
  border: none;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.baum-mapscii-search button:hover,
.baum-mapscii-actions button:hover {
  background: #0056CC;
}

.baum-btn-secondary {
  background: #333333 !important;
}

.baum-btn-secondary:hover {
  background: #555555 !important;
}

.baum-mapscii-coordinates {
  display: flex;
  gap: 20px;
  color: #00FF00;
  font-weight: 500;
  font-size: 14px;
}

.baum-mapscii-actions {
  display: flex;
  gap: 10px;
}

/* Map Container */
.baum-mapscii-container {
  flex: 1;
  background: #000000;
  padding: 20px;
  overflow: auto;
  position: relative;
}

/* Mapscii Canvas Styling */
.baum-mapscii-container canvas {
  background: #000000 !important;
  border: 1px solid #333333;
  border-radius: 4px;
  width: 100%;
  height: 100%;
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
}

/* Instructions */
.baum-mapscii-instructions {
  padding: 15px 20px;
  border-top: 1px solid #333333;
  background: #111111;
  font-size: 12px;
  color: #cccccc;
}

.baum-mapscii-instructions p {
  margin: 0 0 8px 0;
  font-weight: 600;
}

.baum-mapscii-instructions ul {
  margin: 0;
  padding-left: 20px;
}

.baum-mapscii-instructions li {
  margin: 4px 0;
}

/* Error State */
.baum-mapscii-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #ffffff;
}

.baum-mapscii-error h4 {
  color: #FF6B6B;
  margin: 0 0 10px 0;
  font-size: 18px;
}

.baum-mapscii-error p {
  margin: 5px 0;
  color: #cccccc;
}

.baum-manual-coordinates {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #333333;
  border-radius: 4px;
  background: #111111;
}

.baum-manual-coordinates label {
  display: block;
  margin: 10px 0;
  color: #ffffff;
}

.baum-manual-coordinates input {
  background: #000000;
  border: 1px solid #333333;
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: inherit;
  margin-left: 10px;
  width: 150px;
}

.baum-manual-coordinates input:focus {
  outline: none;
  border-color: #007AFF;
}

.baum-manual-coordinates button {
  background: #007AFF;
  border: none;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-family: inherit;
  margin-top: 10px;
}

/* Place Edit/View Buttons */
.baum-mapscii-edit-controls {
  margin: 20px 0;
  padding: 15px;
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.baum-place-map-container {
  margin: 20px 0;
  padding: 20px;
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.baum-place-map-container h3 {
  margin: 0 0 15px 0;
  color: #333333;
}

.baum-place-coordinates {
  margin-top: 15px;
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  font-size: 14px;
  color: #666666;
}

.baum-no-location {
  color: #999999;
  font-style: italic;
  margin-bottom: 15px;
}

/* Body class when modal is open */
body.baum-mapscii-modal-open {
  overflow: hidden;
}

/* Responsive Design */
@media (max-width: 768px) {
  .baum-mapscii-modal-content {
    width: 95vw;
    height: 95vh;
  }
  
  .baum-mapscii-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .baum-mapscii-search {
    min-width: auto;
  }
  
  .baum-mapscii-coordinates {
    justify-content: center;
  }
  
  .baum-mapscii-actions {
    justify-content: center;
  }
}

/* Terminal-style ASCII Map Rendering */
.baum-mapscii-container pre {
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.2;
  color: #00FF00;
  background: #000000;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  white-space: pre;
  margin: 0;
}

/* Mapscii-specific styling */
.mapscii-canvas {
  background: #000000 !important;
  color: #00FF00 !important;
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace !important;
  font-size: 12px !important;
  line-height: 1.2 !important;
}

/* Loading state */
.baum-mapscii-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #cccccc;
  font-size: 16px;
}

.baum-mapscii-loading::before {
  content: "Loading interactive map...";
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}
