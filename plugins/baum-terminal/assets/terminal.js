/**
 * Baum Terminal JavaScript
 */
(function($) {
  'use strict';

  class BaumTerminal {
    constructor() {
      this.isOpen = false;
      this.commandHistory = [];
      this.historyIndex = -1;
      this.isProcessing = false;
      this.currentAI = baumTerminal.defaultAI || 'claude';
      this.currentAnimation = null;
      this.animationInterval = null;

      this.init();
    }

    init() {
      this.bindEvents();
      this.setupKeyboardShortcuts();
    }

    bindEvents() {
      // Terminal button click
      $(document).on('click', '#baum-terminal-button', () => {
        this.toggle();
      });

      // Exit button click
      $(document).on('click', '#baum-terminal-exit', () => {
        this.close();
      });

      // Input handling - both keydown and keypress for real-time response
      $(document).on('keydown', '#baum-terminal-input', (e) => {
        this.handleKeydown(e);
      });

      // Real-time character input handling
      $(document).on('input', '#baum-terminal-input', (e) => {
        this.handleRealTimeInput(e);
      });

      // Character-by-character keypress handling
      $(document).on('keypress', '#baum-terminal-input', (e) => {
        this.handleKeypress(e);
      });

      // Prevent any accidental closing - full screen terminal should only close via exit button or ESC
      $(document).on('click', '.baum-terminal-overlay', (e) => {
        // Don't close on click - this is full screen terminal
        e.stopPropagation();
      });

      // AI selector change
      $(document).on('change', '#baum-terminal-ai-select', (e) => {
        this.switchAI($(e.target).val());
      });
    }

    setupKeyboardShortcuts() {
      // Ctrl+` to toggle terminal
      $(document).on('keydown', (e) => {
        if (e.ctrlKey && e.key === '`') {
          e.preventDefault();
          this.toggle();
        }
        
        // Escape to close
        if (e.key === 'Escape' && this.isOpen) {
          this.close();
        }
      });
    }

    toggle() {
      if (this.isOpen) {
        this.close();
      } else {
        this.open();
      }
    }

    open() {
      // Lock body and make terminal full screen
      $('body').addClass('baum-terminal-active');

      $('#baum-terminal-overlay').addClass('active');
      this.isOpen = true;

      // Focus on input after animation
      setTimeout(() => {
        $('#baum-terminal-input').focus();
      }, 200);

      // Initialize AI selector
      this.initializeAISelector();

      // Add welcome message if first time
      if ($('#baum-terminal-output').children().length === 0) {
        this.addSystemMessage('Terminal initialized. Type "help" for available commands.', 'cyan');
        this.addSystemMessage(`Current AI: ${this.getAIDisplayName(this.currentAI)}`, 'yellow');
      }
    }

    initializeAISelector() {
      $('#baum-terminal-ai-select').val(this.currentAI);
      this.updateAIStatus();
    }

    switchAI(newAI) {
      const oldAI = this.currentAI;
      this.currentAI = newAI;
      this.updateAIStatus();

      this.addSystemMessage(`[GREEN]Switched from ${this.getAIDisplayName(oldAI)} to ${this.getAIDisplayName(newAI)}[/GREEN]`, 'system');
      this.addSystemMessage(`[CYAN]You can now chat with ${this.getAIDisplayName(newAI)}[/CYAN]`, 'system');
    }

    updateAIStatus() {
      const statusElement = $('#baum-terminal-ai-status');
      const aiName = this.getAIDisplayName(this.currentAI);

      statusElement.removeClass('baum-terminal-green baum-terminal-cyan');

      if (this.currentAI === 'claude') {
        statusElement.addClass('baum-terminal-green');
        statusElement.html('● Claude Active');
      } else if (this.currentAI === 'arya') {
        statusElement.addClass('baum-terminal-cyan');
        statusElement.html('● Arya Active');
      }
    }

    getAIDisplayName(ai) {
      const names = {
        'claude': 'Claude AI',
        'arya': 'Arya by Gab'
      };
      return names[ai] || ai;
    }

    close() {
      // Restore body to normal state
      $('body').removeClass('baum-terminal-active');

      $('#baum-terminal-overlay').removeClass('active');
      this.isOpen = false;
    }

    handleKeydown(e) {
      const input = $('#baum-terminal-input');

      switch (e.key) {
        case 'Enter':
          e.preventDefault();
          this.processCommand(input.val().trim());
          break;

        case 'ArrowUp':
          e.preventDefault();
          this.navigateHistory('up');
          break;

        case 'ArrowDown':
          e.preventDefault();
          this.navigateHistory('down');
          break;

        case 'Tab':
          e.preventDefault();
          this.handleTabCompletion(input.val());
          break;

        case 'Escape':
          e.preventDefault();
          this.stopAnimation();
          break;

        case 'l':
          if (e.ctrlKey) {
            e.preventDefault();
            this.clearTerminal();
          }
          break;
      }
    }

    processCommand(command) {
      if (!command || this.isProcessing) return;

      const input = $('#baum-terminal-input');
      
      // Add command to history
      this.commandHistory.push(command);
      this.historyIndex = this.commandHistory.length;
      
      // Display user input
      this.addUserInput(command);
      
      // Clear input
      $('#baum-terminal-input').val('');
      
      // Handle local commands
      if (this.handleLocalCommand(command)) {
        return;
      }
      
      // Send to Claude API
      this.sendToAPI(command);
    }

    handleLocalCommand(command) {
      const cmd = command.toLowerCase();

      switch (cmd) {
        case 'clear':
        case 'cls':
          this.clearTerminal();
          return true;

        case 'history':
          this.showHistory();
          return true;

        case 'exit':
        case 'quit':
          this.close();
          return true;

        case 'about':
          this.showAbout();
          return true;

        case 'suggestions':
        case 'suggest':
          this.getSuggestions();
          return true;

        default:
          return false;
      }
    }

    getSuggestions() {
      this.isProcessing = true;
      this.showLoading();

      const data = {
        action: 'baum_terminal_suggestions',
        nonce: baumTerminal.nonce
      };

      $.ajax({
        url: baumTerminal.ajaxUrl,
        type: 'POST',
        data: data,
        success: (response) => {
          this.hideLoading();

          if (response.success) {
            this.displayResponse({
              type: 'suggestions',
              content: response.data.suggestions
            }, response.data.timestamp);
          } else {
            this.addErrorMessage('Failed to load suggestions');
          }
        },
        error: () => {
          this.hideLoading();
          this.addErrorMessage('Network error loading suggestions');
        },
        complete: () => {
          this.isProcessing = false;
        }
      });
    }

    sendToAPI(message) {
      this.isProcessing = true;
      this.showLoading();

      const data = {
        action: 'baum_terminal_chat',
        message: message,
        ai_model: this.currentAI,
        nonce: baumTerminal.nonce
      };
      
      $.ajax({
        url: baumTerminal.ajaxUrl,
        type: 'POST',
        data: data,
        timeout: 30000,
        success: (response) => {
          this.hideLoading();

          if (response.success) {
            // Handle AI switching
            if (response.data.response.ai_switch) {
              this.currentAI = response.data.response.ai_switch;
              $('#baum-terminal-ai-select').val(this.currentAI);
              this.updateAIStatus();
            }

            this.displayResponse(response.data.response, response.data.timestamp);
          } else {
            this.addErrorMessage(response.data.message || 'An error occurred');
          }
        },
        error: (xhr, status, error) => {
          this.hideLoading();
          
          if (status === 'timeout') {
            this.addErrorMessage('Request timed out. Please try again.');
          } else {
            this.addErrorMessage('Network error: ' + error);
          }
        },
        complete: () => {
          this.isProcessing = false;
        }
      });
    }

    displayResponse(response, timestamp) {
      const output = $('#baum-terminal-output');
      const responseDiv = $('<div class="baum-terminal-line baum-terminal-response"></div>');

      // Format response based on type
      let formattedResponse = this.formatResponse(response);

      // Wrap in <pre> for exact terminal formatting
      const preElement = $('<pre></pre>').html(formattedResponse);
      responseDiv.append(preElement);

      // Add timestamp
      if (timestamp) {
        const timestampSpan = $('<span class="baum-terminal-timestamp"></span>').text(`[${timestamp}]`);
        responseDiv.append(timestampSpan);
      }

      output.append(responseDiv);
      this.scrollToBottom();
    }

    formatResponse(response) {
      if (typeof response === 'object') {
        switch (response.type) {
          case 'error':
            return `<span class="baum-terminal-red">ERROR: ${this.formatTextWithColors(response.content)}</span>`;

          case 'help':
            return this.formatTextWithColors(response.content);

          case 'system':
            return this.formatTextWithColors(response.content);

          case 'map':
            return this.formatTextWithColors(response.content);

          case 'animation':
            // Start animation after a brief delay to show the message first
            setTimeout(() => this.startAnimation(response), 500);
            return `<span class="baum-terminal-green">Starting ${response.name} animation... ${response.controls}</span>`;

          case 'interactive':
            // Don't start interactive for now - just show message
            return `<span class="baum-terminal-yellow">Interactive feature "${response.name}" coming soon! ${response.controls}</span>`;

          case 'success':
          default:
            return this.formatTextWithColors(response.content);
        }
      }

      return this.formatTextWithColors(response);
    }

    formatTextWithColors(text) {
      if (typeof text !== 'string') {
        return text;
      }

      // Convert color tags to HTML spans
      let processedText = text;

      processedText = processedText.replace(/\[GREEN\](.*?)\[\/GREEN\]/gs, '<span class="baum-terminal-green">$1</span>');
      processedText = processedText.replace(/\[RED\](.*?)\[\/RED\]/gs, '<span class="baum-terminal-red">$1</span>');
      processedText = processedText.replace(/\[YELLOW\](.*?)\[\/YELLOW\]/gs, '<span class="baum-terminal-yellow">$1</span>');
      processedText = processedText.replace(/\[CYAN\](.*?)\[\/CYAN\]/gs, '<span class="baum-terminal-cyan">$1</span>');
      processedText = processedText.replace(/\[BLUE\](.*?)\[\/BLUE\]/gs, '<span class="baum-terminal-blue">$1</span>');
      processedText = processedText.replace(/\[MAGENTA\](.*?)\[\/MAGENTA\]/gs, '<span class="baum-terminal-magenta">$1</span>');
      processedText = processedText.replace(/\[GRAY\](.*?)\[\/GRAY\]/gs, '<span class="baum-terminal-gray">$1</span>');
      processedText = processedText.replace(/\[WHITE\](.*?)\[\/WHITE\]/gs, '<span class="baum-terminal-white">$1</span>');

      return processedText;
    }

    addUserInput(command) {
      const output = $('#baum-terminal-output');
      const inputDiv = $('<div class="baum-terminal-line baum-terminal-user-input"></div>');
      const preElement = $('<pre></pre>').text(command);
      inputDiv.append(preElement);
      output.append(inputDiv);
      this.scrollToBottom();
    }

    addSystemMessage(message, color = 'white') {
      const output = $('#baum-terminal-output');
      const messageDiv = $('<div class="baum-terminal-line"></div>');

      // Process color tags in the message
      const processedMessage = this.formatTextWithColors(message);

      // Wrap in <pre> for exact terminal formatting
      const preElement = $('<pre></pre>');
      if (color !== 'white') {
        preElement.html(`<span class="baum-terminal-${color}">${processedMessage}</span>`);
      } else {
        preElement.html(processedMessage);
      }

      messageDiv.append(preElement);
      output.append(messageDiv);
      this.scrollToBottom();
    }

    addErrorMessage(message) {
      this.addSystemMessage(`ERROR: ${message}`, 'red');
    }

    showLoading() {
      const output = $('#baum-terminal-output');
      const loadingDiv = $('<div class="baum-terminal-line baum-terminal-loading" id="baum-terminal-loading"></div>');
      loadingDiv.html('<span class="baum-terminal-cyan">Processing...</span><span class="baum-terminal-cursor"></span>');
      output.append(loadingDiv);
      this.scrollToBottom();
    }

    hideLoading() {
      $('#baum-terminal-loading').remove();
    }

    navigateHistory(direction) {
      const input = $('#baum-terminal-input');

      if (direction === 'up' && this.historyIndex > 0) {
        this.historyIndex--;
        input.val(this.commandHistory[this.historyIndex]);
      } else if (direction === 'down') {
        if (this.historyIndex < this.commandHistory.length - 1) {
          this.historyIndex++;
          input.val(this.commandHistory[this.historyIndex]);
        } else {
          this.historyIndex = this.commandHistory.length;
          input.val('');
        }
      }
    }

    handleTabCompletion(partial) {
      const commands = [
        'help', 'clear', 'history', 'exit', 'about', 'suggestions', 'mapscii', 'animations',
        'ai claude', 'ai arya',
        'map world', 'map europe', 'map usa', 'map asia', 'map africa',
        'map real world', 'map real usa', 'map real europe',
        'map claude london', 'map claude paris', 'map claude tokyo',
        'map server world', 'map server usa',
        'matrix', 'clock', 'aquarium', 'fire', 'starfield', 'snake', 'gallery',
        'weather rain', 'weather snow',
        'what\'s the news in', 'search for', 'latest headlines',
        'unemployment rates by state', 'inflation trends over time',
        'GDP growth by region', 'travel advisories for',
        'population growth in', 'breaking news', 'tech news'
      ];

      const matches = commands.filter(cmd => cmd.startsWith(partial.toLowerCase()));

      if (matches.length === 1) {
        $('#baum-terminal-input').val(matches[0] + ' ');
      } else if (matches.length > 1) {
        this.addSystemMessage('Available completions: ' + matches.join(', '), 'yellow');
      }
    }

    clearTerminal() {
      $('#baum-terminal-output').empty();
      this.addSystemMessage('Terminal cleared.', 'green');
    }

    showHistory() {
      if (this.commandHistory.length === 0) {
        this.addSystemMessage('No command history.', 'yellow');
        return;
      }
      
      this.addSystemMessage('Command History:', 'cyan');
      this.commandHistory.forEach((cmd, index) => {
        this.addSystemMessage(`${index + 1}: ${cmd}`, 'white');
      });
    }

    showAbout() {
      const about = `
Baum Terminal v1.0.0
Powered by Claude AI
Site: ${baumTerminal.siteName}
URL: ${baumTerminal.siteUrl}

Features:
• AI-powered chat interface
• Web search capabilities
• News aggregation
• Command history
• Keyboard shortcuts

Shortcuts:
• Ctrl+\` - Toggle terminal
• Escape - Close terminal
• Ctrl+L - Clear terminal
• Up/Down arrows - Navigate history
• Tab - Auto-complete
      `.trim();
      
      this.addSystemMessage(about, 'cyan');
    }

    scrollToBottom() {
      const content = $('.baum-terminal-content');
      content.scrollTop(content[0].scrollHeight);
    }

    // Animation Functions
    startAnimation(animationData) {
      this.stopAnimation(); // Stop any existing animation
      this.currentAnimation = animationData;

      if (animationData.name === 'clock') {
        this.startClockAnimation();
      } else if (animationData.frames && Array.isArray(animationData.frames)) {
        this.startFrameAnimation(animationData);
      }
    }

    startFrameAnimation(animationData) {
      let frameIndex = 0;
      const frames = animationData.frames;
      const output = $('#baum-terminal-output');

      // Clear terminal for full screen effect
      output.empty();

      // Create animation container
      const animationDiv = $('<div class="baum-terminal-animation"></div>');
      output.append(animationDiv);

      this.animationInterval = setInterval(() => {
        const frameContent = this.formatTextWithColors(frames[frameIndex]);
        animationDiv.html(`<pre>${frameContent}</pre>`);

        frameIndex = (frameIndex + 1) % frames.length;

        // Stop after duration if specified
        if (animationData.duration > 0 && Date.now() - this.animationStartTime > animationData.duration) {
          this.stopAnimation();
        }
      }, 150); // Faster frame rate for smoother animation

      this.animationStartTime = Date.now();
    }

    startClockAnimation() {
      const output = $('#baum-terminal-output');

      // Clear terminal for full screen effect
      output.empty();

      const clockDiv = $('<div class="baum-terminal-animation"></div>');
      output.append(clockDiv);

      this.animationInterval = setInterval(() => {
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        const clockArt = this.generateClockArt(timeString);
        const formattedClock = this.formatTextWithColors(clockArt);
        clockDiv.html(`<pre>${formattedClock}</pre>`);
      }, 1000); // Update every second
    }

    generateClockArt(timeString) {
      return `
[CYAN]================================================================
                         ASCII CLOCK
================================================================[/CYAN]

                    [YELLOW]${timeString}[/YELLOW]

[GREEN]    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║                    ${timeString}                    ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝[/GREEN]

[GRAY]Press ESC to stop the clock[/GRAY]
`;
    }

    stopAnimation() {
      if (this.animationInterval) {
        clearInterval(this.animationInterval);
        this.animationInterval = null;
      }

      if (this.currentAnimation) {
        this.addSystemMessage(`Stopped ${this.currentAnimation.name}`, 'yellow');
        this.currentAnimation = null;
      }

      // Remove animation containers
      $('.baum-terminal-animation').remove();
    }

    // Real-time input handling functions
    handleRealTimeInput(e) {
      const input = $(e.target);
      const currentValue = input.val();

      // Check for real-time commands that should respond immediately
      if (this.shouldHandleRealTime(currentValue)) {
        this.processRealTimeCommand(currentValue);
      }
    }

    handleKeypress(e) {
      const char = String.fromCharCode(e.which);
      const input = $('#baum-terminal-input');
      const currentValue = input.val();

      // Handle special character combinations for interactive features
      this.handleInteractiveInput(char, currentValue);
    }

    shouldHandleRealTime(input) {
      // Commands that should respond in real-time as you type
      const realTimeCommands = [
        'chart', 'graph', 'plot', 'draw', 'live', 'monitor', 'watch'
      ];

      return realTimeCommands.some(cmd => input.toLowerCase().startsWith(cmd));
    }

    processRealTimeCommand(input) {
      const lowerInput = input.toLowerCase();

      if (lowerInput.startsWith('chart') || lowerInput.startsWith('graph')) {
        this.showLiveChart(input);
      } else if (lowerInput.startsWith('draw')) {
        this.showLiveDrawing(input);
      } else if (lowerInput.startsWith('monitor')) {
        this.showLiveMonitor(input);
      }
    }

    handleInteractiveInput(char, currentInput) {
      // Handle arrow keys and special characters for interactive elements
      if (this.currentAnimation && this.currentAnimation.type === 'interactive') {
        // Handle interactive game input
        this.handleGameInput(char, currentInput);
      }
    }

    // Live chart generation as you type
    showLiveChart(input) {
      const output = $('#baum-terminal-output');

      // Remove previous live chart
      $('.baum-terminal-live-chart').remove();

      const chartDiv = $('<div class="baum-terminal-live-chart"></div>');

      // Generate simple ASCII chart based on input
      const chartData = this.generateLiveChart(input);
      chartDiv.html(`<pre>${this.formatTextWithColors(chartData)}</pre>`);

      output.append(chartDiv);
      this.scrollToBottom();
    }

    generateLiveChart(input) {
      // Extract numbers from input for chart data
      const numbers = input.match(/\d+/g) || [10, 20, 15, 25, 30];
      const maxVal = Math.max(...numbers);

      let chart = "[CYAN]Live Chart (as you type):[/CYAN]\n\n";

      // Generate bars
      for (let i = 0; i < numbers.length; i++) {
        const value = parseInt(numbers[i]);
        const barLength = Math.round((value / maxVal) * 40);
        const bar = '[GREEN]' + '█'.repeat(barLength) + '[/GREEN]';
        chart += `${value.toString().padStart(3)} |${bar}\n`;
      }

      chart += "\n[GRAY]Chart updates as you type numbers...[/GRAY]";

      return chart;
    }

    // Live drawing/ASCII art as you type
    showLiveDrawing(input) {
      const output = $('#baum-terminal-output');

      // Remove previous live drawing
      $('.baum-terminal-live-drawing').remove();

      const drawingDiv = $('<div class="baum-terminal-live-drawing"></div>');

      // Generate ASCII art based on input
      const drawing = this.generateLiveDrawing(input);
      drawingDiv.html(`<pre>${this.formatTextWithColors(drawing)}</pre>`);

      output.append(drawingDiv);
      this.scrollToBottom();
    }

    generateLiveDrawing(input) {
      const words = input.split(' ').slice(1); // Remove 'draw' command
      let drawing = "[CYAN]Live ASCII Drawing:[/CYAN]\n\n";

      // Simple ASCII art generation based on words
      words.forEach(word => {
        switch(word.toLowerCase()) {
          case 'house':
            drawing += "[YELLOW]    /\\\n   /  \\\n  /____\\\n |    |\n | [] |\n |____|\n[/YELLOW]";
            break;
          case 'tree':
            drawing += "[GREEN]   ^^^^\n  ^^^^^^\n ^^^^^^^^\n    ||\n    ||\n[/GREEN]";
            break;
          case 'sun':
            drawing += "[YELLOW] \\ | /\n  \\|/\n---O---\n  /|\\\n / | \\\n[/YELLOW]";
            break;
          case 'star':
            drawing += "[WHITE]   *\n  ***\n *****\n  ***\n   *\n[/WHITE]";
            break;
          default:
            drawing += `[GRAY]${word} [/GRAY]`;
        }
        drawing += "\n";
      });

      drawing += "\n[GRAY]Type: draw house tree sun star[/GRAY]";

      return drawing;
    }

    // Live system monitor
    showLiveMonitor(input) {
      const output = $('#baum-terminal-output');

      // Remove previous monitor
      $('.baum-terminal-live-monitor').remove();

      const monitorDiv = $('<div class="baum-terminal-live-monitor"></div>');

      const monitorData = this.generateLiveMonitor();
      monitorDiv.html(`<pre>${this.formatTextWithColors(monitorData)}</pre>`);

      output.append(monitorDiv);
      this.scrollToBottom();
    }

    generateLiveMonitor() {
      const now = new Date();
      const cpu = Math.round(Math.random() * 100);
      const memory = Math.round(Math.random() * 100);
      const disk = Math.round(Math.random() * 100);

      return `[CYAN]Live System Monitor - ${now.toLocaleTimeString()}[/CYAN]

[YELLOW]CPU Usage:[/YELLOW]    ${cpu}% [${'█'.repeat(Math.round(cpu/5))}${' '.repeat(20-Math.round(cpu/5))}]
[YELLOW]Memory:[/YELLOW]       ${memory}% [${'█'.repeat(Math.round(memory/5))}${' '.repeat(20-Math.round(memory/5))}]
[YELLOW]Disk Usage:[/YELLOW]   ${disk}% [${'█'.repeat(Math.round(disk/5))}${' '.repeat(20-Math.round(disk/5))}]

[GRAY]Updates in real-time as you type...[/GRAY]`;
    }


  }

  // Initialize terminal when document is ready
  $(document).ready(() => {
    new BaumTerminal();
  });

})(jQuery);
