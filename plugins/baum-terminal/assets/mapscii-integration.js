/**
 * Mapscii Integration for Place CPT
 * Interactive ASCII maps in browser using mapscii library
 */

(function($) {
  'use strict';

  class BaumMapscii {
    constructor() {
      this.modal = null;
      this.mapsciiInstance = null;
      this.currentPlaceId = null;
      this.currentLat = 40.7128; // Default to NYC
      this.currentLng = -74.0060;
      this.isViewOnly = false;
      this.mapsciiLoaded = false;

      this.init();
      this.waitForMapscii();
    }
    
    init() {
      this.modal = $('#baum-mapscii-modal');
      this.bindEvents();
    }

    /**
     * Wait for mapscii library to load
     */
    waitForMapscii() {
      let attempts = 0;
      const maxAttempts = 60; // 6 seconds

      const checkMapscii = () => {
        attempts++;

        if (window.mapsciiReady === true) {
          console.log('Mapscii loaded successfully');
          this.mapsciiLoaded = true;
          this.onMapsciiLoaded();
          return;
        }

        if (attempts < maxAttempts) {
          setTimeout(checkMapscii, 100);
        } else {
          console.error('Mapscii failed to load after 6 seconds');
          this.mapsciiLoaded = false;
        }
      };

      checkMapscii();
    }

    /**
     * Called when mapscii is loaded
     */
    onMapsciiLoaded() {
      console.log('Mapscii integration ready');
    }
    
    bindEvents() {
      // Close modal events
      $('.baum-mapscii-close, #baum-mapscii-cancel').on('click', () => {
        this.closeModal();
      });
      
      // ESC key to close
      $(document).on('keydown', (e) => {
        if (e.key === 'Escape' && this.modal.is(':visible')) {
          this.closeModal();
        }
      });
      
      // Search functionality
      $('#baum-mapscii-search-btn').on('click', () => {
        this.searchLocation();
      });
      
      $('#baum-mapscii-search').on('keypress', (e) => {
        if (e.which === 13) { // Enter key
          this.searchLocation();
        }
      });
      
      // Save location
      $('#baum-mapscii-save').on('click', () => {
        this.saveLocation();
      });
      
      // Modal click outside to close
      this.modal.on('click', (e) => {
        if (e.target === this.modal[0]) {
          this.closeModal();
        }
      });
    }
    
    /**
     * Open mapscii for place editing
     */
    openForPlace(placeId) {
      this.currentPlaceId = placeId;
      this.isViewOnly = false;
      
      // Load existing coordinates if available
      this.loadPlaceCoordinates(placeId).then(() => {
        this.openModal();
        this.initializeMapscii();
      });
    }
    
    /**
     * Open mapscii for viewing only
     */
    openViewOnly(lat, lng, placeName = '') {
      this.currentPlaceId = null;
      this.currentLat = lat;
      this.currentLng = lng;
      this.isViewOnly = true;
      
      // Update UI for view-only mode
      $('#baum-mapscii-save').hide();
      $('.baum-mapscii-search').hide();
      $('.baum-mapscii-modal-content h3').text(`Interactive Map - ${placeName}`);
      
      this.openModal();
      this.initializeMapscii();
    }
    
    /**
     * Load place coordinates from server
     */
    loadPlaceCoordinates(placeId) {
      return $.ajax({
        url: baumMapscii.ajaxUrl,
        type: 'POST',
        data: {
          action: 'get_place_coordinates',
          place_id: placeId,
          nonce: baumMapscii.nonce
        }
      }).done((response) => {
        if (response.success) {
          this.currentLat = parseFloat(response.data.latitude) || 40.7128; // Default to NYC
          this.currentLng = parseFloat(response.data.longitude) || -74.0060;
          
          // Update search field with existing address
          if (response.data.address) {
            $('#baum-mapscii-search').val(response.data.address);
          }
        }
      }).fail(() => {
        // Default coordinates (NYC)
        this.currentLat = 40.7128;
        this.currentLng = -74.0060;
      });
    }
    
    /**
     * Open the modal
     */
    openModal() {
      this.modal.show();
      $('body').addClass('baum-mapscii-modal-open');
      
      // Reset UI for editing mode
      if (!this.isViewOnly) {
        $('#baum-mapscii-save').show();
        $('.baum-mapscii-search').show();
        $('.baum-mapscii-modal-content h3').text('Interactive Map - Select Location');
      }
    }
    
    /**
     * Close the modal
     */
    closeModal() {
      this.modal.hide();
      $('body').removeClass('baum-mapscii-modal-open');
      
      // Cleanup mapscii instance
      if (this.mapsciiInstance) {
        this.mapsciiInstance.destroy();
        this.mapsciiInstance = null;
      }
      
      // Clear container
      $('#baum-mapscii-container').empty();
    }
    
    /**
     * Initialize mapscii instance
     */
    initializeMapscii() {
      const container = document.getElementById('baum-mapscii-container');

      if (!container) {
        console.error('Map container not found');
        return;
      }

      if (!this.mapsciiLoaded) {
        this.showMapsciiError();
        return;
      }

      // Clear any existing content
      container.innerHTML = '';

      try {
        // Initialize real mapscii
        if (!window.Mapscii) {
          throw new Error('Mapscii not available');
        }

        // Create mapscii instance
        this.mapsciiInstance = new window.Mapscii({
          container: container,
          center: [this.currentLng, this.currentLat],
          zoom: 12,
          width: 100,
          height: 30,
          interactive: true
        });

        console.log('Mapscii initialized successfully');

        // Update coordinates display
        this.updateCoordinatesDisplay();

        // Bind events
        this.bindMapsciiEvents();

        // Focus the container for keyboard input
        container.setAttribute('tabindex', '0');
        container.focus();

      } catch (error) {
        console.error('Failed to initialize mapscii:', error);
        this.showMapsciiError();
      }
    }
    
    /**
     * Bind mapscii-specific events
     */
    bindMapsciiEvents() {
      if (!this.mapsciiInstance) return;

      // Bind WASD navigation to the map container
      const container = document.getElementById('baum-mapscii-container');

      if (container) {
        container.addEventListener('keydown', (e) => {
          const moveAmount = 0.001;
          let moved = false;

          switch(e.key.toLowerCase()) {
            case 'w':
            case 'arrowup':
              e.preventDefault();
              this.currentLat += moveAmount;
              moved = true;
              break;
            case 's':
            case 'arrowdown':
              e.preventDefault();
              this.currentLat -= moveAmount;
              moved = true;
              break;
            case 'a':
            case 'arrowleft':
              e.preventDefault();
              this.currentLng -= moveAmount;
              moved = true;
              break;
            case 'd':
            case 'arrowright':
              e.preventDefault();
              this.currentLng += moveAmount;
              moved = true;
              break;
            case '+':
            case '=':
              e.preventDefault();
              if (this.mapsciiInstance.zoomIn) {
                this.mapsciiInstance.zoomIn();
              }
              break;
            case '-':
            case '_':
              e.preventDefault();
              if (this.mapsciiInstance.zoomOut) {
                this.mapsciiInstance.zoomOut();
              }
              break;
          }

          if (moved) {
            this.updateCoordinatesDisplay();
            // Update mapscii center
            if (this.mapsciiInstance.setCenter) {
              this.mapsciiInstance.setCenter([this.currentLng, this.currentLat]);
            }
          }
        });

        // Make sure container can receive focus
        container.setAttribute('tabindex', '0');
        container.style.outline = 'none';
      }

      // Try to bind mapscii native events if available
      try {
        if (this.mapsciiInstance.on) {
          this.mapsciiInstance.on('move', (center) => {
            this.currentLng = center[0];
            this.currentLat = center[1];
            this.updateCoordinatesDisplay();
          });

          this.mapsciiInstance.on('click', (coordinates) => {
            if (!this.isViewOnly) {
              this.currentLng = coordinates[0];
              this.currentLat = coordinates[1];
              this.updateCoordinatesDisplay();
            }
          });
        }
      } catch (error) {
        console.warn('Mapscii native events not available:', error);
      }
    }



    /**
     * Update coordinates display
     */
    updateCoordinatesDisplay() {
      $('#baum-mapscii-lat').text(this.currentLat.toFixed(6));
      $('#baum-mapscii-lng').text(this.currentLng.toFixed(6));
    }
    
    /**
     * Search for location
     */
    searchLocation() {
      const query = $('#baum-mapscii-search').val().trim();

      if (!query) {
        alert('Please enter a location to search for');
        return;
      }

      // Show loading state
      const searchBtn = $('#baum-mapscii-search-btn');
      const originalText = searchBtn.text();
      searchBtn.text('Searching...').prop('disabled', true);

      // Use Nominatim geocoding service
      this.geocodeLocation(query).then((coordinates) => {
        if (coordinates) {
          this.currentLat = coordinates.lat;
          this.currentLng = coordinates.lng;

          // Update mapscii view
          if (this.mapsciiInstance && this.mapsciiInstance.setCenter) {
            this.mapsciiInstance.setCenter([this.currentLng, this.currentLat]);
          }

          this.updateCoordinatesDisplay();
          console.log(`Found location: ${query} at ${this.currentLat}, ${this.currentLng}`);

        } else {
          alert('Location not found. Please try a different search term.');
        }
      }).catch((error) => {
        console.error('Search failed:', error);
        alert('Search failed. Please check your internet connection and try again.');
      }).finally(() => {
        // Restore button state
        searchBtn.text(originalText).prop('disabled', false);
      });
    }

    /**
     * Geocode location using Nominatim (OpenStreetMap)
     */
    geocodeLocation(query) {
      return new Promise((resolve, reject) => {
        $.ajax({
          url: 'https://nominatim.openstreetmap.org/search',
          method: 'GET',
          data: {
            q: query,
            format: 'json',
            limit: 1,
            addressdetails: 1
          },
          headers: {
            'User-Agent': 'BaumTerminal/1.0'
          },
          timeout: 10000
        }).done((results) => {
          if (results && results.length > 0) {
            const result = results[0];
            resolve({
              lat: parseFloat(result.lat),
              lng: parseFloat(result.lon),
              display_name: result.display_name
            });
          } else {
            resolve(null);
          }
        }).fail((xhr, status, error) => {
          console.error('Geocoding request failed:', status, error);
          reject(error);
        });
      });
    }
    
    /**
     * Save location to place
     */
    saveLocation() {
      if (!this.currentPlaceId) {
        alert('No place selected for saving');
        return;
      }
      
      const address = $('#baum-mapscii-search').val().trim();
      
      $.ajax({
        url: baumMapscii.ajaxUrl,
        type: 'POST',
        data: {
          action: 'save_place_coordinates',
          place_id: this.currentPlaceId,
          latitude: this.currentLat,
          longitude: this.currentLng,
          address: address,
          nonce: baumMapscii.nonce
        }
      }).done((response) => {
        if (response.success) {
          alert('Location saved successfully!');
          this.closeModal();
          
          // Refresh the page to show updated coordinates
          if (window.location.href.includes('post.php') || window.location.href.includes('post-new.php')) {
            window.location.reload();
          }
        } else {
          alert('Failed to save location: ' + (response.data || 'Unknown error'));
        }
      }).fail(() => {
        alert('Failed to save location. Please try again.');
      });
    }
    
    /**
     * Show mapscii error fallback
     */
    showMapsciiError() {
      const container = $('#baum-mapscii-container');
      container.html(`
        <div class="baum-mapscii-error">
          <h4>Interactive Map Unavailable</h4>
          <p>The interactive ASCII map could not be loaded.</p>
          <p>You can still manually enter coordinates:</p>
          <div class="baum-manual-coordinates">
            <label>Latitude: <input type="number" id="manual-lat" step="0.000001" value="${this.currentLat}"></label>
            <label>Longitude: <input type="number" id="manual-lng" step="0.000001" value="${this.currentLng}"></label>
            <button id="update-manual-coords" class="button">Update Coordinates</button>
          </div>
        </div>
      `);
      
      // Bind manual coordinate update
      $('#update-manual-coords').on('click', () => {
        this.currentLat = parseFloat($('#manual-lat').val()) || 0;
        this.currentLng = parseFloat($('#manual-lng').val()) || 0;
        this.updateCoordinatesDisplay();
      });
    }
  }

  // Initialize when document is ready
  $(document).ready(function() {
    window.BaumMapscii = new BaumMapscii();
  });

})(jQuery);
