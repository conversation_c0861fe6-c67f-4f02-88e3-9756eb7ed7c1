<?php
/**
 * Plugin Name: <PERSON>um Terminal
 * Plugin URI: https://baumpress.com/plugins/baum-terminal
 * Description: Terminal-style interface for interacting with Claude AI with web search and news capabilities
 * Version: 1.0.0
 * Author: BaumPress
 * License: GPL v2 or later
 * Text Domain: baum-terminal
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Define plugin constants
define('BAUM_TERMINAL_VERSION', '1.0.0');
define('BAUM_TERMINAL_PLUGIN_DIR', get_template_directory() . '/plugins/baum-terminal/');
define('BAUM_TERMINAL_PLUGIN_URL', get_template_directory_uri() . '/plugins/baum-terminal/');

// Include required files
require_once BAUM_TERMINAL_PLUGIN_DIR . 'includes/ascii-art-generator.php';
require_once BAUM_TERMINAL_PLUGIN_DIR . 'includes/map-generator.php';
require_once BAUM_TERMINAL_PLUGIN_DIR . 'includes/animation-engine.php';
require_once BAUM_TERMINAL_PLUGIN_DIR . 'includes/claude-map-api.php';
require_once BAUM_TERMINAL_PLUGIN_DIR . 'includes/mapscii-integration.php';
require_once BAUM_TERMINAL_PLUGIN_DIR . 'includes/place-cpt.php';

/**
 * Main Baum Terminal Class
 */
class BaumTerminal {

  private $ascii_generator;
  private $map_generator;
  private $animation_engine;
  private $claude_map_api;

  /**
   * Constructor
   */
  public function __construct() {
    $this->ascii_generator = new BaumTerminalAsciiGenerator();
    $this->map_generator = new BaumTerminalMapGenerator();
    $this->animation_engine = new BaumTerminalAnimationEngine();
    $this->claude_map_api = new BaumTerminalClaudeMapAPI();

    add_action('init', array($this, 'init'));
    add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    add_action('wp_footer', array($this, 'render_terminal'));
    add_action('wp_ajax_baum_terminal_chat', array($this, 'handle_chat'));
    add_action('wp_ajax_nopriv_baum_terminal_chat', array($this, 'handle_chat'));
    add_action('wp_ajax_baum_terminal_suggestions', array($this, 'get_suggestions'));
    add_action('wp_ajax_nopriv_baum_terminal_suggestions', array($this, 'get_suggestions'));
    add_action('admin_menu', array($this, 'add_admin_menu'));
    add_action('admin_init', array($this, 'admin_init'));

    // Rate limiting
    add_action('init', array($this, 'init_rate_limiting'));
  }
  
  /**
   * Initialize plugin
   */
  public function init() {
    // Load text domain for theme-based plugin
    load_theme_textdomain('baum-terminal', BAUM_TERMINAL_PLUGIN_DIR . 'languages');
  }
  
  /**
   * Enqueue scripts and styles
   */
  public function enqueue_scripts() {
    wp_enqueue_script(
      'baum-terminal-js',
      BAUM_TERMINAL_PLUGIN_URL . 'assets/terminal.js',
      array('jquery'),
      BAUM_TERMINAL_VERSION,
      true
    );
    
    wp_enqueue_style(
      'baum-terminal-css',
      BAUM_TERMINAL_PLUGIN_URL . 'assets/terminal.css',
      array(),
      BAUM_TERMINAL_VERSION
    );
    
    // Localize script
    wp_localize_script('baum-terminal-js', 'baumTerminal', array(
      'ajaxUrl' => admin_url('admin-ajax.php'),
      'nonce' => wp_create_nonce('baum_terminal_nonce'),
      'siteName' => get_bloginfo('name'),
      'siteUrl' => home_url(),
      'rateLimitMessage' => __('Rate limit exceeded. Please wait before sending another message.', 'baum-terminal'),
      'defaultAI' => get_option('baum_terminal_default_ai', 'claude'),
      'availableAIs' => array(
        'claude' => 'Claude AI',
        'arya' => 'Arya by Gab'
      )
    ));
  }
  
  /**
   * Render terminal HTML
   */
  public function render_terminal() {
    $site_name = get_bloginfo('name');
    $ascii_art = $this->generate_ascii_art($site_name);
    
    ?>
    <!-- Floating Terminal Button - Bottom Left -->
    <div id="baum-terminal-button" class="baum-terminal-button">
      <i class="fas fa-terminal"></i>
    </div>

    <!-- Full Screen Terminal Overlay -->
    <div id="baum-terminal-overlay" class="baum-terminal-overlay">
      <!-- Exit Button -->
      <div id="baum-terminal-exit" class="baum-terminal-exit">
        <i class="fas fa-times"></i>
      </div>

      <div class="baum-terminal-container">
        
        <div class="baum-terminal-content">
          <div class="baum-terminal-ascii">
            <pre><?php echo esc_html($ascii_art); ?></pre>
          </div>
          
          <div class="baum-terminal-welcome">
            <p class="baum-terminal-green">Welcome to <?php echo esc_html($site_name); ?> Terminal</p>
            <p class="baum-terminal-cyan">AI-powered terminal with web search and news capabilities</p>
            <p class="baum-terminal-yellow">Try: "what's the news in San Francisco?" or "search for latest tech trends"</p>
            <p class="baum-terminal-gray">Type 'help' for available commands | 'ai claude' or 'ai arya' to switch AI</p>
          </div>

          <!-- AI Selector -->
          <div class="baum-terminal-ai-selector">
            <span class="baum-terminal-ai-label">AI Model:</span>
            <select id="baum-terminal-ai-select" class="baum-terminal-select">
              <option value="claude">Claude AI</option>
              <option value="arya">Arya by Gab</option>
            </select>
            <span id="baum-terminal-ai-status" class="baum-terminal-ai-status baum-terminal-green">● Claude Active</span>
          </div>
          
          <div id="baum-terminal-output" class="baum-terminal-output"></div>
          
          <div class="baum-terminal-input-line">
            <span class="baum-terminal-prompt">user@terminal:~$ </span>
            <input type="text" id="baum-terminal-input" class="baum-terminal-input" placeholder="Type your message..." autocomplete="off" spellcheck="false">
          </div>
        </div>
      </div>
    </div>
    <?php
  }
  
  /**
   * Generate ASCII art for site name
   */
  private function generate_ascii_art($site_name) {
    return $this->ascii_generator->generate_site_name_art($site_name, 'auto');
  }
  
  /**
   * Handle chat AJAX request
   */
  public function handle_chat() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'baum_terminal_nonce')) {
      wp_die('Security check failed');
    }
    
    // Check rate limiting
    if (!$this->check_rate_limit()) {
      wp_send_json_error(array(
        'message' => __('Rate limit exceeded. Please wait before sending another message.', 'baum-terminal')
      ));
    }
    
    $message = sanitize_text_field($_POST['message']);
    $ai_model = sanitize_text_field($_POST['ai_model'] ?? 'claude');
    $user_ip = $_SERVER['REMOTE_ADDR'];

    // Process the message
    $response = $this->process_message($message, $ai_model);

    // Update rate limiting
    $this->update_rate_limit();
    
    wp_send_json_success(array(
      'response' => $response,
      'timestamp' => current_time('H:i:s')
    ));
  }
  
  /**
   * Process message and get AI response
   */
  private function process_message($message, $ai_model = 'claude') {
    // Check for AI switching commands
    if (preg_match('/^ai\s+(claude|arya)$/i', $message, $matches)) {
      $new_ai = strtolower($matches[1]);
      return array(
        'type' => 'system',
        'content' => "[GREEN]Switched to " . ucfirst($new_ai) . " AI[/GREEN]\n" .
                    "[CYAN]You can now chat with " . ($new_ai === 'claude' ? 'Claude AI' : 'Arya by Gab') . "[/CYAN]",
        'ai_switch' => $new_ai
      );
    }

    // Check for map commands
    if (preg_match('/^map\s+(.+)$/i', $message, $matches)) {
      $location = trim($matches[1]);
      return array(
        'type' => 'map',
        'content' => $this->map_generator->generate_map($location)
      );
    }

    // Check for mapscii command
    if (strtolower($message) === 'mapscii') {
      return $this->get_mapscii_help();
    }

    // Check for animation commands
    if (strtolower($message) === 'matrix') {
      return $this->animation_engine->get_matrix_animation();
    }

    if (strtolower($message) === 'clock') {
      return $this->animation_engine->get_clock_animation();
    }

    if (strtolower($message) === 'aquarium') {
      return $this->animation_engine->get_aquarium_animation();
    }

    if (strtolower($message) === 'fire') {
      return $this->animation_engine->get_fire_animation();
    }

    if (strtolower($message) === 'starfield') {
      return $this->animation_engine->get_starfield_animation();
    }

    if (strtolower($message) === 'snake') {
      return $this->animation_engine->get_snake_game();
    }

    if (strtolower($message) === 'gallery') {
      return $this->animation_engine->get_art_gallery();
    }

    // Check for weather commands
    if (preg_match('/^weather\s+(rain|snow)$/i', $message, $matches)) {
      $weather_type = strtolower($matches[1]);
      return $this->animation_engine->get_weather_animation($weather_type);
    }

    // Check for animations help
    if (strtolower($message) === 'animations') {
      return $this->get_animations_help();
    }

    // Check for special commands
    if (strtolower($message) === 'help') {
      return $this->get_help_response();
    }

    // Route to appropriate AI
    switch ($ai_model) {
      case 'arya':
        return $this->process_arya_message($message);
      case 'claude':
      default:
        return $this->process_claude_message($message);
    }
  }

  /**
   * Process message with Claude AI
   */
  private function process_claude_message($message) {
    $api_key = get_option('baum_terminal_claude_api_key');

    if (empty($api_key)) {
      return array(
        'type' => 'error',
        'content' => 'Claude API key not configured. Please check plugin settings.'
      );
    }

    // Determine if this is a news or search request
    $is_news = $this->is_news_request($message);
    $is_search = $this->is_search_request($message);

    // Prepare system message with MCP capabilities
    $system_message = $this->get_system_message($is_news, $is_search);

    // Make API call to Claude
    return $this->call_claude_api($message, $system_message);
  }

  /**
   * Process message with Arya AI
   */
  private function process_arya_message($message) {
    $api_key = get_option('baum_terminal_arya_api_key');

    if (empty($api_key)) {
      return array(
        'type' => 'error',
        'content' => 'Arya API key not configured. Please check plugin settings.'
      );
    }

    // Determine if this is a news or search request
    $is_news = $this->is_news_request($message);
    $is_search = $this->is_search_request($message);

    // Prepare system message for Arya
    $system_message = $this->get_arya_system_message($is_news, $is_search);

    // Make API call to Arya
    return $this->call_arya_api($message, $system_message);
  }
  
  /**
   * Check if message is a news request
   */
  private function is_news_request($message) {
    $news_keywords = array('news', 'headlines', 'breaking', 'latest', 'current events');
    $message_lower = strtolower($message);
    
    foreach ($news_keywords as $keyword) {
      if (strpos($message_lower, $keyword) !== false) {
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * Check if message is a search request
   */
  private function is_search_request($message) {
    $search_keywords = array('search', 'find', 'look up', 'google', 'what is', 'who is');
    $message_lower = strtolower($message);
    
    foreach ($search_keywords as $keyword) {
      if (strpos($message_lower, $keyword) !== false) {
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * Get system message with MCP capabilities
   */
  private function get_system_message($is_news, $is_search) {
    $system = "You are a helpful AI assistant integrated into a terminal interface. ";
    $system .= "Respond in a terminal-friendly format with appropriate colors and formatting. ";
    
    if ($is_news) {
      $system .= "You have access to news aggregation capabilities. When asked about news, ";
      $system .= "provide current headlines and summaries in monospace format. ";
    }
    
    if ($is_search) {
      $system .= "You have web search capabilities. When asked to search for information, ";
      $system .= "provide relevant and up-to-date results. ";
    }
    
    $system .= "Use terminal colors: GREEN for success, RED for errors, YELLOW for warnings, ";
    $system .= "CYAN for information, and WHITE for normal text. ";
    $system .= "Keep responses concise and terminal-appropriate.";
    
    return $system;
  }
  
  /**
   * Get help response
   */
  private function get_help_response() {
    return array(
      'type' => 'help',
      'content' => $this->get_enhanced_help_content()
    );
  }

  /**
   * Get enhanced help content with suggestions
   */
  private function get_enhanced_help_content() {
    $help = "[CYAN]═══════════════════════════════════════════════════════════════[/CYAN]\n";
    $help .= "[CYAN]                    BAUM TERMINAL HELP SYSTEM                    [/CYAN]\n";
    $help .= "[CYAN]═══════════════════════════════════════════════════════════════[/CYAN]\n\n";

    $help .= "[YELLOW]BUILT-IN COMMANDS:[/YELLOW]\n";
    $help .= "• help - Show this help message\n";
    $help .= "• clear/cls - Clear the terminal\n";
    $help .= "• history - Show command history\n";
    $help .= "• about - Terminal information\n";
    $help .= "• suggestions - Get topic suggestions\n";
    $help .= "• ai claude - Switch to Claude AI\n";
    $help .= "• ai arya - Switch to Arya by Gab\n";
    $help .= "• mapscii - Show ASCII map commands\n";
    $help .= "• exit/quit - Close terminal\n\n";

    $help .= "[YELLOW]ASCII MAPS:[/YELLOW]\n";
    $help .= "• map world - Show world map\n";
    $help .= "• map europe - Show Europe map\n";
    $help .= "• map usa - Show United States map\n";
    $help .= "• map real world - Detailed real world map\n";
    $help .= "• map claude london - AI-generated map of London\n";
    $help .= "• map server world - Server command-line map\n\n";

    $help .= "[YELLOW]ANIMATIONS & GAMES:[/YELLOW]\n";
    $help .= "• matrix - Matrix rain animation\n";
    $help .= "• clock - Real-time ASCII clock\n";
    $help .= "• weather rain/snow - Weather animations\n";
    $help .= "• aquarium - ASCII aquarium\n";
    $help .= "• fire - Fire animation\n";
    $help .= "• starfield - Moving stars\n";
    $help .= "• snake - Snake game\n";
    $help .= "• gallery - ASCII art gallery\n";
    $help .= "• animations - Show animation help\n\n";

    $help .= "[YELLOW]NEWS & CURRENT EVENTS:[/YELLOW]\n";
    $help .= "• what's the news in San Francisco?\n";
    $help .= "• latest headlines\n";
    $help .= "• breaking news\n";
    $help .= "• tech news today\n\n";

    $help .= "[YELLOW]WEB SEARCH:[/YELLOW]\n";
    $help .= "• search for artificial intelligence\n";
    $help .= "• find information about climate change\n";
    $help .= "• look up latest tech trends\n\n";

    $help .= "[YELLOW]GOVERNMENT DATA & ECONOMICS:[/YELLOW]\n";
    $help .= "• unemployment rates by state\n";
    $help .= "• inflation trends over time\n";
    $help .= "• GDP growth by region\n";
    $help .= "• travel advisories for Japan\n";
    $help .= "• population growth in Texas\n\n";

    $help .= "[YELLOW]GEOPOLITICS & DEMOGRAPHICS:[/YELLOW]\n";
    $help .= "• diplomatic status with China\n";
    $help .= "• trade relationships with EU\n";
    $help .= "• demographic trends in California\n";
    $help .= "• economic indicators for Q3\n\n";

    $help .= "[GREEN]Type 'suggestions' for more topic ideas![/GREEN]\n";
    $help .= "[GRAY]Tip: Use Tab for auto-completion, ↑/↓ for history[/GRAY]";

    return $help;
  }

  /**
   * Get suggestions for topics to explore
   */
  public function get_suggestions() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'baum_terminal_nonce')) {
      wp_die('Security check failed');
    }

    $suggestions = $this->generate_topic_suggestions();

    wp_send_json_success(array(
      'suggestions' => $suggestions,
      'timestamp' => current_time('H:i:s')
    ));
  }

  /**
   * Generate topic suggestions
   */
  private function generate_topic_suggestions() {
    $categories = array(
      'Economic Data' => array(
        'unemployment rates by state',
        'inflation trends over time',
        'GDP growth by region',
        'interest rate changes',
        'consumer spending patterns',
        'housing market data',
        'stock market performance'
      ),
      'Geopolitical Topics' => array(
        'travel advisories for [country]',
        'trade relationships with China',
        'diplomatic status updates',
        'international sanctions list',
        'embassy locations worldwide',
        'foreign policy changes',
        'global security threats'
      ),
      'Demographics & Society' => array(
        'population growth by city',
        'age distribution trends',
        'income inequality statistics',
        'education levels by region',
        'migration patterns',
        'urban vs rural demographics',
        'workforce participation rates'
      ),
      'News & Current Events' => array(
        'breaking news worldwide',
        'tech industry updates',
        'climate change developments',
        'political developments',
        'scientific breakthroughs',
        'economic policy changes',
        'international relations'
      ),
      'Technology & Innovation' => array(
        'AI development trends',
        'cybersecurity threats',
        'renewable energy progress',
        'space exploration updates',
        'medical breakthroughs',
        'startup funding trends',
        'patent filings by sector'
      )
    );

    $formatted_suggestions = "[CYAN]═══════════════════════════════════════════════════════════════[/CYAN]\n";
    $formatted_suggestions .= "[CYAN]                      TOPIC SUGGESTIONS                         [/CYAN]\n";
    $formatted_suggestions .= "[CYAN]═══════════════════════════════════════════════════════════════[/CYAN]\n\n";

    foreach ($categories as $category => $topics) {
      $formatted_suggestions .= "[YELLOW]{$category}:[/YELLOW]\n";
      foreach ($topics as $topic) {
        $formatted_suggestions .= "• {$topic}\n";
      }
      $formatted_suggestions .= "\n";
    }

    $formatted_suggestions .= "[GREEN]Try any of these topics or ask your own questions![/GREEN]\n";
    $formatted_suggestions .= "[GRAY]Example: \"unemployment rates by state\" or \"what's happening in tech?\"[/GRAY]";

    return $formatted_suggestions;
  }

  /**
   * Get mapscii help response
   */
  private function get_mapscii_help() {
    return array(
      'type' => 'help',
      'content' => "[CYAN]╔══════════════════════════════════════════════════════════════╗
║                    BAUM TERMINAL - ASCII MAPS                 ║
╠══════════════════════════════════════════════════════════════╣[/CYAN]

[YELLOW]AVAILABLE MAP COMMANDS:[/YELLOW]

• [GREEN]map world[/GREEN] - Show ASCII world map
• [GREEN]map europe[/GREEN] - Show ASCII map of Europe
• [GREEN]map usa[/GREEN] - Show ASCII map of United States
• [GREEN]map asia[/GREEN] - Show ASCII map of Asia
• [GREEN]map africa[/GREEN] - Show ASCII map of Africa

[YELLOW]EXAMPLES:[/YELLOW]

[CYAN]user@terminal:~$[/CYAN] map europe
[CYAN]user@terminal:~$[/CYAN] map world
[CYAN]user@terminal:~$[/CYAN] map usa

[YELLOW]FEATURES:[/YELLOW]

• [GREEN]Colored ASCII maps[/GREEN] with country names and borders
• [GREEN]Geographic legends[/GREEN] explaining symbols and colors
• [GREEN]Cached results[/GREEN] for fast loading
• [GREEN]Terminal-optimized[/GREEN] display with proper formatting

[YELLOW]INTERACTIVE MAPS:[/YELLOW]

For detailed interactive maps, try asking:
• \"show me an interactive map of Europe\"
• \"I need a detailed map of California\"
• \"find directions to San Francisco\"

[BLUE]External Resources:[/BLUE]
• OpenStreetMap: https://www.openstreetmap.org/
• Google Maps: https://www.google.com/maps/

[GRAY]Note: ASCII maps are simplified representations.
For precise geographic data, use interactive web maps.[/GRAY]

[CYAN]╚══════════════════════════════════════════════════════════════╝[/CYAN]"
    );
  }

  /**
   * Get animations help response
   */
  private function get_animations_help() {
    return array(
      'type' => 'help',
      'content' => "[CYAN]================================================================
                 BAUM TERMINAL - ANIMATIONS & GAMES
================================================================[/CYAN]

[YELLOW]ANIMATED ASCII:[/YELLOW]

• [GREEN]matrix[/GREEN] - Matrix rain animation (like The Matrix)
• [GREEN]clock[/GREEN] - Real-time animated ASCII clock
• [GREEN]weather rain[/GREEN] - Animated rain falling
• [GREEN]weather snow[/GREEN] - Animated snowfall
• [GREEN]aquarium[/GREEN] - ASCII aquarium with swimming fish
• [GREEN]fire[/GREEN] - Flickering flame animation
• [GREEN]starfield[/GREEN] - Moving stars in space

[YELLOW]INTERACTIVE GAMES:[/YELLOW]

• [GREEN]snake[/GREEN] - Classic Snake game (arrow keys to play)
• [GREEN]gallery[/GREEN] - ASCII art gallery (arrow keys to browse)

[YELLOW]EXAMPLES:[/YELLOW]

[CYAN]user@terminal:~$[/CYAN] matrix
[CYAN]user@terminal:~$[/CYAN] weather rain
[CYAN]user@terminal:~$[/CYAN] snake
[CYAN]user@terminal:~$[/CYAN] aquarium

[YELLOW]CONTROLS:[/YELLOW]

• [GREEN]ESC[/GREEN] - Stop any animation or game
• [GREEN]Arrow Keys[/GREEN] - Navigate games and galleries
• [GREEN]SPACE[/GREEN] - Start games (when prompted)

[YELLOW]FEATURES:[/YELLOW]

• [GREEN]Real-time animations[/GREEN] with smooth frame transitions
• [GREEN]Interactive controls[/GREEN] for games and navigation
• [GREEN]Colorful ASCII art[/GREEN] with terminal color support
• [GREEN]Responsive gameplay[/GREEN] with keyboard input

[BLUE]Try 'matrix' for a cool Matrix rain effect![/BLUE]

[GRAY]Note: Animations run in your browser terminal.
Use ESC key to stop any animation at any time.[/GRAY]

[CYAN]================================================================[/CYAN]"
    );
  }
  
  /**
   * Call Claude API
   */
  private function call_claude_api($message, $system_message) {
    $api_key = get_option('baum_terminal_claude_api_key');
    $model = get_option('baum_terminal_claude_model', 'claude-3-5-sonnet-20241022');

    $headers = array(
      'Content-Type' => 'application/json',
      'x-api-key' => $api_key,
      'anthropic-version' => '2023-06-01'
    );

    // Enhanced system message with terminal formatting instructions
    $enhanced_system = $system_message . "\n\n" .
      "IMPORTANT: Format your responses for terminal display:\n" .
      "- Use [GREEN]text[/GREEN] for success messages\n" .
      "- Use [RED]text[/RED] for errors or warnings\n" .
      "- Use [YELLOW]text[/YELLOW] for highlights or important info\n" .
      "- Use [CYAN]text[/CYAN] for headers and titles\n" .
      "- Use [BLUE]text[/BLUE] for links and references\n" .
      "- Keep lines under 80 characters when possible\n" .
      "- Use monospace formatting for code or structured data\n" .
      "- For news: format as headlines with sources and timestamps\n" .
      "- For search: provide title, URL, and brief description";

    $body = array(
      'model' => $model,
      'max_tokens' => 1500,
      'system' => $enhanced_system,
      'messages' => array(
        array(
          'role' => 'user',
          'content' => $message
        )
      )
    );

    $response = wp_remote_post('https://api.anthropic.com/v1/messages', array(
      'headers' => $headers,
      'body' => json_encode($body),
      'timeout' => 30
    ));

    if (is_wp_error($response)) {
      return array(
        'type' => 'error',
        'content' => 'Failed to connect to Claude API: ' . $response->get_error_message()
      );
    }

    $response_body = wp_remote_retrieve_body($response);
    $data = json_decode($response_body, true);

    if (isset($data['error'])) {
      return array(
        'type' => 'error',
        'content' => 'Claude API Error: ' . $data['error']['message']
      );
    }

    if (isset($data['content'][0]['text'])) {
      return array(
        'type' => 'success',
        'content' => $this->format_terminal_response($data['content'][0]['text'])
      );
    } else {
      return array(
        'type' => 'error',
        'content' => 'Unexpected response from Claude API'
      );
    }
  }

  /**
   * Format response for terminal display
   */
  private function format_terminal_response($text) {
    // Add terminal-specific formatting
    $text = $this->format_news_content($text);
    $text = $this->format_search_content($text);
    $text = $this->add_terminal_colors($text);

    return $text;
  }

  /**
   * Format news content for terminal
   */
  private function format_news_content($text) {
    // Look for news-like patterns and format them
    if (preg_match('/news|headline|breaking/i', $text)) {
      // Add news formatting
      $text = preg_replace('/^(.+)$/m', '• $1', $text);
    }

    return $text;
  }

  /**
   * Format search content for terminal
   */
  private function format_search_content($text) {
    // Format search results if detected
    if (preg_match('/search|results|found/i', $text)) {
      // Add search result formatting
      $lines = explode("\n", $text);
      $formatted_lines = array();

      foreach ($lines as $line) {
        if (preg_match('/^https?:\/\//', trim($line))) {
          $formatted_lines[] = '[BLUE]' . trim($line) . '[/BLUE]';
        } else {
          $formatted_lines[] = $line;
        }
      }

      $text = implode("\n", $formatted_lines);
    }

    return $text;
  }

  /**
   * Add terminal color formatting
   */
  private function add_terminal_colors($text) {
    // Enhance existing color tags and add new ones
    $text = str_replace('**', '[YELLOW]', $text);
    $text = preg_replace('/\*([^*]+)\*/', '[YELLOW]$1[/YELLOW]', $text);

    return $text;
  }
  
  /**
   * Initialize rate limiting
   */
  public function init_rate_limiting() {
    // Clean up old rate limit entries
    $this->cleanup_rate_limits();
  }
  
  /**
   * Check rate limit
   */
  private function check_rate_limit() {
    $user_ip = $_SERVER['REMOTE_ADDR'];
    $rate_limit = get_option('baum_terminal_rate_limit', 20); // 20 requests per hour default
    $rate_limit_key = 'baum_terminal_rate_' . md5($user_ip);
    
    $current_count = get_transient($rate_limit_key);
    
    if ($current_count === false) {
      return true; // No previous requests
    }
    
    return $current_count < $rate_limit;
  }
  
  /**
   * Update rate limit
   */
  private function update_rate_limit() {
    $user_ip = $_SERVER['REMOTE_ADDR'];
    $rate_limit_key = 'baum_terminal_rate_' . md5($user_ip);
    
    $current_count = get_transient($rate_limit_key);
    
    if ($current_count === false) {
      set_transient($rate_limit_key, 1, HOUR_IN_SECONDS);
    } else {
      set_transient($rate_limit_key, $current_count + 1, HOUR_IN_SECONDS);
    }
  }
  
  /**
   * Clean up old rate limit entries
   */
  private function cleanup_rate_limits() {
    // WordPress transients automatically expire, so no manual cleanup needed
  }
  
  /**
   * Add admin menu
   */
  public function add_admin_menu() {
    add_options_page(
      __('Baum Terminal Settings', 'baum-terminal'),
      __('Baum Terminal', 'baum-terminal'),
      'manage_options',
      'baum-terminal',
      array($this, 'admin_page')
    );
  }
  
  /**
   * Admin page
   */
  public function admin_page() {
    ?>
    <div class="wrap">
      <h1><?php _e('Baum Terminal Settings', 'baum-terminal'); ?></h1>
      <form method="post" action="options.php">
        <?php
        settings_fields('baum_terminal_settings');
        do_settings_sections('baum_terminal_settings');
        submit_button();
        ?>
      </form>
    </div>
    <?php
  }
  
  /**
   * Admin init
   */
  public function admin_init() {
    register_setting('baum_terminal_settings', 'baum_terminal_claude_api_key');
    register_setting('baum_terminal_settings', 'baum_terminal_claude_model');
    register_setting('baum_terminal_settings', 'baum_terminal_arya_api_key');
    register_setting('baum_terminal_settings', 'baum_terminal_arya_api_url');
    register_setting('baum_terminal_settings', 'baum_terminal_arya_model');
    register_setting('baum_terminal_settings', 'baum_terminal_default_ai');
    register_setting('baum_terminal_settings', 'baum_terminal_rate_limit');
    register_setting('baum_terminal_settings', 'baum_terminal_enable_news');
    register_setting('baum_terminal_settings', 'baum_terminal_enable_search');
    
    add_settings_section(
      'baum_terminal_main',
      __('AI Configuration', 'baum-terminal'),
      array($this, 'settings_section_callback'),
      'baum_terminal_settings'
    );

    add_settings_section(
      'baum_terminal_arya',
      __('Arya by Gab Configuration', 'baum-terminal'),
      array($this, 'arya_settings_section_callback'),
      'baum_terminal_settings'
    );
    
    add_settings_field(
      'baum_terminal_claude_api_key',
      __('Claude API Key', 'baum-terminal'),
      array($this, 'api_key_callback'),
      'baum_terminal_settings',
      'baum_terminal_main'
    );
    
    add_settings_field(
      'baum_terminal_claude_model',
      __('Claude Model', 'baum-terminal'),
      array($this, 'model_callback'),
      'baum_terminal_settings',
      'baum_terminal_main'
    );

    add_settings_field(
      'baum_terminal_default_ai',
      __('Default AI Model', 'baum-terminal'),
      array($this, 'default_ai_callback'),
      'baum_terminal_settings',
      'baum_terminal_main'
    );

    add_settings_field(
      'baum_terminal_arya_api_key',
      __('Arya API Key', 'baum-terminal'),
      array($this, 'arya_api_key_callback'),
      'baum_terminal_settings',
      'baum_terminal_arya'
    );

    add_settings_field(
      'baum_terminal_arya_api_url',
      __('Arya API URL', 'baum-terminal'),
      array($this, 'arya_api_url_callback'),
      'baum_terminal_settings',
      'baum_terminal_arya'
    );

    add_settings_field(
      'baum_terminal_arya_model',
      __('Arya Model', 'baum-terminal'),
      array($this, 'arya_model_callback'),
      'baum_terminal_settings',
      'baum_terminal_arya'
    );
    
    add_settings_field(
      'baum_terminal_rate_limit',
      __('Rate Limit (requests per hour)', 'baum-terminal'),
      array($this, 'rate_limit_callback'),
      'baum_terminal_settings',
      'baum_terminal_main'
    );
    
    add_settings_field(
      'baum_terminal_enable_news',
      __('Enable News Aggregation', 'baum-terminal'),
      array($this, 'enable_news_callback'),
      'baum_terminal_settings',
      'baum_terminal_main'
    );
    
    add_settings_field(
      'baum_terminal_enable_search',
      __('Enable Web Search', 'baum-terminal'),
      array($this, 'enable_search_callback'),
      'baum_terminal_settings',
      'baum_terminal_main'
    );
  }
  
  public function settings_section_callback() {
    echo '<p>' . __('Configure your AI API settings and terminal features.', 'baum-terminal') . '</p>';
  }

  public function arya_settings_section_callback() {
    echo '<p>' . __('Configure Arya by Gab integration. Get your API key from Gab.com developer portal.', 'baum-terminal') . '</p>';
  }
  
  public function api_key_callback() {
    $value = get_option('baum_terminal_claude_api_key');
    echo '<input type="password" id="baum_terminal_claude_api_key" name="baum_terminal_claude_api_key" value="' . esc_attr($value) . '" class="regular-text" />';
    echo '<p class="description">' . __('Your Claude API key from Anthropic', 'baum-terminal') . '</p>';
  }
  
  public function model_callback() {
    $value = get_option('baum_terminal_claude_model', 'claude-3-5-sonnet-20241022');
    $models = array(
      'claude-3-5-sonnet-20241022' => 'Claude 3.5 Sonnet (Latest)',
      'claude-3-5-sonnet-20240620' => 'Claude 3.5 Sonnet (June)',
      'claude-3-sonnet-20240229' => 'Claude 3 Sonnet',
      'claude-3-haiku-20240307' => 'Claude 3 Haiku',
      'claude-3-opus-20240229' => 'Claude 3 Opus'
    );

    echo '<select id="baum_terminal_claude_model" name="baum_terminal_claude_model">';
    foreach ($models as $model_id => $model_name) {
      echo '<option value="' . esc_attr($model_id) . '"' . selected($value, $model_id, false) . '>' . esc_html($model_name) . '</option>';
    }
    echo '</select>';
    echo '<p class="description">' . __('Claude model to use for responses', 'baum-terminal') . '</p>';
  }
  
  public function rate_limit_callback() {
    $value = get_option('baum_terminal_rate_limit', 20);
    echo '<input type="number" id="baum_terminal_rate_limit" name="baum_terminal_rate_limit" value="' . esc_attr($value) . '" min="1" max="100" />';
    echo '<p class="description">' . __('Maximum requests per user per hour', 'baum-terminal') . '</p>';
  }
  
  public function enable_news_callback() {
    $value = get_option('baum_terminal_enable_news', 1);
    echo '<input type="checkbox" id="baum_terminal_enable_news" name="baum_terminal_enable_news" value="1"' . checked($value, 1, false) . ' />';
    echo '<label for="baum_terminal_enable_news">' . __('Enable news aggregation capabilities', 'baum-terminal') . '</label>';
  }
  
  public function enable_search_callback() {
    $value = get_option('baum_terminal_enable_search', 1);
    echo '<input type="checkbox" id="baum_terminal_enable_search" name="baum_terminal_enable_search" value="1"' . checked($value, 1, false) . ' />';
    echo '<label for="baum_terminal_enable_search">' . __('Enable web search capabilities', 'baum-terminal') . '</label>';
  }

  public function default_ai_callback() {
    $value = get_option('baum_terminal_default_ai', 'claude');
    $options = array(
      'claude' => 'Claude AI',
      'arya' => 'Arya by Gab'
    );

    echo '<select id="baum_terminal_default_ai" name="baum_terminal_default_ai">';
    foreach ($options as $option_value => $option_name) {
      echo '<option value="' . esc_attr($option_value) . '"' . selected($value, $option_value, false) . '>' . esc_html($option_name) . '</option>';
    }
    echo '</select>';
    echo '<p class="description">' . __('Default AI model to use when terminal opens', 'baum-terminal') . '</p>';
  }

  public function arya_api_key_callback() {
    $value = get_option('baum_terminal_arya_api_key');
    echo '<input type="password" id="baum_terminal_arya_api_key" name="baum_terminal_arya_api_key" value="' . esc_attr($value) . '" class="regular-text" />';
    echo '<p class="description">' . __('Your Arya API key from Gab.com', 'baum-terminal') . '</p>';
  }

  public function arya_api_url_callback() {
    $value = get_option('baum_terminal_arya_api_url', 'https://api.gab.com/v1/chat/completions');
    echo '<input type="url" id="baum_terminal_arya_api_url" name="baum_terminal_arya_api_url" value="' . esc_attr($value) . '" class="regular-text" />';
    echo '<p class="description">' . __('Arya API endpoint URL', 'baum-terminal') . '</p>';
  }

  public function arya_model_callback() {
    $value = get_option('baum_terminal_arya_model', 'arya-1');
    $models = array(
      'arya-1' => 'Arya v1',
      'arya-2' => 'Arya v2',
      'arya-pro' => 'Arya Pro'
    );

    echo '<select id="baum_terminal_arya_model" name="baum_terminal_arya_model">';
    foreach ($models as $model_id => $model_name) {
      echo '<option value="' . esc_attr($model_id) . '"' . selected($value, $model_id, false) . '>' . esc_html($model_name) . '</option>';
    }
    echo '</select>';
    echo '<p class="description">' . __('Arya model version to use', 'baum-terminal') . '</p>';
  }

  /**
   * Get Arya system message
   */
  private function get_arya_system_message($is_news, $is_search) {
    $system = "You are Arya, a helpful AI assistant integrated into a terminal interface. ";
    $system .= "Respond in a terminal-friendly format with appropriate colors and formatting. ";
    $system .= "You represent free speech and open dialogue principles. ";

    if ($is_news) {
      $system .= "You have access to news aggregation capabilities. When asked about news, ";
      $system .= "provide current headlines and summaries in monospace format. ";
    }

    if ($is_search) {
      $system .= "You have web search capabilities. When asked to search for information, ";
      $system .= "provide relevant and up-to-date results. ";
    }

    $system .= "Use terminal colors: GREEN for success, RED for errors, YELLOW for warnings, ";
    $system .= "CYAN for information, and WHITE for normal text. ";
    $system .= "Keep responses concise and terminal-appropriate. ";
    $system .= "Embrace diverse viewpoints and encourage open discussion.";

    return $system;
  }

  /**
   * Call Arya API
   */
  private function call_arya_api($message, $system_message) {
    $api_key = get_option('baum_terminal_arya_api_key');
    $api_url = get_option('baum_terminal_arya_api_url', 'https://api.gab.com/v1/chat/completions');

    $headers = array(
      'Content-Type' => 'application/json',
      'Authorization' => 'Bearer ' . $api_key,
      'User-Agent' => 'BaumTerminal/1.0'
    );

    // Enhanced system message with terminal formatting instructions
    $enhanced_system = $system_message . "\n\n" .
      "IMPORTANT: Format your responses for terminal display:\n" .
      "- Use [GREEN]text[/GREEN] for success messages\n" .
      "- Use [RED]text[/RED] for errors or warnings\n" .
      "- Use [YELLOW]text[/YELLOW] for highlights or important info\n" .
      "- Use [CYAN]text[/CYAN] for headers and titles\n" .
      "- Use [BLUE]text[/BLUE] for links and references\n" .
      "- Keep lines under 80 characters when possible\n" .
      "- Use monospace formatting for code or structured data\n" .
      "- For news: format as headlines with sources and timestamps\n" .
      "- For search: provide title, URL, and brief description";

    $body = array(
      'model' => get_option('baum_terminal_arya_model', 'arya-1'),
      'max_tokens' => 1500,
      'temperature' => 0.7,
      'messages' => array(
        array(
          'role' => 'system',
          'content' => $enhanced_system
        ),
        array(
          'role' => 'user',
          'content' => $message
        )
      )
    );

    $response = wp_remote_post($api_url, array(
      'headers' => $headers,
      'body' => json_encode($body),
      'timeout' => 30
    ));

    if (is_wp_error($response)) {
      return array(
        'type' => 'error',
        'content' => 'Failed to connect to Arya API: ' . $response->get_error_message()
      );
    }

    $response_body = wp_remote_retrieve_body($response);
    $data = json_decode($response_body, true);

    if (isset($data['error'])) {
      return array(
        'type' => 'error',
        'content' => 'Arya API Error: ' . $data['error']['message']
      );
    }

    if (isset($data['choices'][0]['message']['content'])) {
      return array(
        'type' => 'success',
        'content' => $this->format_terminal_response($data['choices'][0]['message']['content'])
      );
    } else {
      return array(
        'type' => 'error',
        'content' => 'Unexpected response from Arya API'
      );
    }
  }
}

// Initialize the plugin
new BaumTerminal();
