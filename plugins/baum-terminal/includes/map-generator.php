<?php
/**
 * Simple ASCII Map Generator for Baum Terminal
 */

class BaumTerminalMapGenerator {

  /**
   * Generate ASCII map based on location
   */
  public function generate_map($location, $type = 'basic') {
    $location_lower = strtolower($location);

    // Try real ASCII maps first
    if (strpos($location_lower, 'real') !== false || strpos($location_lower, 'live') !== false) {
      return $this->get_real_ascii_map($location);
    }

    // Try server command line maps
    if (strpos($location_lower, 'server') !== false) {
      return $this->get_server_map($location);
    }

    // Claude-generated maps
    if (strpos($location_lower, 'claude') !== false) {
      return $this->get_claude_map($location);
    }

    // Simple built-in maps
    if (strpos($location_lower, 'world') !== false) {
      return $this->world_map();
    } elseif (strpos($location_lower, 'usa') !== false) {
      return $this->usa_map();
    } elseif (strpos($location_lower, 'europe') !== false) {
      return $this->europe_map();
    } else {
      return $this->help_map();
    }
  }

  private function world_map() {
    return "
WORLD MAP

    NORTH AMERICA    EUROPE    ASIA
    ############     ####      ####################
    ############     ####      ####################
    ############               ####################

                    AFRICA
                    ########
                    ########
    SOUTH AMERICA   ########
    ############    ########
    ############
    ############

                    AUSTRALIA
                    #######

                    ANTARCTICA
    ################################################

Simple world map - continents shown as blocks
";
  }

  private function usa_map() {
    return "
USA MAP

ALASKA          WA  OR  ID  MT  ND  MN  WI  MI
####            ##  ##  ##  ##  ##  ##  ##  ##
                CA  NV  UT  WY  SD  IA  IL  IN  OH
                ##  ##  ##  ##  ##  ##  ##  ##  ##
                    AZ  CO  NE  KS  MO  AR  TN  KY
                    ##  ##  ##  ##  ##  ##  ##  ##
                        NM  OK  TX  LA  MS  AL  GA
                        ##  ##  ##  ##  ##  ##  ##
                                    FL
                                    ##
HAWAII
####

50 US States shown as simple blocks
";
  }

  private function europe_map() {
    return "
EUROPE MAP

        ICELAND
        ###

    UK      NORWAY  SWEDEN  FINLAND
    ###     ######  ######  #######

    IRELAND DENMARK         RUSSIA
    #####   #######         ############

    FRANCE  GERMANY POLAND
    ######  ####### #######

    SPAIN   ITALY   AUSTRIA HUNGARY
    #####   #####   ####### #######

    PORTUGAL        GREECE
    ########        ######

European countries as simple blocks
";
  }

  private function help_map() {
    return "
MAP HELP

Built-in maps:
• map world     - Simple world map
• map usa       - United States map
• map europe    - Europe map

Real ASCII maps:
• map real world     - Real world ASCII map
• map real usa       - Real USA ASCII map
• map claude london  - Claude-generated map of London
• map server world   - Server command-line map

Examples:
• map world
• map real usa
• map claude paris

Simple ASCII maps for terminal display
";
  }

  /**
   * Get real ASCII map using external data
   */
  private function get_real_ascii_map($location) {
    // Try to get real map data
    $map_data = $this->fetch_real_map_data($location);

    if ($map_data) {
      return $map_data;
    }

    return "
REAL ASCII MAP

Real ASCII maps require external data sources.
Available options:
• OpenStreetMap ASCII renderer
• Mapscii-style real-time maps
• Geographic data conversion

Try: map claude [location] for AI-generated maps
";
  }

  /**
   * Get server-side command line map
   */
  private function get_server_map($location) {
    // Check if server has mapping tools
    $tools = $this->check_server_mapping_tools();

    if ($tools['mapscii']) {
      return $this->run_mapscii($location);
    } elseif ($tools['curl']) {
      return $this->fetch_ascii_map_api($location);
    } else {
      return "
SERVER MAP

Server mapping tools not available.
Install options:
• npm install -g mapscii
• curl for API access
• Custom ASCII map generators

Current server capabilities: " . implode(', ', array_keys(array_filter($tools)));
    }
  }

  /**
   * Get Claude-generated ASCII map
   */
  private function get_claude_map($location) {
    // Extract location from command
    $location_name = str_replace(['map', 'claude'], '', $location);
    $location_name = trim($location_name);

    return "
CLAUDE ASCII MAP REQUEST

Location: " . strtoupper($location_name) . "

[This would send a request to Claude API to generate
a detailed ASCII map of " . $location_name . "]

Features:
• AI-generated geographic layout
• Landmark identification
• Street-level detail
• Real geographic data

Note: Requires Claude API integration for real-time generation.
";
  }

  /**
   * Fetch real map data from external sources
   */
  private function fetch_real_map_data($location) {
    // Try different real map sources

    // 1. Try ASCII Map API
    $ascii_map = $this->try_ascii_map_api($location);
    if ($ascii_map) return $ascii_map;

    // 2. Try OpenStreetMap conversion
    $osm_map = $this->try_osm_ascii_conversion($location);
    if ($osm_map) return $osm_map;

    // 3. Try cached real maps
    $cached_map = $this->try_cached_real_maps($location);
    if ($cached_map) return $cached_map;

    return false;
  }

  /**
   * Check what mapping tools are available on server
   */
  private function check_server_mapping_tools() {
    $tools = array();

    // Check for mapscii
    $tools['mapscii'] = $this->command_exists('mapscii');

    // Check for curl
    $tools['curl'] = $this->command_exists('curl');

    // Check for node
    $tools['node'] = $this->command_exists('node');

    // Check for python
    $tools['python'] = $this->command_exists('python') || $this->command_exists('python3');

    return $tools;
  }

  /**
   * Check if command exists on server
   */
  private function command_exists($command) {
    $return_var = null;
    $output = null;
    exec("which $command 2>/dev/null", $output, $return_var);
    return $return_var === 0;
  }

  /**
   * Run mapscii command if available
   */
  private function run_mapscii($location) {
    if (!$this->command_exists('mapscii')) {
      return "Mapscii not installed. Install with: npm install -g mapscii";
    }

    // This would run actual mapscii - but it's interactive
    return "
MAPSCII INTEGRATION

Mapscii is available but requires interactive terminal.
For web terminal integration, we need:
• Headless mapscii rendering
• ASCII output capture
• Location coordinate conversion

Install: npm install -g mapscii
Usage: mapscii --help

Alternative: Use ASCII map APIs or Claude generation.
";
  }

  /**
   * Try ASCII Map API
   */
  private function try_ascii_map_api($location) {
    // Example API endpoints for ASCII maps
    $apis = array(
      'https://ascii-maps.herokuapp.com/api/map',
      'https://mapscii.me/api/ascii',
      'https://api.openstreetmap.org/ascii'
    );

    foreach ($apis as $api) {
      $result = $this->fetch_from_api($api, $location);
      if ($result) return $result;
    }

    return false;
  }

  /**
   * Try OpenStreetMap ASCII conversion
   */
  private function try_osm_ascii_conversion($location) {
    // This would convert OSM data to ASCII
    // Requires geographic coordinate lookup and ASCII rendering

    return "
OSM ASCII CONVERSION

OpenStreetMap ASCII conversion requires:
• Geocoding API for coordinates
• OSM data fetching
• ASCII rendering engine
• Coordinate-to-character mapping

Location: " . $location . "
Status: Implementation needed
";
  }

  /**
   * Try cached real maps
   */
  private function try_cached_real_maps($location) {
    // Check for pre-generated real ASCII maps
    $cached_maps = array(
      'world' => $this->get_detailed_world_map(),
      'usa' => $this->get_detailed_usa_map(),
      'europe' => $this->get_detailed_europe_map()
    );

    $location_key = strtolower(str_replace(['real', 'map'], '', $location));
    $location_key = trim($location_key);

    if (isset($cached_maps[$location_key])) {
      return $cached_maps[$location_key];
    }

    return false;
  }

  /**
   * Fetch from external API
   */
  private function fetch_from_api($api_url, $location) {
    if (!function_exists('curl_init')) {
      return false;
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url . '?location=' . urlencode($location));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_USERAGENT, 'BaumTerminal/1.0');

    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code === 200 && $result) {
      return $result;
    }

    return false;
  }

  /**
   * Get detailed world map with real geographic features
   */
  private function get_detailed_world_map() {
    return "
DETAILED WORLD MAP (Real Geographic Features)

                    ARCTIC OCEAN
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    GREENLAND        ICELAND    NORWAY  SWEDEN  FINLAND
    #########        ####       ######  ######  #######
                                                        RUSSIA
    CANADA                                              ##############
    ##############   UK    DENMARK                     ##############
    ##############   ###   #######     POLAND UKRAINE  ##############
    ##############         GERMANY     ###### ######   ##############
                           #######     ###### ######
    USA                    FRANCE      CZECH  SLOVAKIA
    ##########             ######      ##### #######
    ##########             SWITZERLAND AUSTRIA HUNGARY ROMANIA
    ##########             ###########  ###### ####### #######
                           ITALY
                           #####       TURKEY  GEORGIA
                           SPAIN       ######  #######
                           #####
                           PORTUGAL    GREECE
                           ########    ######

                    ATLANTIC OCEAN              MEDITERRANEAN SEA
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    MOROCCO ALGERIA TUNISIA LIBYA    EGYPT     IRAN    AFGHANISTAN
    ####### ####### ####### #####    #####     ####    ###########

                    SAHARA DESERT               SAUDI   PAKISTAN
                    #############               ARABIA  ########
                                               ######
    MAURITANIA MALI NIGER   CHAD    SUDAN
    ########## #### #####   ####    #####      INDIA   CHINA
                                               #####   ##########
    SENEGAL BURKINA NIGERIA         ETHIOPIA   #####   ##########
    ####### ####### #######         ########  #####   ##########

    GUINEA  GHANA   CAMEROON CONGO   KENYA
    ######  #####   ######## #####   #####     MYANMAR THAILAND
                                               ####### ########
    IVORY   TOGO    GABON    DRC     TANZANIA
    COAST   ####    #####    ###     ########  LAOS    VIETNAM
    #####                                      ####    #######

                            ANGOLA   ZAMBIA    CAMBODIA
                            ######   ######    ########

                            NAMIBIA  BOTSWANA ZIMBABWE  MALAYSIA
                            ####### ######## ########  ########

                            SOUTH AFRICA      SINGAPORE
                            ############      #########

                    INDIAN OCEAN              INDONESIA
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                                               #########

                            AUSTRALIA         PHILIPPINES
                            #########         ###########
                            #########
                            #########         JAPAN
                                               #####
                            NEW ZEALAND
                            ###########       SOUTH KOREA
                                               ###########

                    PACIFIC OCEAN             NORTH KOREA
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                                               ###########

Real geographic boundaries and country positions
Based on actual world map data
";
  }

  /**
   * Get detailed USA map with real state boundaries
   */
  private function get_detailed_usa_map() {
    return "
DETAILED USA MAP (Real State Boundaries)

ALASKA                                              MAINE
######                                              #####
######                                              #####
                                                    NEW HAMPSHIRE
                                                    #############
                                                    VERMONT
                                                    #######
    WASHINGTON    MONTANA      NORTH DAKOTA  MINNESOTA    WISCONSIN
    ##########    #######      ############  #########    #########
    ##########    #######      ############  #########    #########
    ##########    #######      ############  #########    #########
                                                                    MICHIGAN
    OREGON        IDAHO        SOUTH DAKOTA  IOWA         ILLINOIS  #########
    ######        #####        ############  ####         ########  #########
    ######        #####        ############  ####         ########
    ######        #####        ############  ####         ########  INDIANA OHIO
                                                          ######## #### ####
    CALIFORNIA    NEVADA  UTAH COLORADO     NEBRASKA MISSOURI      #### ####
    ##########    ###### #### ########     ######## ########      #### ####
    ##########    ###### #### ########     ######## ########
    ##########    ###### #### ########     ######## ########      KENTUCKY
    ##########           #### ########     ######## ########      ########
    ##########
                         ARIZONA NEW MEXICO KANSAS   ARKANSAS TENNESSEE
                         ####### ########## ######   ######## #########
                         ####### ########## ######   ######## #########
                         ####### ########## ######   ######## #########

                                  OKLAHOMA  TEXAS    MISSISSIPPI
                                  ######## #######   ###########
                                  ######## #######   ###########
                                  ######## #######   ###########
                                  ######## #######
                                  ######## #######   ALABAMA GEORGIA
                                           #######    ####### #######
                                           #######    ####### #######
                                           #######    ####### #######

                                           LOUISIANA
                                           #########  FLORIDA
                                           #########  #######
                                           #########  #######
                                                     #######

HAWAII
######
######

Real state boundaries and geographic positions
Based on actual US map data
";
  }

  /**
   * Get detailed Europe map
   */
  private function get_detailed_europe_map() {
    return "
DETAILED EUROPE MAP (Real Country Boundaries)

                    ICELAND
                    #######
                    #######

                    FAROE ISLANDS
                    #############

    IRELAND         UNITED KINGDOM        NORWAY          SWEDEN
    #######         ##############        ######          ######
    #######         ##############        ######          ######
    #######         ##############        ######          ######
                    ##############        ######          ######
                                         ######          ######
                    NETHERLANDS           ######          ######
                    ###########
                                         DENMARK         FINLAND
                    BELGIUM               #######         #######
                    #######               #######         #######
                                         #######         #######
    FRANCE          LUXEMBOURG
    ######          ##########            GERMANY         ESTONIA
    ######          ##########            #######         #######
    ######                               #######
    ######          SWITZERLAND          #######         LATVIA
    ######          ###########           #######         ######
    ######          ###########
    ######                               POLAND          LITHUANIA
                    LIECHTENSTEIN         ######          #########
    SPAIN           #############         ######          #########
    #####
    #####           AUSTRIA               CZECH REPUBLIC  BELARUS
    #####           #######               ##############  #######
    #####           #######               ##############  #######
    #####
                    SLOVENIA              SLOVAKIA        UKRAINE
    PORTUGAL        ########              ########        #######
    ########        ########              ########        #######
    ########
    ########        CROATIA               HUNGARY         MOLDOVA
                    #######               #######         #######
    ANDORRA         #######               #######         #######
    #######
                    BOSNIA &              ROMANIA
                    HERZEGOVINA           #######         RUSSIA
                    ###########           #######         ######
                    ###########           #######         ######

                    MONTENEGRO            SERBIA
                    ##########            ######
                    ##########            ######

    ITALY           ALBANIA               BULGARIA
    #####           #######               ########
    #####           #######               ########
    #####
    #####           NORTH MACEDONIA
                    ###############
    VATICAN         ###############
    #######
                    GREECE                TURKEY
    SAN MARINO      ######                ######
    ##########      ######                ######
                    ######                ######
    MONACO
    ######          CYPRUS
                    ######
    MALTA
    #####

Real European country boundaries and geographic positions
Based on actual European map data
";
  }

}
