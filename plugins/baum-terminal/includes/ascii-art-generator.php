<?php
/**
 * ASCII Art Generator for Baum Terminal
 * Intelligently generates ASCII art for site names and responses
 */

class BaumTerminalAsciiGenerator {
  
  private $fonts = array();
  private $cache_duration = 3600; // 1 hour
  
  public function __construct() {
    $this->init_fonts();
  }
  
  /**
   * Initialize ASCII fonts
   */
  private function init_fonts() {
    $this->fonts = array(
      'block' => array(
        'height' => 3,
        'chars' => array(
          'A' => array('██████', '██  ██', '██████'),
          'B' => array('██████', '██████', '██████'),
          'C' => array('██████', '██    ', '██████'),
          'D' => array('██████', '██  ██', '██████'),
          'E' => array('██████', '██████', '██████'),
          'F' => array('██████', '██████', '██    '),
          'G' => array('██████', '██ ███', '██████'),
          'H' => array('██  ██', '██████', '██  ██'),
          'I' => array('██████', '  ██  ', '██████'),
          'J' => array('██████', '    ██', '██████'),
          'K' => array('██  ██', '██████', '██  ██'),
          'L' => array('██    ', '██    ', '██████'),
          'M' => array('██████', '██████', '██  ██'),
          'N' => array('██████', '██  ██', '██  ██'),
          'O' => array('██████', '██  ██', '██████'),
          'P' => array('██████', '██████', '██    '),
          'Q' => array('██████', '██ ███', '██████'),
          'R' => array('██████', '██████', '██  ██'),
          'S' => array('██████', '██████', '██████'),
          'T' => array('██████', '  ██  ', '  ██  '),
          'U' => array('██  ██', '██  ██', '██████'),
          'V' => array('██  ██', '██  ██', ' ████ '),
          'W' => array('██  ██', '██████', '██████'),
          'X' => array('██  ██', ' ████ ', '██  ██'),
          'Y' => array('██  ██', ' ████ ', '  ██  '),
          'Z' => array('██████', ' ████ ', '██████'),
          ' ' => array('      ', '      ', '      '),
          '0' => array('██████', '██  ██', '██████'),
          '1' => array('  ██  ', '  ██  ', '  ██  '),
          '2' => array('██████', '██████', '██████'),
          '3' => array('██████', '██████', '██████'),
          '4' => array('██  ██', '██████', '    ██'),
          '5' => array('██████', '██████', '██████'),
          '6' => array('██████', '██████', '██████'),
          '7' => array('██████', '    ██', '    ██'),
          '8' => array('██████', '██████', '██████'),
          '9' => array('██████', '██████', '██████')
        )
      ),
      'small' => array(
        'height' => 1,
        'chars' => array(
          'A' => array('▄▀█'),
          'B' => array('█▄▄'),
          'C' => array('▄▀█'),
          'D' => array('█▄▀'),
          'E' => array('█▀▀'),
          'F' => array('█▀▀'),
          'G' => array('▄▀█'),
          'H' => array('█▀█'),
          'I' => array('█'),
          'J' => array('  █'),
          'K' => array('█▄▀'),
          'L' => array('█  '),
          'M' => array('█▄█'),
          'N' => array('█▄█'),
          'O' => array('█▀█'),
          'P' => array('█▀▄'),
          'Q' => array('█▀█'),
          'R' => array('█▀▄'),
          'S' => array('▄▀█'),
          'T' => array('▀█▀'),
          'U' => array('█ █'),
          'V' => array('█ █'),
          'W' => array('█ █'),
          'X' => array('▀▄▀'),
          'Y' => array('█ █'),
          'Z' => array('▀▀▀'),
          ' ' => array(' '),
          '0' => array('█▀█'),
          '1' => array('▄█ '),
          '2' => array('▀▀▀'),
          '3' => array('▀▀▀'),
          '4' => array('█ █'),
          '5' => array('▀▀▀'),
          '6' => array('▀▀▀'),
          '7' => array('▀▀▀'),
          '8' => array('▀▀▀'),
          '9' => array('▀▀▀')
        )
      ),
      'banner' => array(
        'height' => 5,
        'chars' => array(
          'A' => array(
            '  ██  ',
            ' ████ ',
            '██  ██',
            '██████',
            '██  ██'
          ),
          'B' => array(
            '██████',
            '██  ██',
            '██████',
            '██  ██',
            '██████'
          ),
          'C' => array(
            ' █████',
            '██    ',
            '██    ',
            '██    ',
            ' █████'
          ),
          // Add more letters as needed...
        )
      )
    );
  }
  
  /**
   * Generate ASCII art for text
   */
  public function generate_text_art($text, $font = 'block', $color = 'green') {
    $cache_key = 'baum_ascii_' . md5($text . $font . $color);
    $cached = get_transient($cache_key);
    
    if ($cached !== false) {
      return $cached;
    }
    
    $text = strtoupper($text);
    $font_data = $this->fonts[$font] ?? $this->fonts['block'];
    $height = $font_data['height'];
    $chars = $font_data['chars'];
    
    $lines = array_fill(0, $height, '');
    
    for ($i = 0; $i < strlen($text); $i++) {
      $char = $text[$i];
      $char_art = $chars[$char] ?? $chars[' '];
      
      for ($line = 0; $line < $height; $line++) {
        $lines[$line] .= $char_art[$line] ?? str_repeat(' ', 6);
      }
    }
    
    $result = implode("\n", $lines);

    // Don't add color wrapper - return clean text for terminal

    // Cache the result
    set_transient($cache_key, $result, $this->cache_duration);

    return $result;
  }
  
  /**
   * Generate contextual ASCII art based on response content
   */
  public function generate_contextual_art($content, $context = '') {
    $art_type = $this->detect_art_type($content, $context);
    
    switch ($art_type) {
      case 'news':
        return $this->generate_news_art();
      case 'search':
        return $this->generate_search_art();
      case 'economic':
        return $this->generate_economic_art();
      case 'weather':
        return $this->generate_weather_art();
      case 'error':
        return $this->generate_error_art();
      case 'success':
        return $this->generate_success_art();
      default:
        return $this->generate_generic_art();
    }
  }
  
  /**
   * Detect what type of ASCII art to generate
   */
  private function detect_art_type($content, $context) {
    $content_lower = strtolower($content);
    
    if (strpos($content_lower, 'news') !== false || strpos($content_lower, 'headline') !== false) {
      return 'news';
    }
    
    if (strpos($content_lower, 'search') !== false || strpos($content_lower, 'found') !== false) {
      return 'search';
    }
    
    if (strpos($content_lower, 'economic') !== false || strpos($content_lower, 'gdp') !== false || strpos($content_lower, 'inflation') !== false) {
      return 'economic';
    }
    
    if (strpos($content_lower, 'weather') !== false || strpos($content_lower, 'temperature') !== false) {
      return 'weather';
    }
    
    if (strpos($content_lower, 'error') !== false || strpos($content_lower, 'failed') !== false) {
      return 'error';
    }
    
    if (strpos($content_lower, 'success') !== false || strpos($content_lower, 'complete') !== false) {
      return 'success';
    }
    
    return 'generic';
  }
  
  /**
   * Generate news-themed ASCII art
   */
  private function generate_news_art() {
    return "
    ███╗   ██╗███████╗██╗    ██╗███████╗
    ████╗  ██║██╔════╝██║    ██║██╔════╝
    ██╔██╗ ██║█████╗  ██║ █╗ ██║███████╗
    ██║╚██╗██║██╔══╝  ██║███╗██║╚════██║
    ██║ ╚████║███████╗╚███╔███╔╝███████║
    ╚═╝  ╚═══╝╚══════╝ ╚══╝╚══╝ ╚══════╝
    ";
  }
  
  /**
   * Generate search-themed ASCII art
   */
  private function generate_search_art() {
    return "
    ╔═══════════════════════════════════╗
    ║  🔍 SEARCH RESULTS               ║
    ╠═══════════════════════════════════╣
    ║                                   ║
    ╚═══════════════════════════════════╝
    ";
  }
  
  /**
   * Generate economic-themed ASCII art
   */
  private function generate_economic_art() {
    return "
    $$$$$$$$\\  $$$$$$\\  $$$$$$\\  $$\\   $$\\ 
    $$  _____|$$  __$$\\ $$  __$$\\ $$$\\  $$ |
    $$ |      $$ /  \\__|$$ /  $$ |$$$$\\ $$ |
    $$$$$\\    $$ |      $$ |  $$ |$$ $$\\$$ |
    $$  __|   $$ |      $$ |  $$ |$$ \\$$$$ |
    $$ |      $$ |  $$\\ $$ |  $$ |$$ |\\$$$ |
    $$$$$$$$\\ \\$$$$$$  | $$$$$$  |$$ | \\$$ |
    \\________| \\______/  \\______/ \\__|  \\__|
    ";
  }
  
  /**
   * Generate weather-themed ASCII art
   */
  private function generate_weather_art() {
    return "
         .--.
      .-(    ).
     (___.__)__)
       ☀️ ⛅ 🌧️
    ";
  }
  
  /**
   * Generate error-themed ASCII art
   */
  private function generate_error_art() {
    return "
    ███████╗██████╗ ██████╗  ██████╗ ██████╗ 
    ██╔════╝██╔══██╗██╔══██╗██╔═══██╗██╔══██╗
    █████╗  ██████╔╝██████╔╝██║   ██║██████╔╝
    ██╔══╝  ██╔══██╗██╔══██╗██║   ██║██╔══██╗
    ███████╗██║  ██║██║  ██║╚██████╔╝██║  ██║
    ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚═╝  ╚═╝
    ";
  }
  
  /**
   * Generate success-themed ASCII art
   */
  private function generate_success_art() {
    return "
    ✓ SUCCESS ✓
    ═══════════
    ";
  }
  
  /**
   * Generate generic ASCII art
   */
  private function generate_generic_art() {
    $patterns = array(
      "▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓",
      "░░░░░░░░░░░░░░░░░░░░",
      "████████████████████",
      "▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀",
      "▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄"
    );
    
    return $patterns[array_rand($patterns)];
  }
  
  /**
   * Add color wrapper to ASCII art - return clean text for terminal
   */
  private function add_color_wrapper($art, $color) {
    // Return clean art without color tags for better terminal display
    return $art;
  }
  
  /**
   * Generate ASCII chart from data
   */
  public function generate_ascii_chart($data, $title = '', $width = 60, $height = 10) {
    if (empty($data)) {
      return "No data available for chart";
    }
    
    $max_value = max($data);
    $min_value = min($data);
    $range = $max_value - $min_value;
    
    if ($range == 0) {
      $range = 1;
    }
    
    $chart = array();
    
    // Add title
    if ($title) {
      $chart[] = str_pad($title, $width, ' ', STR_PAD_BOTH);
      $chart[] = str_repeat('═', $width);
    }
    
    // Generate chart bars
    for ($i = $height - 1; $i >= 0; $i--) {
      $line = '';
      $threshold = $min_value + ($range * $i / ($height - 1));
      
      foreach ($data as $value) {
        if ($value >= $threshold) {
          $line .= '█';
        } else {
          $line .= ' ';
        }
      }
      
      $chart[] = sprintf('%6.1f │%s', $threshold, $line);
    }
    
    // Add bottom axis
    $chart[] = str_repeat(' ', 7) . '└' . str_repeat('─', count($data));
    
    return implode("\n", $chart);
  }
  
  /**
   * Generate ASCII map representation
   */
  public function generate_ascii_map($data, $regions) {
    // Simple ASCII map generation
    // This would be enhanced with actual geographic data
    $map = array();
    
    foreach ($regions as $region => $coords) {
      $value = $data[$region] ?? 0;
      $symbol = $this->get_map_symbol($value);
      $map[] = sprintf("%-15s %s %s", $region, $symbol, $value);
    }
    
    return implode("\n", $map);
  }
  
  /**
   * Get map symbol based on value
   */
  private function get_map_symbol($value) {
    if ($value > 75) return '█';
    if ($value > 50) return '▓';
    if ($value > 25) return '▒';
    if ($value > 0) return '░';
    return ' ';
  }
  
  /**
   * Generate intelligent site name art
   */
  public function generate_site_name_art($site_name, $style = 'auto') {
    // Clear any old cache first
    $old_cache_key = 'baum_site_ascii_' . md5($site_name . $style);
    delete_transient($old_cache_key);

    $cache_key = 'baum_site_ascii_v2_' . md5($site_name . $style);
    $cached = get_transient($cache_key);

    if ($cached !== false) {
      return $cached;
    }

    // Create simple, clean ASCII art for terminal
    $clean_name = strtoupper(substr($site_name, 0, 20));

    // Simple block style that works well in terminal
    $art = "\n";
    $art .= "╔" . str_repeat("═", strlen($clean_name) + 2) . "╗\n";
    $art .= "║ " . $clean_name . " ║\n";
    $art .= "╚" . str_repeat("═", strlen($clean_name) + 2) . "╝\n";
    $art .= "\n";

    // Cache the result
    set_transient($cache_key, $art, $this->cache_duration);

    return $art;
  }
}
