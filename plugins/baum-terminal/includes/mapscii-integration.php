<?php
/**
 * Mapscii Integration for Place CPT
 * Integrates mapscii.js for interactive maps in browser
 */

class BaumTerminalMapsciiIntegration {
  
  /**
   * Initialize mapscii integration
   */
  public function __construct() {
    add_action('wp_enqueue_scripts', array($this, 'enqueue_mapscii_assets'));
    add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_mapscii_assets'));
    add_action('wp_footer', array($this, 'add_mapscii_modal'));
    add_action('admin_footer', array($this, 'add_mapscii_modal'));
    add_action('wp_ajax_get_place_coordinates', array($this, 'get_place_coordinates'));
    add_action('wp_ajax_nopriv_get_place_coordinates', array($this, 'get_place_coordinates'));
    add_action('wp_ajax_save_place_coordinates', array($this, 'save_place_coordinates'));
    add_action('wp_ajax_nopriv_save_place_coordinates', array($this, 'save_place_coordinates'));
  }
  
  /**
   * Enqueue mapscii assets for frontend
   */
  public function enqueue_mapscii_assets() {
    // Load mapscii scripts and styles with frontend context
    $this->load_mapscii_scripts('frontend');
    $this->load_mapscii_styles('frontend');

    // Localize script for frontend AJAX
    wp_localize_script('baum-mapscii-integration-frontend', 'baumMapscii', array(
      'ajaxUrl' => admin_url('admin-ajax.php'),
      'nonce' => wp_create_nonce('baum_mapscii_nonce'),
      'isAdmin' => false
    ));
  }

  /**
   * Enqueue mapscii assets for admin
   */
  public function enqueue_admin_mapscii_assets($hook) {
    // Load on place edit screens and any admin page that might need mapscii
    global $post_type, $pagenow;

    $load_mapscii = false;

    // Load on place edit screens
    if ($post_type === 'place' && in_array($hook, ['post.php', 'post-new.php'])) {
      $load_mapscii = true;
    }

    // Load on place list screen
    if ($hook === 'edit.php' && $post_type === 'place') {
      $load_mapscii = true;
    }

    // Load on any admin page if we detect place-related content
    if (isset($_GET['post']) && get_post_type($_GET['post']) === 'place') {
      $load_mapscii = true;
    }

    if (!$load_mapscii) {
      return;
    }

    // Load mapscii assets for admin
    $this->load_mapscii_scripts('admin');
    $this->load_mapscii_styles('admin');

    // Localize script for admin AJAX
    wp_localize_script('baum-mapscii-integration-admin', 'baumMapscii', array(
      'ajaxUrl' => admin_url('admin-ajax.php'),
      'nonce' => wp_create_nonce('baum_mapscii_nonce'),
      'isAdmin' => true
    ));
  }

  /**
   * Load mapscii scripts (shared between frontend and admin)
   */
  private function load_mapscii_scripts($context = 'frontend') {
    // Create unique script handle for admin vs frontend
    $script_handle = 'baum-mapscii-integration-' . $context;

    // Check if already enqueued to prevent double loading
    if (wp_script_is($script_handle, 'enqueued')) {
      return;
    }

    // Load xterm.js for real terminal
    wp_enqueue_script(
      'xterm-js',
      'https://cdn.jsdelivr.net/npm/xterm@5.3.0/lib/xterm.min.js',
      array(),
      '5.3.0',
      true
    );

    // Load xterm.js addons
    wp_enqueue_script(
      'xterm-addon-fit',
      'https://cdn.jsdelivr.net/npm/xterm-addon-fit@0.8.0/lib/xterm-addon-fit.min.js',
      array('xterm-js'),
      '0.8.0',
      true
    );

    wp_enqueue_script(
      'xterm-addon-web-links',
      'https://cdn.jsdelivr.net/npm/xterm-addon-web-links@0.9.0/lib/xterm-addon-web-links.min.js',
      array('xterm-js'),
      '0.9.0',
      true
    );

    // Enqueue our mapscii integration with unique handle
    wp_enqueue_script(
      $script_handle,
      BAUM_TERMINAL_PLUGIN_URL . 'assets/mapscii-integration.js',
      array('jquery', 'xterm-js', 'xterm-addon-fit'),
      '1.0.0',
      true
    );

    // Initialize xterm.js terminal connection to mapscii.me
    wp_add_inline_script($script_handle, '
      window.MapsciiTerminal = function(options) {
        this.container = options.container;
        this.center = options.center || [0, 0];
        this.zoom = options.zoom || 10;
        this.terminal = null;
        this.fitAddon = null;
        this.socket = null;
        this.isConnected = false;

        this.init();
      };

      window.MapsciiTerminal.prototype.init = function() {
        this.setupContainer();
        this.createTerminal();
        this.connectToMapscii();
      };

      window.MapsciiTerminal.prototype.setupContainer = function() {
        this.container.innerHTML = `
          <div class="mapscii-terminal-container">
            <div class="mapscii-status">Connecting to mapscii.me...</div>
            <div id="mapscii-terminal" style="
              width: 100%;
              height: 600px;
              background: #000;
            "></div>
          </div>
        `;

        this.terminalElement = this.container.querySelector("#mapscii-terminal");
        this.status = this.container.querySelector(".mapscii-status");
      };

      window.MapsciiTerminal.prototype.createTerminal = function() {
        // Create xterm.js terminal
        this.terminal = new Terminal({
          cursorBlink: true,
          fontSize: 12,
          fontFamily: "monospace",
          theme: {
            background: "#000000",
            foreground: "#00ff00",
            cursor: "#00ff00",
            selection: "#ffffff"
          },
          cols: 120,
          rows: 40
        });

        // Add fit addon
        this.fitAddon = new FitAddon.FitAddon();
        this.terminal.loadAddon(this.fitAddon);

        // Add web links addon
        this.terminal.loadAddon(new WebLinksAddon.WebLinksAddon());

        // Open terminal in container
        this.terminal.open(this.terminalElement);
        this.fitAddon.fit();

        // Handle terminal input - forward all user input to MapSCII
        this.terminal.onData((data) => {
          if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            console.log(`Sending user input to MapSCII: "${data}" (${data.charCodeAt(0)})`);
            this.socket.send(data);
          }
        });

        // Enable focus for keyboard input
        this.terminal.focus();
      };

      window.MapsciiTerminal.prototype.connectToMapscii = function() {
        try {
          // Connect to mapscii.me via WebSocket
          this.connectViaWebSocket();
        } catch (error) {
          console.error("Connection failed:", error);
          this.showConnectionError();
        }
      };

      window.MapsciiTerminal.prototype.connectViaWebSocket = function() {
        // mapscii.me uses Telnet (port 23), not WebSocket
        // We need to use a WebSocket-to-Telnet bridge

        this.status.textContent = "Connecting to mapscii.me...";

        // Try different connection methods in order
        this.tryDirectWebSocket();
      };

      window.MapsciiTerminal.prototype.tryDirectWebSocket = function() {
        // Detect if we\'re on HTTPS and need secure WebSocket
        const isSecure = window.location.protocol === "https:";
        const wsProtocol = isSecure ? "wss:" : "ws:";
        const wsPort = isSecure ? "8443" : "8080";

        this.status.textContent = "Connecting to local proxy...";

        try {
          const wsUrl = `${wsProtocol}//localhost:${wsPort}`;
          console.log(`Attempting connection to: ${wsUrl}`);
          this.socket = new WebSocket(wsUrl);

          this.socket.onopen = () => {
            this.isConnected = true;
            this.status.textContent = "Connected to mapscii.me via proxy";
            this.terminal.write("\\r\\n\\x1b[32m✓ Connected to MapSCII via local proxy\\x1b[0m\\r\\n");
            this.terminal.write("\\x1b[90mUse WASD or arrow keys to navigate, +/- to zoom\\x1b[0m\\r\\n");
            this.terminal.write("\\x1b[90mWaiting for mapscii data...\\x1b[0m\\r\\n\\r\\n");
            console.log("Connected to mapscii.me via local websockify proxy");

            // Start interactive session immediately
            setTimeout(() => {
              this.startInteractiveSession();
            }, 500); // Much faster - only 0.5 seconds
          };

          this.socket.onmessage = (event) => {
            console.log("Received data from mapscii:", event.data.length, "bytes");

            // Handle both text and binary data from mapscii
            if (typeof event.data === "string") {
              this.terminal.write(event.data);
            } else {
              // Convert binary data to string
              const reader = new FileReader();
              reader.onload = () => {
                console.log("Converted binary data to text:", reader.result.length, "chars");
                this.terminal.write(reader.result);
              };
              reader.readAsText(event.data);
            }
          };

          this.socket.onclose = (event) => {
            this.isConnected = false;
            console.log(`Local proxy connection closed (code: ${event.code}, reason: ${event.reason})`);

            // If we received data and connection closed normally, it\'s likely MapSCII timeout
            if (event.code === 1005 || event.code === 1000) {
              this.status.textContent = "MapSCII session ended";
              this.terminal.write("\\r\\n\\x1b[33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\\x1b[0m\\r\\n");
              this.terminal.write("\\x1b[32m✓ MapSCII connection completed successfully\\x1b[0m\\r\\n");
              this.terminal.write("\\x1b[90mMapSCII typically shows a welcome screen and then disconnects.\\x1b[0m\\r\\n");
              this.terminal.write("\\x1b[90mFor interactive maps, try: telnet mapscii.me or ssh mapscii.me\\x1b[0m\\r\\n");
              this.terminal.write("\\r\\n\\x1b[36mPress r to reconnect or close this modal\\x1b[0m\\r\\n");
              this.terminal.write("\\x1b[33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\\x1b[0m\\r\\n");

              // Add reconnect functionality
              this.addReconnectHandler();
            } else {
              this.status.textContent = "Connection error";
              this.tryAlternativeProxy();
            }
          };

          this.socket.onerror = (error) => {
            console.log(`Local proxy failed (${wsUrl}), trying alternatives...`);
            this.tryAlternativeProxy();
          };

        } catch (error) {
          console.log("Local proxy creation failed, trying alternatives...");
          this.tryAlternativeProxy();
        }
      };

      window.MapsciiTerminal.prototype.tryAlternativeProxy = function() {
        // Try alternative proxy ports/URLs based on current protocol
        this.status.textContent = "Trying alternative connections...";

        const isSecure = window.location.protocol === "https:";
        const wsProtocol = isSecure ? "wss:" : "ws:";

        // Only try a few real alternatives, not random ports
        const alternativeUrls = isSecure ? [
          `${wsProtocol}//baumpress.localhost:8443` // WordPress hostname with same port
        ] : [
          `${wsProtocol}//localhost:8081`          // Standard alternative port
        ];

        this.tryProxyUrl(0, alternativeUrls);
      };

      window.MapsciiTerminal.prototype.tryProxyUrl = function(index, urls) {
        if (index >= urls.length) {
          this.showConnectionError();
          return;
        }

        const url = urls[index];
        this.status.textContent = `Trying proxy ${index + 1}/${urls.length}...`;

        try {
          this.socket = new WebSocket(url);

          this.socket.onopen = () => {
            this.isConnected = true;
            this.status.textContent = "Connected via proxy";
            this.terminal.write("\\r\\n\\x1b[32mConnected to MapSCII via proxy\\x1b[0m\\r\\n");
            console.log(`Connected via proxy: ${url}`);
          };

          this.socket.onmessage = (event) => {
            this.terminal.write(event.data);
          };

          this.socket.onclose = () => {
            this.isConnected = false;
            console.log(`Proxy ${index + 1} failed, trying next...`);
            this.tryProxyUrl(index + 1, urls);
          };

          this.socket.onerror = (error) => {
            console.log(`Proxy ${index + 1} error, trying next...`);
            this.tryProxyUrl(index + 1, urls);
          };

        } catch (error) {
          console.log(`Proxy ${index + 1} creation failed, trying next...`);
          this.tryProxyUrl(index + 1, urls);
        }
      };

      window.MapsciiTerminal.prototype.showConnectionError = function() {
        const isSecure = window.location.protocol === "https:";
        const requiredPort = isSecure ? "8443" : "8080";
        const wsProtocol = isSecure ? "wss:" : "ws:";

        this.status.textContent = "Connection setup required";
        this.terminal.write("\\r\\n\\x1b[33m╔══════════════════════════════════════════════════════════════════════════════╗\\x1b[0m\\r\\n");
        this.terminal.write("\\x1b[33m║\\x1b[0m \\x1b[1;37mMapSCII Terminal Connection\\x1b[0m                                           \\x1b[33m║\\x1b[0m\\r\\n");
        this.terminal.write("\\x1b[33m╠══════════════════════════════════════════════════════════════════════════════╣\\x1b[0m\\r\\n");
        this.terminal.write("\\x1b[33m║\\x1b[0m                                                                              \\x1b[33m║\\x1b[0m\\r\\n");

        if (isSecure) {
          this.terminal.write("\\x1b[33m║\\x1b[0m \\x1b[31m⚠️  HTTPS site requires secure WebSocket (WSS) connection\\x1b[0m                \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m                                                                              \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m \\x1b[32mFor HTTPS sites, you need SSL-enabled proxy:\\x1b[0m                            \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m                                                                              \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m \\x1b[36m1. Generate SSL Certificate\\x1b[0m                                             \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m    \\x1b[90mopenssl req -x509 -newkey rsa:4096 -keyout key.pem \\\\\\x1b[0m                   \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m    \\x1b[90m  -out cert.pem -days 365 -nodes\\x1b[0m                                       \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m                                                                              \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m \\x1b[36m2. Run Secure Proxy\\x1b[0m                                                     \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m    \\x1b[90mwebsockify --cert=cert.pem --key=key.pem 8443 mapscii.me:23\\x1b[0m           \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m                                                                              \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m \\x1b[36m3. Accept Self-Signed Certificate\\x1b[0m                                       \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m    Visit https://localhost:8443 and accept the certificate                 \\x1b[33m║\\x1b[0m\\r\\n");
        } else {
          this.terminal.write("\\x1b[33m║\\x1b[0m \\x1b[31mDirect browser connection to mapscii.me is not possible\\x1b[0m                   \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m \\x1b[31mbecause browsers cannot make raw Telnet connections.\\x1b[0m                     \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m                                                                              \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m \\x1b[32mTo connect to the real mapscii.me server:\\x1b[0m                                \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m                                                                              \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m \\x1b[36m1. Install WebSocket-to-Telnet Proxy\\x1b[0m                                    \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m    \\x1b[90mnpm install -g websockify\\x1b[0m                                             \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m                                                                              \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m \\x1b[36m2. Run Proxy Server\\x1b[0m                                                     \\x1b[33m║\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[33m║\\x1b[0m    \\x1b[90mwebsockify 8080 mapscii.me:23\\x1b[0m                                        \\x1b[33m║\\x1b[0m\\r\\n");
        }

        this.terminal.write("\\x1b[33m║\\x1b[0m                                                                              \\x1b[33m║\\x1b[0m\\r\\n");
        this.terminal.write("\\x1b[33m║\\x1b[0m \\x1b[36mAlternative: Use Terminal Directly\\x1b[0m                                      \\x1b[33m║\\x1b[0m\\r\\n");
        this.terminal.write("\\x1b[33m║\\x1b[0m    \\x1b[90mtelnet mapscii.me\\x1b[0m                                                     \\x1b[33m║\\x1b[0m\\r\\n");
        this.terminal.write("\\x1b[33m║\\x1b[0m    \\x1b[90mssh mapscii.me\\x1b[0m                                                        \\x1b[33m║\\x1b[0m\\r\\n");
        this.terminal.write("\\x1b[33m║\\x1b[0m                                                                              \\x1b[33m║\\x1b[0m\\r\\n");
        this.terminal.write("\\x1b[33m╚══════════════════════════════════════════════════════════════════════════════╝\\x1b[0m\\r\\n");
        this.terminal.write("\\r\\n");
        this.terminal.write(`\\x1b[1;37mRequired: ${wsProtocol}//localhost:${requiredPort}\\x1b[0m\\r\\n`);
        this.terminal.write("\\x1b[90mThe xterm.js terminal will display real mapscii ASCII maps once connected.\\x1b[0m\\r\\n");
      };

      window.MapsciiTerminal.prototype.setCenter = function(center) {
        this.center = center;
        // Send navigation command to mapscii if connected
        if (this.isConnected && this.socket) {
          // Send \'g\' command to go to coordinates
          this.socket.send(\'g\');
          setTimeout(() => {
            this.socket.send(center[1] + \',\' + center[0] + \'\\r\');
          }, 100);
        }
      };

      window.MapsciiTerminal.prototype.zoomIn = function() {
        if (this.isConnected && this.socket) {
          this.socket.send(\'+\');
        }
      };

      window.MapsciiTerminal.prototype.zoomOut = function() {
        if (this.isConnected && this.socket) {
          this.socket.send(\'-\');
        }
      };

      window.MapsciiTerminal.prototype.resize = function() {
        if (this.fitAddon) {
          this.fitAddon.fit();
        }
      };

      window.MapsciiTerminal.prototype.addReconnectHandler = function() {
        const self = this;

        // Add keyboard listener for reconnect
        const reconnectHandler = (e) => {
          if (e.key.toLowerCase() === "r") {
            self.terminal.write("\\r\\n\\x1b[32mReconnecting to MapSCII...\\x1b[0m\\r\\n");
            self.connectToMapscii();

            // Remove this handler after use
            document.removeEventListener("keydown", reconnectHandler);
          }
        };

        document.addEventListener("keydown", reconnectHandler);

        // Auto-remove handler after 30 seconds
        setTimeout(() => {
          document.removeEventListener("keydown", reconnectHandler);
        }, 30000);
      };

      window.MapsciiTerminal.prototype.startInteractiveSession = function() {
        if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
          return;
        }

        console.log("Starting interactive MapSCII session...");

        // Send multiple rapid commands to ensure MapSCII stays active
        this.socket.send("\\r"); // Enter to dismiss welcome screen

        setTimeout(() => {
          if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send("\\r"); // Another enter
          }
        }, 200);

        setTimeout(() => {
          if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(" "); // Space to ensure we\'re in interactive mode
          }
        }, 400);

        // Very aggressive keep-alive to prevent timeout
        this.keepAliveInterval = setInterval(() => {
          if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            // Send tiny movements to keep connection alive
            this.socket.send("w"); // Move north
            setTimeout(() => {
              if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                this.socket.send("s"); // Move back south
              }
            }, 100);
          } else {
            clearInterval(this.keepAliveInterval);
          }
        }, 5000); // Every 5 seconds - more aggressive

        // Enable keyboard navigation
        this.enableKeyboardNavigation();

        // Show interactive instructions
        setTimeout(() => {
          this.terminal.write("\\r\\n\\x1b[36m━━━ Interactive MapSCII Ready ━━━\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[32m• WASD or Arrow Keys: Navigate\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[32m• +/- or PgUp/PgDn: Zoom\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[32m• a: Toggle ASCII art mode\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[32m• q: Quit (close modal)\\x1b[0m\\r\\n");
          this.terminal.write("\\x1b[36m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\\x1b[0m\\r\\n");
        }, 2000);
      };

      window.MapsciiTerminal.prototype.enableKeyboardNavigation = function() {
        const self = this;

        // Remove any existing keyboard handler
        if (this.keyboardHandler) {
          document.removeEventListener("keydown", this.keyboardHandler);
        }

        this.keyboardHandler = (e) => {
          if (!self.socket || self.socket.readyState !== WebSocket.OPEN) {
            return;
          }

          let command = null;

          // Handle navigation keys
          switch(e.key) {
            case "w":
            case "W":
            case "ArrowUp":
              command = "w";
              e.preventDefault();
              break;
            case "s":
            case "S":
            case "ArrowDown":
              command = "s";
              e.preventDefault();
              break;
            case "a":
            case "A":
            case "ArrowLeft":
              command = "a";
              e.preventDefault();
              break;
            case "d":
            case "D":
            case "ArrowRight":
              command = "d";
              e.preventDefault();
              break;
            case "+":
            case "=":
            case "PageUp":
              command = "+";
              e.preventDefault();
              break;
            case "-":
            case "_":
            case "PageDown":
              command = "-";
              e.preventDefault();
              break;
            case "q":
            case "Q":
            case "Escape":
              // Close modal
              const modal = document.querySelector(".baum-modal");
              if (modal) {
                modal.style.display = "none";
              }
              e.preventDefault();
              return;
            default:
              // Forward other keys directly to MapSCII
              if (e.key.length === 1) {
                command = e.key;
              }
              break;
          }

          if (command) {
            console.log(`Sending navigation command: ${command}`);
            self.socket.send(command);
          }
        };

        document.addEventListener("keydown", this.keyboardHandler);

        // Clean up on disconnect
        if (this.socket) {
          this.socket.addEventListener("close", () => {
            if (self.keyboardHandler) {
              document.removeEventListener("keydown", self.keyboardHandler);
            }
            if (self.keepAliveInterval) {
              clearInterval(self.keepAliveInterval);
            }
          });
        }
      };







      // Set up the Mapscii class to use xterm.js terminal
      window.Mapscii = window.MapsciiTerminal;
      window.mapsciiReady = true;

      console.log("Mapscii xterm.js terminal loaded for " + "' . $context . '");
    ');


  }

  /**
   * Load mapscii styles (shared between frontend and admin)
   */
  private function load_mapscii_styles($context = 'frontend') {
    // Create unique style handle for admin vs frontend
    $style_handle = 'baum-mapscii-styles-' . $context;

    // Check if already enqueued to prevent double loading
    if (wp_style_is($style_handle, 'enqueued')) {
      return;
    }

    // Load xterm.js CSS
    wp_enqueue_style(
      'xterm-css',
      'https://cdn.jsdelivr.net/npm/xterm@5.3.0/css/xterm.css',
      array(),
      '5.3.0'
    );

    // Enqueue mapscii styles with unique handle
    wp_enqueue_style(
      $style_handle,
      BAUM_TERMINAL_PLUGIN_URL . 'assets/mapscii-integration.css',
      array('xterm-css'),
      '1.0.0'
    );
  }
  
  /**
   * Add mapscii modal to footer
   */
  public function add_mapscii_modal() {
    ?>
    <div id="baum-mapscii-modal" class="baum-mapscii-modal" style="display: none;">
      <div class="baum-mapscii-modal-content">
        <div class="baum-mapscii-header">
          <h3>Interactive Map - Select Location</h3>
          <button class="baum-mapscii-close">&times;</button>
        </div>
        
        <div class="baum-mapscii-controls">
          <div class="baum-mapscii-search">
            <input type="text" id="baum-mapscii-search" placeholder="Search for a location...">
            <button id="baum-mapscii-search-btn">Search</button>
          </div>
          
          <div class="baum-mapscii-coordinates">
            <span>Lat: <span id="baum-mapscii-lat">0</span></span>
            <span>Lng: <span id="baum-mapscii-lng">0</span></span>
          </div>
          
          <div class="baum-mapscii-actions">
            <button id="baum-mapscii-save" class="baum-btn-primary">Save Location</button>
            <button id="baum-mapscii-cancel" class="baum-btn-secondary">Cancel</button>
          </div>
        </div>
        
        <div id="baum-mapscii-container" class="baum-mapscii-container">
          <!-- Mapscii will be rendered here -->
        </div>
        
        <div class="baum-mapscii-instructions">
          <p><strong>Controls:</strong></p>
          <ul>
            <li>Arrow keys or WASD - Move around</li>
            <li>+/- or mouse wheel - Zoom in/out</li>
            <li>Click - Select location</li>
            <li>ESC - Close map</li>
          </ul>
        </div>
      </div>
    </div>
    <?php
  }
  
  /**
   * Get place coordinates via AJAX
   */
  public function get_place_coordinates() {
    check_ajax_referer('baum_mapscii_nonce', 'nonce');
    
    $place_id = intval($_POST['place_id']);
    
    if (!$place_id) {
      wp_send_json_error('Invalid place ID');
    }
    
    // Get coordinates from ACF fields
    $latitude = get_field('latitude', $place_id);
    $longitude = get_field('longitude', $place_id);
    $address = get_field('address', $place_id);
    $city = get_field('city', $place_id);
    $country = get_field('country', $place_id);
    
    wp_send_json_success(array(
      'latitude' => $latitude ?: 0,
      'longitude' => $longitude ?: 0,
      'address' => $address ?: '',
      'city' => $city ?: '',
      'country' => $country ?: '',
      'display_name' => get_the_title($place_id)
    ));
  }
  
  /**
   * Save place coordinates via AJAX
   */
  public function save_place_coordinates() {
    check_ajax_referer('baum_mapscii_nonce', 'nonce');
    
    $place_id = intval($_POST['place_id']);
    $latitude = floatval($_POST['latitude']);
    $longitude = floatval($_POST['longitude']);
    $address = sanitize_text_field($_POST['address']);
    
    if (!$place_id) {
      wp_send_json_error('Invalid place ID');
    }
    
    // Save to ACF fields
    update_field('latitude', $latitude, $place_id);
    update_field('longitude', $longitude, $place_id);
    
    if ($address) {
      update_field('address', $address, $place_id);
    }
    
    wp_send_json_success(array(
      'message' => 'Location saved successfully',
      'latitude' => $latitude,
      'longitude' => $longitude
    ));
  }
  
  /**
   * Add mapscii button to place edit screens
   */
  public function add_mapscii_button_to_place_edit() {
    global $post;
    
    if ($post && $post->post_type === 'place') {
      ?>
      <div class="baum-mapscii-edit-controls">
        <button type="button" id="baum-open-mapscii" class="button button-primary" data-place-id="<?php echo $post->ID; ?>">
          📍 Select Location with Interactive Map
        </button>
        <p class="description">Click to open an interactive map where you can select the exact location</p>
      </div>
      
      <script>
      jQuery(document).ready(function($) {
        $('#baum-open-mapscii').on('click', function() {
          const placeId = $(this).data('place-id');
          if (window.BaumMapscii) {
            window.BaumMapscii.openForPlace(placeId);
          }
        });
      });
      </script>
      <?php
    }
  }
  
  /**
   * Add mapscii view to place single pages
   */
  public function add_mapscii_view_to_place_single() {
    global $post;
    
    if ($post && $post->post_type === 'place') {
      $latitude = get_field('latitude', $post->ID);
      $longitude = get_field('longitude', $post->ID);
      
      if ($latitude && $longitude) {
        ?>
        <div class="baum-place-map-container">
          <h3>Location Map</h3>
          <button type="button" class="baum-view-mapscii button" data-lat="<?php echo $latitude; ?>" data-lng="<?php echo $longitude; ?>" data-place-name="<?php echo esc_attr(get_the_title()); ?>">
            🗺️ View Interactive Map
          </button>
          
          <div class="baum-place-coordinates">
            <p><strong>Coordinates:</strong> <?php echo $latitude; ?>, <?php echo $longitude; ?></p>
          </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
          $('.baum-view-mapscii').on('click', function() {
            const lat = parseFloat($(this).data('lat'));
            const lng = parseFloat($(this).data('lng'));
            const placeName = $(this).data('place-name');
            
            if (window.BaumMapscii) {
              window.BaumMapscii.openViewOnly(lat, lng, placeName);
            }
          });
        });
        </script>
        <?php
      } else {
        ?>
        <div class="baum-place-map-container">
          <p class="baum-no-location">No location coordinates set for this place.</p>
          <?php if (current_user_can('edit_post', $post->ID)): ?>
            <button type="button" class="baum-add-mapscii button" data-place-id="<?php echo $post->ID; ?>">
              📍 Add Location with Interactive Map
            </button>
          <?php endif; ?>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
          $('.baum-add-mapscii').on('click', function() {
            const placeId = $(this).data('place-id');
            if (window.BaumMapscii) {
              window.BaumMapscii.openForPlace(placeId);
            }
          });
        });
        </script>
        <?php
      }
    }
  }
  
  /**
   * Register ACF fields for places if they don't exist
   */
  public function register_place_acf_fields() {
    if (function_exists('acf_add_local_field_group')) {
      acf_add_local_field_group(array(
        'key' => 'group_baum_place_location',
        'title' => 'Location Details',
        'fields' => array(
          array(
            'key' => 'field_baum_latitude',
            'label' => 'Latitude',
            'name' => 'latitude',
            'type' => 'number',
            'step' => 0.000001,
            'placeholder' => '40.7128'
          ),
          array(
            'key' => 'field_baum_longitude',
            'label' => 'Longitude', 
            'name' => 'longitude',
            'type' => 'number',
            'step' => 0.000001,
            'placeholder' => '-74.0060'
          ),
          array(
            'key' => 'field_baum_address',
            'label' => 'Address',
            'name' => 'address',
            'type' => 'text',
            'placeholder' => '123 Main St'
          ),
          array(
            'key' => 'field_baum_city',
            'label' => 'City',
            'name' => 'city', 
            'type' => 'text',
            'placeholder' => 'New York'
          ),
          array(
            'key' => 'field_baum_country',
            'label' => 'Country',
            'name' => 'country',
            'type' => 'text',
            'placeholder' => 'United States'
          )
        ),
        'location' => array(
          array(
            array(
              'param' => 'post_type',
              'operator' => '==',
              'value' => 'place'
            )
          )
        )
      ));
    }
  }
  
  /**
   * Initialize hooks
   */
  public function init_hooks() {
    add_action('acf/init', array($this, 'register_place_acf_fields'));
    add_action('edit_form_after_title', array($this, 'add_mapscii_button_to_place_edit'));
    add_action('the_content', array($this, 'maybe_add_mapscii_to_content'));
  }
  
  /**
   * Maybe add mapscii to content
   */
  public function maybe_add_mapscii_to_content($content) {
    if (is_singular('place')) {
      ob_start();
      $this->add_mapscii_view_to_place_single();
      $mapscii_content = ob_get_clean();
      
      return $content . $mapscii_content;
    }
    
    return $content;
  }
}

// Initialize the integration
$baum_mapscii_integration = new BaumTerminalMapsciiIntegration();
$baum_mapscii_integration->init_hooks();
