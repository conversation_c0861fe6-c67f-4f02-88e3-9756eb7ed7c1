<?php
/**
 * Claude API Integration for Real-Time ASCII Map Generation
 */

class BaumTerminalClaudeMapAPI {
  
  private $api_key;
  private $api_url = 'https://api.anthropic.com/v1/messages';
  
  public function __construct() {
    // Get API key from environment or WordPress options
    $this->api_key = defined('CLAUDE_API_KEY') ? CLAUDE_API_KEY : get_option('baum_claude_api_key');
  }
  
  /**
   * Generate ASCII map using Claude API
   */
  public function generate_ascii_map($location, $style = 'detailed') {
    if (!$this->api_key) {
      return $this->get_api_key_error();
    }
    
    $prompt = $this->build_map_prompt($location, $style);
    $response = $this->call_claude_api($prompt);
    
    if ($response && isset($response['content'])) {
      return $this->format_map_response($response['content'], $location);
    }
    
    return $this->get_api_error();
  }
  
  /**
   * Build prompt for <PERSON> to generate ASCII map
   */
  private function build_map_prompt($location, $style) {
    return "Please create a detailed ASCII map of {$location}. 

Requirements:
- Use only standard ASCII characters (no Unicode)
- Maximum width of 80 characters
- Maximum height of 25 lines
- Include major landmarks, streets, or geographic features
- Use # for land/buildings, ~ for water, . for open space
- Label important locations clearly
- Make it geographically accurate
- Style: {$style}

Create a terminal-friendly ASCII representation that would be useful for navigation and geographic understanding.

Location: {$location}";
  }
  
  /**
   * Call Claude API
   */
  private function call_claude_api($prompt) {
    if (!function_exists('curl_init')) {
      return false;
    }
    
    $data = array(
      'model' => 'claude-3-sonnet-20240229',
      'max_tokens' => 2000,
      'messages' => array(
        array(
          'role' => 'user',
          'content' => $prompt
        )
      )
    );
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $this->api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      'Content-Type: application/json',
      'x-api-key: ' . $this->api_key,
      'anthropic-version: 2023-06-01'
    ));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200 && $response) {
      return json_decode($response, true);
    }
    
    return false;
  }
  
  /**
   * Format Claude's response into terminal-ready map
   */
  private function format_map_response($content, $location) {
    // Extract ASCII map from Claude's response
    $map_content = '';
    
    if (is_array($content) && isset($content[0]['text'])) {
      $map_content = $content[0]['text'];
    } elseif (is_string($content)) {
      $map_content = $content;
    }
    
    // Add header and formatting
    $formatted_map = "
[CYAN]CLAUDE-GENERATED ASCII MAP: " . strtoupper($location) . "[/CYAN]
[CYAN]" . str_repeat("=", 60) . "[/CYAN]

" . $map_content . "

[GREEN]Generated by Claude AI[/GREEN]
[GRAY]Real-time ASCII map based on geographic data[/GRAY]
";
    
    return $formatted_map;
  }
  
  /**
   * Get API key error message
   */
  private function get_api_key_error() {
    return "
[RED]CLAUDE API KEY REQUIRED[/RED]

To generate real-time ASCII maps with Claude:

1. Get Claude API key from: https://console.anthropic.com/
2. Add to wp-config.php: define('CLAUDE_API_KEY', 'your-key-here');
3. Or set in WordPress admin: Settings > Baum Terminal

[YELLOW]Alternative commands:[/YELLOW]
• map real world  - Use cached detailed maps
• map server usa  - Use server-side tools
• map world       - Use simple built-in maps

[BLUE]Benefits of Claude maps:[/BLUE]
• Real-time generation for any location
• Geographically accurate layouts
• Landmark identification
• Street-level detail
";
  }
  
  /**
   * Get API error message
   */
  private function get_api_error() {
    return "
[RED]CLAUDE API ERROR[/RED]

Failed to generate ASCII map from Claude API.

Possible issues:
• API key invalid or expired
• Rate limit exceeded
• Network connectivity
• Service temporarily unavailable

[YELLOW]Try these alternatives:[/YELLOW]
• map real world  - Cached detailed maps
• map world       - Simple built-in maps
• Check API key configuration

[GRAY]Retry the command in a few moments[/GRAY]
";
  }
  
  /**
   * Generate multiple map styles
   */
  public function generate_map_styles($location) {
    $styles = array(
      'overview' => 'broad geographic overview',
      'detailed' => 'detailed street-level view',
      'landmarks' => 'focus on major landmarks',
      'transport' => 'transportation and routes'
    );
    
    $maps = array();
    foreach ($styles as $style => $description) {
      $maps[$style] = $this->generate_ascii_map($location, $style);
    }
    
    return $maps;
  }
  
  /**
   * Get location suggestions
   */
  public function get_location_suggestions() {
    return array(
      'Cities' => array('london', 'paris', 'tokyo', 'new york', 'san francisco'),
      'Countries' => array('france', 'japan', 'italy', 'australia', 'canada'),
      'Landmarks' => array('grand canyon', 'mount fuji', 'eiffel tower', 'big ben'),
      'Regions' => array('silicon valley', 'manhattan', 'downtown', 'old town')
    );
  }
  
  /**
   * Check if API is configured
   */
  public function is_configured() {
    return !empty($this->api_key);
  }
  
  /**
   * Test API connection
   */
  public function test_connection() {
    if (!$this->api_key) {
      return false;
    }
    
    $test_response = $this->call_claude_api("Say 'API connection successful' in exactly those words.");
    
    return $test_response && 
           isset($test_response['content']) && 
           strpos(json_encode($test_response['content']), 'API connection successful') !== false;
  }
}
