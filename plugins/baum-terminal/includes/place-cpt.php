<?php
/**
 * Place Custom Post Type
 * For locations that can be viewed with mapscii
 */

class BaumTerminalPlaceCPT {
  
  /**
   * Initialize Place CPT
   */
  public function __construct() {
    add_action('init', array($this, 'register_place_cpt'));
    add_action('add_meta_boxes', array($this, 'add_place_meta_boxes'));
    add_filter('single_template', array($this, 'load_place_template'));
  }
  
  /**
   * Register Place Custom Post Type
   */
  public function register_place_cpt() {
    $labels = array(
      'name' => 'Places',
      'singular_name' => 'Place',
      'menu_name' => 'Places',
      'add_new' => 'Add New Place',
      'add_new_item' => 'Add New Place',
      'edit_item' => 'Edit Place',
      'new_item' => 'New Place',
      'view_item' => 'View Place',
      'search_items' => 'Search Places',
      'not_found' => 'No places found',
      'not_found_in_trash' => 'No places found in trash'
    );
    
    $args = array(
      'labels' => $labels,
      'public' => true,
      'publicly_queryable' => true,
      'show_ui' => true,
      'show_in_menu' => true,
      'query_var' => true,
      'rewrite' => array('slug' => 'places'),
      'capability_type' => 'post',
      'has_archive' => true,
      'hierarchical' => false,
      'menu_position' => 20,
      'menu_icon' => 'dashicons-location-alt',
      'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
      'show_in_rest' => true
    );
    
    register_post_type('place', $args);
  }
  
  /**
   * Add meta boxes for places
   */
  public function add_place_meta_boxes() {
    add_meta_box(
      'baum-place-mapscii',
      '📍 Interactive Map Location',
      array($this, 'render_mapscii_meta_box'),
      'place',
      'normal',
      'high'
    );
  }
  
  /**
   * Render mapscii meta box
   */
  public function render_mapscii_meta_box($post) {
    $latitude = get_post_meta($post->ID, '_place_latitude', true);
    $longitude = get_post_meta($post->ID, '_place_longitude', true);
    $address = get_post_meta($post->ID, '_place_address', true);
    
    ?>
    <div class="baum-place-mapscii-meta">
      <p>Use the interactive map to select the exact location for this place.</p>
      
      <div class="baum-mapscii-current-location">
        <?php if ($latitude && $longitude): ?>
          <p><strong>Current Location:</strong></p>
          <p>📍 Latitude: <code><?php echo esc_html($latitude); ?></code></p>
          <p>📍 Longitude: <code><?php echo esc_html($longitude); ?></code></p>
          <?php if ($address): ?>
            <p>📍 Address: <code><?php echo esc_html($address); ?></code></p>
          <?php endif; ?>
        <?php else: ?>
          <p><em>No location set yet.</em></p>
        <?php endif; ?>
      </div>
      
      <div class="baum-mapscii-actions">
        <button type="button" id="baum-open-mapscii-meta" class="button button-primary button-large" data-place-id="<?php echo $post->ID; ?>">
          🗺️ Open Interactive Map
        </button>
        
        <?php if ($latitude && $longitude): ?>
          <button type="button" id="baum-view-mapscii-meta" class="button button-secondary" data-lat="<?php echo $latitude; ?>" data-lng="<?php echo $longitude; ?>" data-place-name="<?php echo esc_attr(get_the_title($post)); ?>">
            👁️ View Current Location
          </button>
        <?php endif; ?>
      </div>
      
      <div class="baum-mapscii-manual-fields" style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd;">
        <h4>Manual Coordinates (Optional)</h4>
        <p>You can also manually enter coordinates if needed:</p>
        
        <table class="form-table">
          <tr>
            <th><label for="place_latitude">Latitude</label></th>
            <td>
              <input type="number" id="place_latitude" name="place_latitude" value="<?php echo esc_attr($latitude); ?>" step="0.000001" placeholder="40.7128" class="regular-text">
              <p class="description">Decimal degrees (e.g., 40.7128)</p>
            </td>
          </tr>
          <tr>
            <th><label for="place_longitude">Longitude</label></th>
            <td>
              <input type="number" id="place_longitude" name="place_longitude" value="<?php echo esc_attr($longitude); ?>" step="0.000001" placeholder="-74.0060" class="regular-text">
              <p class="description">Decimal degrees (e.g., -74.0060)</p>
            </td>
          </tr>
          <tr>
            <th><label for="place_address">Address</label></th>
            <td>
              <input type="text" id="place_address" name="place_address" value="<?php echo esc_attr($address); ?>" placeholder="123 Main St, City, Country" class="regular-text">
              <p class="description">Full address for reference</p>
            </td>
          </tr>
        </table>
      </div>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
      $('#baum-open-mapscii-meta').on('click', function() {
        const placeId = $(this).data('place-id');
        if (window.BaumMapscii) {
          window.BaumMapscii.openForPlace(placeId);
        } else {
          alert('Mapscii integration not loaded. Please refresh the page.');
        }
      });
      
      $('#baum-view-mapscii-meta').on('click', function() {
        const lat = parseFloat($(this).data('lat'));
        const lng = parseFloat($(this).data('lng'));
        const placeName = $(this).data('place-name');
        
        if (window.BaumMapscii) {
          window.BaumMapscii.openViewOnly(lat, lng, placeName);
        } else {
          alert('Mapscii integration not loaded. Please refresh the page.');
        }
      });
    });
    </script>
    
    <style>
    .baum-place-mapscii-meta {
      padding: 15px;
    }
    
    .baum-mapscii-current-location {
      background: #f9f9f9;
      padding: 15px;
      border-radius: 4px;
      margin: 15px 0;
    }
    
    .baum-mapscii-current-location code {
      background: #fff;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
    }
    
    .baum-mapscii-actions {
      margin: 15px 0;
    }
    
    .baum-mapscii-actions .button {
      margin-right: 10px;
    }
    
    .baum-mapscii-manual-fields h4 {
      margin-bottom: 10px;
      color: #333;
    }
    </style>
    <?php
  }
  
  /**
   * Save place meta data
   */
  public function save_place_meta($post_id) {
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
      return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
      return;
    }
    
    // Save manual coordinates if provided
    if (isset($_POST['place_latitude'])) {
      update_post_meta($post_id, '_place_latitude', sanitize_text_field($_POST['place_latitude']));
    }
    
    if (isset($_POST['place_longitude'])) {
      update_post_meta($post_id, '_place_longitude', sanitize_text_field($_POST['place_longitude']));
    }
    
    if (isset($_POST['place_address'])) {
      update_post_meta($post_id, '_place_address', sanitize_text_field($_POST['place_address']));
    }
  }
  
  /**
   * Load custom template for places
   */
  public function load_place_template($template) {
    global $post;
    
    if ($post->post_type === 'place') {
      $place_template = BAUM_TERMINAL_PLUGIN_DIR . 'templates/single-place.php';
      
      if (file_exists($place_template)) {
        return $place_template;
      }
    }
    
    return $template;
  }
  
  /**
   * Initialize hooks
   */
  public function init_hooks() {
    add_action('save_post', array($this, 'save_place_meta'));
  }
}

// Initialize Place CPT
$baum_place_cpt = new BaumTerminalPlaceCPT();
$baum_place_cpt->init_hooks();
