<?php
/**
 * ASCII Animation Engine for Baum Terminal
 * Handles interactive and animated ASCII content
 */

class BaumTerminalAnimationEngine {
  
  /**
   * Get Matrix rain animation
   */
  public function get_matrix_animation() {
    return array(
      'type' => 'animation',
      'name' => 'matrix',
      'duration' => 30000, // 30 seconds
      'frames' => $this->generate_matrix_frames(),
      'controls' => 'Press ESC to stop'
    );
  }
  
  /**
   * Get ASCII clock
   */
  public function get_clock_animation() {
    return array(
      'type' => 'animation',
      'name' => 'clock',
      'duration' => -1, // Infinite
      'frames' => 'realtime', // Generated in real-time
      'controls' => 'Press ESC to stop'
    );
  }
  
  /**
   * Get weather animation
   */
  public function get_weather_animation($type = 'rain') {
    return array(
      'type' => 'animation',
      'name' => 'weather_' . $type,
      'duration' => 60000, // 60 seconds
      'frames' => $this->generate_weather_frames($type),
      'controls' => 'Press ESC to stop'
    );
  }
  
  /**
   * Get ASCII aquarium
   */
  public function get_aquarium_animation() {
    return array(
      'type' => 'animation',
      'name' => 'aquarium',
      'duration' => -1, // Infinite
      'frames' => $this->generate_aquarium_frames(),
      'controls' => 'Press ESC to stop'
    );
  }
  
  /**
   * Get fire animation
   */
  public function get_fire_animation() {
    return array(
      'type' => 'animation',
      'name' => 'fire',
      'duration' => 45000, // 45 seconds
      'frames' => $this->generate_fire_frames(),
      'controls' => 'Press ESC to stop'
    );
  }
  
  /**
   * Get starfield animation
   */
  public function get_starfield_animation() {
    return array(
      'type' => 'animation',
      'name' => 'starfield',
      'duration' => 60000, // 60 seconds
      'frames' => $this->generate_starfield_frames(),
      'controls' => 'Press ESC to stop'
    );
  }
  
  /**
   * Get Snake game
   */
  public function get_snake_game() {
    return array(
      'type' => 'interactive',
      'name' => 'snake',
      'controls' => 'Arrow keys to move, ESC to quit',
      'instructions' => $this->get_snake_instructions()
    );
  }
  
  /**
   * Get interactive ASCII art gallery
   */
  public function get_art_gallery() {
    return array(
      'type' => 'interactive',
      'name' => 'gallery',
      'controls' => 'Left/Right arrows to navigate, ESC to quit',
      'art_pieces' => $this->get_art_collection()
    );
  }
  
  /**
   * Generate Matrix rain frames
   */
  private function generate_matrix_frames() {
    $frames = array();
    $chars = array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F');

    // Generate 30 frames for smooth animation (reduced for performance)
    for ($frame = 0; $frame < 30; $frame++) {
      $output = "";

      // Full screen: 25 rows, 80 columns
      for ($row = 0; $row < 25; $row++) {
        for ($col = 0; $col < 80; $col++) {
          if (rand(1, 100) < 12) { // 12% chance of character
            $char = $chars[array_rand($chars)];
            $output .= "[GREEN]" . $char . "[/GREEN]";
          } else {
            $output .= " ";
          }
        }
        $output .= "\n";
      }

      $frames[] = $output;
    }

    return $frames;
  }
  
  /**
   * Generate weather animation frames
   */
  private function generate_weather_frames($type) {
    $frames = array();
    
    switch ($type) {
      case 'rain':
        return $this->generate_rain_frames();
      case 'snow':
        return $this->generate_snow_frames();
      default:
        return $this->generate_rain_frames();
    }
  }
  
  /**
   * Generate rain animation
   */
  private function generate_rain_frames() {
    $frames = array();

    for ($frame = 0; $frame < 20; $frame++) {
      $output = "";

      // Full screen rain
      for ($row = 0; $row < 25; $row++) {
        for ($col = 0; $col < 80; $col++) {
          if (rand(1, 100) < 6) { // 6% chance of raindrop
            $output .= "[BLUE]|[/BLUE]";
          } else {
            $output .= " ";
          }
        }
        $output .= "\n";
      }

      $frames[] = $output;
    }

    return $frames;
  }
  
  /**
   * Generate snow animation
   */
  private function generate_snow_frames() {
    $frames = array();
    $snow_chars = array('*', '❄', '•', '○');
    
    for ($frame = 0; $frame < 40; $frame++) {
      $output = "";
      
      for ($row = 0; $row < 20; $row++) {
        for ($col = 0; $col < 80; $col++) {
          if (rand(1, 100) < 5) { // 5% chance of snowflake
            $char = $snow_chars[array_rand($snow_chars)];
            $output .= "[WHITE]" . $char . "[/WHITE]";
          } else {
            $output .= " ";
          }
        }
        $output .= "\n";
      }
      
      $frames[] = $output;
    }
    
    return $frames;
  }
  
  /**
   * Generate aquarium frames
   */
  private function generate_aquarium_frames() {
    $frames = array();
    
    for ($frame = 0; $frame < 30; $frame++) {
      $output = $this->create_aquarium_scene($frame);
      $frames[] = $output;
    }
    
    return $frames;
  }
  
  /**
   * Create aquarium scene
   */
  private function create_aquarium_scene($frame) {
    $scene = "";
    
    // Top border
    $scene .= "[CYAN]" . str_repeat("═", 78) . "[/CYAN]\n";
    
    // Water with fish
    for ($row = 1; $row < 19; $row++) {
      $scene .= "[CYAN]║[/CYAN]";
      
      for ($col = 1; $col < 77; $col++) {
        // Add fish at different positions based on frame
        if ($this->should_place_fish($row, $col, $frame)) {
          $scene .= "[YELLOW]><>[/YELLOW]";
          $col += 2; // Skip next 2 positions
        } elseif (rand(1, 100) < 3) { // Bubbles
          $scene .= "[WHITE]○[/WHITE]";
        } else {
          $scene .= " ";
        }
      }
      
      $scene .= "[CYAN]║[/CYAN]\n";
    }
    
    // Bottom border
    $scene .= "[CYAN]" . str_repeat("═", 78) . "[/CYAN]\n";
    
    return $scene;
  }
  
  /**
   * Determine if fish should be placed at position
   */
  private function should_place_fish($row, $col, $frame) {
    // Simple fish movement logic
    $fish_positions = array(
      array('row' => 5, 'start_col' => 10),
      array('row' => 10, 'start_col' => 30),
      array('row' => 15, 'start_col' => 50)
    );
    
    foreach ($fish_positions as $fish) {
      $current_col = ($fish['start_col'] + $frame * 2) % 74;
      if ($row == $fish['row'] && $col >= $current_col && $col < $current_col + 3) {
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * Generate fire animation frames
   */
  private function generate_fire_frames() {
    $frames = array();
    $fire_chars = array('▲', '▼', '◆', '♦', '*', '^');
    
    for ($frame = 0; $frame < 50; $frame++) {
      $output = "";
      
      // Create fire effect from bottom up
      for ($row = 0; $row < 20; $row++) {
        for ($col = 0; $col < 80; $col++) {
          $intensity = 20 - $row; // Higher intensity at bottom
          
          if (rand(1, 100) < $intensity) {
            $char = $fire_chars[array_rand($fire_chars)];
            if ($row < 5) {
              $output .= "[RED]" . $char . "[/RED]";
            } elseif ($row < 10) {
              $output .= "[YELLOW]" . $char . "[/YELLOW]";
            } else {
              $output .= "[WHITE]" . $char . "[/WHITE]";
            }
          } else {
            $output .= " ";
          }
        }
        $output .= "\n";
      }
      
      $frames[] = $output;
    }
    
    return $frames;
  }
  
  /**
   * Generate starfield frames
   */
  private function generate_starfield_frames() {
    $frames = array();
    
    for ($frame = 0; $frame < 50; $frame++) {
      $output = "";
      
      for ($row = 0; $row < 20; $row++) {
        for ($col = 0; $col < 80; $col++) {
          if (rand(1, 100) < 3) { // 3% chance of star
            $stars = array('*', '·', '•', '○', '◦');
            $star = $stars[array_rand($stars)];
            $output .= "[WHITE]" . $star . "[/WHITE]";
          } else {
            $output .= " ";
          }
        }
        $output .= "\n";
      }
      
      $frames[] = $output;
    }
    
    return $frames;
  }
  
  /**
   * Get Snake game instructions
   */
  private function get_snake_instructions() {
    return "
[CYAN]================================================================
                         ASCII SNAKE GAME                        
================================================================[/CYAN]

[YELLOW]CONTROLS:[/YELLOW]
• Arrow Keys - Move snake
• ESC - Quit game

[YELLOW]OBJECTIVE:[/YELLOW]
• Eat the food (●) to grow
• Don't hit walls or yourself
• Try to get the highest score!

[GREEN]Press SPACE to start the game...[/GREEN]
";
  }
  
  /**
   * Get ASCII art collection
   */
  private function get_art_collection() {
    return array(
      array(
        'title' => 'ASCII Cat',
        'art' => "
    /\\_/\\  
   ( o.o ) 
    > ^ <  
"
      ),
      array(
        'title' => 'ASCII Rocket',
        'art' => "
       /\\
      /  \\
     |    |
     | ** |
     |    |
     \\    /
      \\  /
       \\/
      ||||
     /||||\\
"
      ),
      array(
        'title' => 'ASCII House',
        'art' => "
      /\\
     /  \\
    /____\\
   |      |
   | []   |
   |   [] |
   |______|
"
      )
    );
  }
  
  /**
   * Get available animations and games
   */
  public function get_available_commands() {
    return array(
      'matrix' => 'Matrix rain animation',
      'clock' => 'Real-time ASCII clock',
      'weather rain' => 'Rain animation',
      'weather snow' => 'Snow animation',
      'aquarium' => 'ASCII aquarium with fish',
      'fire' => 'Flickering fire animation',
      'starfield' => 'Moving starfield',
      'snake' => 'Classic Snake game',
      'gallery' => 'ASCII art gallery'
    );
  }
}
