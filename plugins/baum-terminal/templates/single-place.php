<?php
/**
 * Single Place Template
 * Displays place with interactive mapscii integration
 */

get_header(); ?>

<div class="baum-place-single">
  <?php while (have_posts()) : the_post(); ?>
    
    <article id="post-<?php the_ID(); ?>" <?php post_class('baum-place-article'); ?>>
      
      <header class="baum-place-header">
        <h1 class="baum-place-title"><?php the_title(); ?></h1>
        
        <?php if (has_post_thumbnail()): ?>
          <div class="baum-place-featured-image">
            <?php the_post_thumbnail('large'); ?>
          </div>
        <?php endif; ?>
      </header>
      
      <div class="baum-place-content">
        <?php the_content(); ?>
      </div>
      
      <?php
      // Get place coordinates
      $latitude = get_post_meta(get_the_ID(), '_place_latitude', true);
      $longitude = get_post_meta(get_the_ID(), '_place_longitude', true);
      $address = get_post_meta(get_the_ID(), '_place_address', true);
      ?>
      
      <div class="baum-place-location-section">
        <h2>📍 Location</h2>
        
        <?php if ($latitude && $longitude): ?>
          
          <div class="baum-place-coordinates-display">
            <div class="baum-coordinates-grid">
              <div class="baum-coordinate-item">
                <strong>Latitude:</strong>
                <code><?php echo esc_html($latitude); ?></code>
              </div>
              <div class="baum-coordinate-item">
                <strong>Longitude:</strong>
                <code><?php echo esc_html($longitude); ?></code>
              </div>
              <?php if ($address): ?>
                <div class="baum-coordinate-item baum-address-item">
                  <strong>Address:</strong>
                  <span><?php echo esc_html($address); ?></span>
                </div>
              <?php endif; ?>
            </div>
          </div>
          
          <div class="baum-place-map-actions">
            <button type="button" class="baum-view-mapscii-btn baum-btn-primary" 
                    data-lat="<?php echo esc_attr($latitude); ?>" 
                    data-lng="<?php echo esc_attr($longitude); ?>" 
                    data-place-name="<?php echo esc_attr(get_the_title()); ?>">
              🗺️ View Interactive ASCII Map
            </button>
            
            <button type="button" class="baum-copy-coordinates-btn baum-btn-secondary" 
                    data-lat="<?php echo esc_attr($latitude); ?>" 
                    data-lng="<?php echo esc_attr($longitude); ?>">
              📋 Copy Coordinates
            </button>
            
            <a href="https://www.google.com/maps?q=<?php echo urlencode($latitude . ',' . $longitude); ?>" 
               target="_blank" 
               class="baum-external-map-btn baum-btn-secondary">
              🌍 View on Google Maps
            </a>
          </div>
          
        <?php else: ?>
          
          <div class="baum-no-location-set">
            <p>📍 No location coordinates have been set for this place yet.</p>
            
            <?php if (current_user_can('edit_post', get_the_ID())): ?>
              <button type="button" class="baum-add-location-btn baum-btn-primary" data-place-id="<?php echo get_the_ID(); ?>">
                🗺️ Add Location with Interactive Map
              </button>
              <p class="baum-edit-hint">
                <small>You can set the location using our interactive ASCII map interface.</small>
              </p>
            <?php endif; ?>
          </div>
          
        <?php endif; ?>
      </div>
      
      <?php if (get_the_excerpt()): ?>
        <div class="baum-place-excerpt">
          <h3>About This Place</h3>
          <?php the_excerpt(); ?>
        </div>
      <?php endif; ?>
      
    </article>
    
  <?php endwhile; ?>
</div>

<script>
jQuery(document).ready(function($) {
  // View interactive map
  $('.baum-view-mapscii-btn').on('click', function() {
    const lat = parseFloat($(this).data('lat'));
    const lng = parseFloat($(this).data('lng'));
    const placeName = $(this).data('place-name');
    
    if (window.BaumMapscii) {
      window.BaumMapscii.openViewOnly(lat, lng, placeName);
    } else {
      alert('Interactive map not available. Please refresh the page.');
    }
  });
  
  // Add location for places without coordinates
  $('.baum-add-location-btn').on('click', function() {
    const placeId = $(this).data('place-id');
    
    if (window.BaumMapscii) {
      window.BaumMapscii.openForPlace(placeId);
    } else {
      alert('Interactive map not available. Please refresh the page.');
    }
  });
  
  // Copy coordinates to clipboard
  $('.baum-copy-coordinates-btn').on('click', function() {
    const lat = $(this).data('lat');
    const lng = $(this).data('lng');
    const coordinates = lat + ',' + lng;
    
    // Try to copy to clipboard
    if (navigator.clipboard) {
      navigator.clipboard.writeText(coordinates).then(function() {
        alert('Coordinates copied to clipboard: ' + coordinates);
      }).catch(function() {
        // Fallback
        prompt('Copy these coordinates:', coordinates);
      });
    } else {
      // Fallback for older browsers
      prompt('Copy these coordinates:', coordinates);
    }
  });
});
</script>

<style>
.baum-place-single {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.baum-place-article {
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.baum-place-header {
  padding: 30px 30px 20px;
  border-bottom: 1px solid #eee;
}

.baum-place-title {
  margin: 0 0 20px 0;
  font-size: 2.5em;
  color: #333;
  font-weight: 700;
}

.baum-place-featured-image {
  margin-top: 20px;
}

.baum-place-featured-image img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.baum-place-content {
  padding: 30px;
  font-size: 1.1em;
  line-height: 1.6;
  color: #555;
}

.baum-place-location-section {
  padding: 30px;
  background: #f8f9fa;
  border-top: 1px solid #eee;
}

.baum-place-location-section h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.8em;
}

.baum-place-coordinates-display {
  background: #ffffff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.baum-coordinates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.baum-coordinate-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.baum-coordinate-item strong {
  color: #333;
  font-size: 0.9em;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.baum-coordinate-item code {
  background: #f1f3f4;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  font-size: 1.1em;
  color: #1a73e8;
  border: 1px solid #e8eaed;
}

.baum-address-item {
  grid-column: 1 / -1;
}

.baum-address-item span {
  background: #f1f3f4;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #e8eaed;
  color: #333;
}

.baum-place-map-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.baum-btn-primary {
  background: #1a73e8;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.baum-btn-primary:hover {
  background: #1557b0;
  color: white;
  text-decoration: none;
}

.baum-btn-secondary {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #dadce0;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.baum-btn-secondary:hover {
  background: #e8eaed;
  color: #333;
  text-decoration: none;
}

.baum-no-location-set {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.baum-no-location-set p {
  font-size: 1.1em;
  margin-bottom: 20px;
}

.baum-edit-hint {
  margin-top: 10px;
  color: #888;
}

.baum-place-excerpt {
  padding: 30px;
  border-top: 1px solid #eee;
}

.baum-place-excerpt h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.4em;
}

/* Responsive Design */
@media (max-width: 768px) {
  .baum-place-single {
    padding: 10px;
  }
  
  .baum-place-header,
  .baum-place-content,
  .baum-place-location-section,
  .baum-place-excerpt {
    padding: 20px;
  }
  
  .baum-place-title {
    font-size: 2em;
  }
  
  .baum-coordinates-grid {
    grid-template-columns: 1fr;
  }
  
  .baum-place-map-actions {
    flex-direction: column;
  }
  
  .baum-btn-primary,
  .baum-btn-secondary {
    justify-content: center;
  }
}
</style>

<?php get_footer(); ?>
