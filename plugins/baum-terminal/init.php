<?php
/**
 * Baum Terminal Initialization
 * Include this file in your theme's functions.php to activate the terminal
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Check if the main plugin file exists
$plugin_file = get_template_directory() . '/plugins/baum-terminal/baum-terminal.php';

if (file_exists($plugin_file)) {
  require_once $plugin_file;
  
  // Add admin notice for successful activation
  add_action('admin_notices', function() {
    if (current_user_can('manage_options')) {
      echo '<div class="notice notice-success is-dismissible">';
      echo '<p><strong>Baum Terminal</strong> is now active! Configure it in <a href="' . admin_url('options-general.php?page=baum-terminal') . '">Settings > Baum Terminal</a>.</p>';
      echo '</div>';
    }
  });
} else {
  // Add admin notice for missing plugin
  add_action('admin_notices', function() {
    if (current_user_can('manage_options')) {
      echo '<div class="notice notice-error">';
      echo '<p><strong>Baum Terminal</strong> plugin files not found in theme directory.</p>';
      echo '</div>';
    }
  });
}
