initSolarSystem = function () {}; 

jQuery(document).ready(function ($) {

  initSolarSystem = function drawSolarSystem (date, elementId) {

    const canvas = document.getElementById(elementId);
    const ctx = canvas.getContext('2d');
    canvas.width = 345; // Canvas dimensions
    canvas.height = 300;

    // Target date
    const targetDate = new Date(date);

    // Reference date with known positions
    const referenceDate = new Date("2024-01-01");

    // Calculate days elapsed since reference date
    // const elapsedDays = (targetDate - referenceDate) / (1000 * 60 * 60 * 24);

    const sun = {
      x: canvas.width / 2,
      y: canvas.height / 2,
      radius: 10,
      color: 'yellow',
    };
    console.log('Baum Solar System Simulation: sun', sun);


  // --color-gray: #616161; 
  // --color-black: #0d0d0d; 
  // --color-pink: #e70d5a; 
  // --color-red: #9c0404; 
  // --color-purple: #94049c;
  // --color-blue: #195bc4;
  // --color-green: #13a471;
  // --color-teal: #048f9c;
  // --color-yellow: #f1d72d;
  // --color-orange: #d77f26;

    const planets = [
      {
        name: 'Mercury',
        radius: 3,
        color: 'gray',
        distanceX: 50,
        distanceY: 40,
        orbitPeriod: 88,
        referenceLongitude: 143.8676, // Degrees
        details: {
          orbitalPeriod: '88 days',
          distanceFromSun: '57.9 million km',
          radius: '2,439.7 km',
        },
      },
      {
        name: 'Venus',
        radius: 5,
        color: 'green',
        distanceX: 80,
        distanceY: 70,
        orbitPeriod: 225,
        referenceLongitude: 186.1013, // Degrees
        details: {
          orbitalPeriod: '225 days',
          distanceFromSun: '108.2 million km',
          radius: '6,051.8 km',
        },
      },
      {
        name: 'Earth',
        radius: 6,
        color: 'blue',
        distanceX: 110,
        distanceY: 100,
        orbitPeriod: 365,
        referenceLongitude: 99.6968, // Degrees
        details: {
          orbitalPeriod: '365 days',
          distanceFromSun: '149.6 million km',
          radius: '6,371 km',
        },
      },
      {
        name: 'Mars',
        radius: 4,
        color: 'red',
        distanceX: 150,
        distanceY: 130,
        orbitPeriod: 687,
        referenceLongitude: 258.5639, // Degrees
        details: {
          orbitalPeriod: '687 days',
          distanceFromSun: '227.9 million km',
          radius: '3,389.5 km',
        },
      },
      {
        name: 'Jupiter',
        radius: 12,
        color: 'pink',
        distanceX: 220,
        distanceY: 180,
        orbitPeriod: 4333,
        referenceLongitude: 45.5007, // Degrees
        details: {
          orbitalPeriod: '12 years',
          distanceFromSun: '778.3 million km',
          radius: '69,911 km',
        },
      },
      {
        name: 'Saturn',
        radius: 10,
        color: 'orange',
        distanceX: 300,
        distanceY: 250,
        orbitPeriod: 10759,
        referenceLongitude: 337.5532, // Degrees
        details: {
          orbitalPeriod: '29 years',
          distanceFromSun: '1.43 billion km',
          radius: '58,232 km',
        },
      },
      {
        name: 'Uranus',
        radius: 8,
        color: 'teal',
        distanceX: 370,
        distanceY: 300,
        orbitPeriod: 30687,
        referenceLongitude: 51.2688, // Degrees
        details: {
          orbitalPeriod: '84 years',
          distanceFromSun: '2.87 billion km',
          radius: '25,362 km',
        },
      },
      {
        name: 'Neptune',
        radius: 8,
        color: 'blue',
        distanceX: 430,
        distanceY: 350,
        orbitPeriod: 60190,
        referenceLongitude: 356.5647, // Degrees
        details: {
          orbitalPeriod: '165 years',
          distanceFromSun: '4.5 billion km',
          radius: '24,622 km',
        },
      },
    ];

    // Function to convert degrees to radians
    function degreesToRadians(degrees) {
      return degrees * (Math.PI / 180);
    }

    function calculateCurrentLongitude (planet) {
      // Calculate days elapsed since the reference date
      const elapsedDays = (targetDate - referenceDate) / (1000 * 60 * 60 * 24);
      console.log('Baum Solar System Simulation: elapsedDays', elapsedDays);

      // Calculate degrees moved since the reference date
      const degreesPerDay = 360 / planet.orbitPeriod;
      const degreesMoved = (elapsedDays * degreesPerDay) % 360;

      // Add movement to the reference longitude
      const currentLongitude = (planet.referenceLongitude + degreesMoved) % 360;
      return degreesToRadians(currentLongitude);
    }


    // Function to draw elliptical orbits
    function drawOrbit(centerX, centerY, distanceX, distanceY) {
      ctx.beginPath();
      ctx.ellipse(centerX, centerY, distanceX, distanceY, 0, 0, 2 * Math.PI);
      ctx.strokeStyle = 'darkgray';
      ctx.lineWidth = 0.5;
      ctx.stroke();
    }

    // Function to draw planets and tooltips
    drawPlanets = function drawPlanets() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw the Sun
      ctx.beginPath();
      ctx.arc(sun.x, sun.y, sun.radius, 0, 2 * Math.PI);
      ctx.fillStyle = sun.color;
      ctx.fill();

      planets.forEach((planet, index) => {
        // Calculate dynamic longitude
        const longitudeRadians = calculateCurrentLongitude(planet); 
        
        // Convert heliocentric longitude to Cartesian coordinates
        const x = sun.x - (planet.distanceX / 2.75) * Math.cos(longitudeRadians); // Flip x-axis if necessary
        const y = sun.y + (planet.distanceY / 2.75) * Math.sin(longitudeRadians); // Correct y-axis

        console.log(`${planet.name}: Longitude = ${planet.referenceLongitude}°, X = ${x}, Y = ${y}`); // Debugging

        // Draw orbit
        drawOrbit(sun.x, sun.y, planet.distanceX / 2.75, planet.distanceY / 2.75);

        console.log("getComputedStyle(canvas).getPropertyValue('--color-' + planet.color);", getComputedStyle(canvas).getPropertyValue('--color-' + planet.color)); 

        // Draw planet
        ctx.beginPath();
        ctx.arc(x, y, planet.radius, 0, 2 * Math.PI);
        ctx.fillStyle = getComputedStyle(canvas).getPropertyValue('--color-' + planet.color);
        // ctx.fillStyle = planet.color;
        ctx.fill();

        // Tooltip positioning remains unchanged
        let tooltipMarker = document.querySelector(`.tooltip-planet[data-index="${index}"]`);
        if (!tooltipMarker) {
          tooltipMarker = document.createElement('div');
          tooltipMarker.className = 'tooltip-planet';
          tooltipMarker.dataset.index = index;
          $('#solar-system').append(tooltipMarker);

          const tooltipHTML = `
            <div style="margin:5px 0px;">
              <h5 style="margin:0;">${planet.name}</h5>
              <b>Longitude:</b> ${planet.referenceLongitude}°<br>
              <b>Distance:</b> ${planet.details.distanceFromSun}<br>
              <b>Radius:</b> ${planet.details.radius}<br>
              <b>Orbital Period:</b> ${planet.details.orbitalPeriod}
            </div>`;
          $(tooltipMarker).tooltipster({
            content: $(tooltipHTML),
            contentAsHTML: true,
            animation: 'grow',
            theme: 'tooltipster-borderless',
            trigger: 'click',
            delay: 0,
          });
        }
        tooltipMarker.style.position = 'absolute';
        tooltipMarker.style.left = `${canvas.offsetLeft + x - planet.radius}px`;
        tooltipMarker.style.top = `${canvas.offsetTop + y - planet.radius}px`;
        tooltipMarker.style.width = `${planet.radius * 2}px`;
        tooltipMarker.style.height = `${planet.radius * 2}px`;
        tooltipMarker.style.backgroundColor = 'transparent';
      });
    }
    drawPlanets();
  }
});