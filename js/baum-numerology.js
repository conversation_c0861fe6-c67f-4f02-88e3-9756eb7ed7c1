function getNumerologyReport(date) {
  // Parse the date
  const parsedDate = new Date(date);
  const day = parsedDate.getDate();
  const month = parsedDate.getMonth() + 1; // getMonth() is zero-indexed
  const year = parsedDate.getFullYear();

  // Function to reduce a number to a single digit
  function reduceToSingleDigit(num) {
      while (num >= 10) {
          num = num.toString().split('').reduce((acc, digit) => acc + parseInt(digit), 0);
      }
      return num;
  }

  // Calculate the root number from day + month + year
  const rootNumber = reduceToSingleDigit(day + month + year);

  // Numerology data based on root number
  const numerologyData = {
    1: {
      "traits": "Honest, stubborn, angry, and prideful",
      "luckyNumber": 1,
      "luckyDates": [1, 2, 4, 7, 8, 10, 16, 19, 25, 28, 29],
      "importantYears": [10, 19, 28, 37, 46, 55, 64],
      "luckyColors": ["red", "orange", "gold", "yellow"],
      "luckyDays": ["Sunday", "Monday"],
      "favorableCareers": ["Medical", "Jewelry", "Engineering", "Games"],
      "luckyGemstones": ["<PERSON>", "<PERSON>", "Topaz"]
    },
    2: {
        "traits": "Friendly, social, overconfident",
        "luckyNumber": 2,
        "luckyDates": [2, 6, 7, 9, 11, 16, 20, 19, 25, 29],
        "importantYears": [11, 16, 20, 25, 29, 34, 38, 43, 47, 52, 56, 61, 65],
        "luckyColors": ["white", "green", "cream"],
        "luckyDays": ["Monday", "Friday"],
        "favorableCareers": ["Computer", "Acting", "Accounting", "Textiles"],
        "luckyGemstones": ["Pearl"]
    },
    3: {
        "traits": "Romantic, ambitious, creative, happy",
        "luckyNumber": 3,
        "luckyDates": [3, 6, 9, 12, 15, 18, 21, 24, 27, 30],
        "importantYears": [12, 21, 30, 33, 36, 48, 57],
        "luckyColors": ["violet", "blue", "purple"],
        "luckyDays": ["Thursday", "Friday"],
        "favorableCareers": ["Broker", "Banker", "Publisher", "Tourism", "Travel"],
        "luckyGemstones": ["Amethyst", "Sapphire"]
    },
    4: {
        "traits": "Social, stubborn, debater, good planner",
        "luckyNumber": 4,
        "luckyDates": [2, 4, 8, 11, 13, 20, 22, 29, 31],
        "importantYears": [13, 17, 22, 26, 31, 40, 48, 49, 58],
        "luckyColors": ["blue", "grey", "cream"],
        "luckyDays": ["Saturday", "Sunday"],
        "favorableCareers": ["Electronics", "Property", "Politics", "Management"],
        "luckyGemstones": ["Blue Sapphire"]
    },
    5: {
        "traits": "Intelligent, sharp-minded, restless, friendly",
        "luckyNumber": 5,
        "luckyDates": [5, 6, 14, 15, 23, 24],
        "importantYears": [14, 19, 23, 28, 32, 41, 50, 59],
        "luckyColors": ["white", "red", "green"],
        "luckyDays": ["Wednesday", "Friday"],
        "favorableCareers": ["Publisher", "Writer", "Jewelry", "Computer", "Clothes"],
        "luckyGemstones": ["Emerald", "Diamond"]
    },
    6: {
        "traits": "Friendly, impatient, magnetic, soft-spoken",
        "luckyNumber": 6,
        "luckyDates": [3, 6, 9, 12, 14, 15, 18, 21, 24, 27, 30],
        "importantYears": [6, 15, 24, 33, 35, 42, 51, 63],
        "luckyColors": ["blue", "pink"],
        "luckyDays": ["Tuesday", "Friday"],
        "favorableCareers": ["Teacher", "Hotel", "Cosmetics", "Broker", "Clothes"],
        "luckyGemstones": ["Emerald", "Diamond"]
    },
    7: {
        "traits": "Clever, restless, attractive, holy",
        "luckyNumber": 7,
        "luckyDates": [2, 7, 11, 16, 20, 25, 29],
        "importantYears": [7, 11, 16, 20, 25, 38, 43, 52],
        "luckyColors": ["white", "yellow"],
        "luckyDays": ["Sunday", "Monday"],
        "favorableCareers": ["Teaching", "Engineering", "Machinery", "Garments"],
        "luckyGemstones": ["Pearl", "Diamond"]
    },
    8: {
        "traits": "Professional, deep thinker, angry, ambitious",
        "luckyNumber": 8,
        "luckyDates": [1, 4, 7, 8, 10, 16, 17, 19, 25, 26, 31],
        "importantYears": [8, 13, 17, 22, 26, 31, 35, 44, 53, 62],
        "luckyColors": ["red", "black", "blue"],
        "luckyDays": ["Saturday", "Sunday"],
        "favorableCareers": ["Computer", "Builder", "Property", "Metals"],
        "luckyGemstones": ["Blue Sapphire"]
    },
    9: {
        "traits": "Hard worker, impatient, generous, angry",
        "luckyNumber": 9,
        "luckyDates": [6, 9, 14, 15, 18, 24, 27],
        "importantYears": [9, 12, 18, 21, 27, 30, 36, 45, 54, 63],
        "luckyColors": ["red", "pink"],
        "luckyDays": ["Tuesday", "Thursday", "Friday"],
        "favorableCareers": ["Garments", "Industry", "Medical"],
        "luckyGemstones": ["Ruby", "Red Coral"]
    }
  };

  // Get the numerology report based on the calculated root number
  const report = numerologyData[rootNumber] || {};

  // Display the report in a formatted way
  return `<p style="margin:15px;">
      <strong class='center'>Those with a connection to this day are ${report.traits}.</strong><br><br>
      <small>The lucky number for this day is ${report.luckyNumber}. Lucky connected days of the month are ${report.luckyDates.join(", ")}. Important years in the future are ${report.importantYears.join(", ")}.</small><br><br>
      Lucky colors are ${report.luckyColors.join(", ")}. Lucky days of the week are ${report.luckyDays.join(" and ")}.
      Remarkable areas of business include ${report.favorableCareers.join(", ")}. And lucky gemstone(s) are ${report.luckyGemstones.join(" and ")}.
      </p>
    <h6 style='display:block;margin:10px;' class='center'>The Root Number is ${rootNumber}</h6>`;
}

jQuery(document).ready(function($) {
  $('.numerologyResult').each(function() {
      const $element = $(this);
      const date = $element.data('date');
      const reportHtml = getNumerologyReport(date);
      $element.html(reportHtml);
  });
});
