window.baum = window.baum || {}; 

jQuery(document).ready(function ($) {

if (!baum_bookmarks.is_page) { 

  console.log('baum_bookmarks', baum_bookmarks);

  // Sync bookmarks from server to localStorage
  if (baum_bookmarks.is_logged_in && baum_bookmarks.my_bookmarks) {
    localStorage.setItem('baum_bookmarks', baum_bookmarks.my_bookmarks);
  }

  bookmarks = localStorage.getItem('baum_bookmarks'); 
  if (!bookmarks) localStorage.setItem('baum_bookmarks', ''); 

  console.log('bookmarks', bookmarks); 

  // Save bookmarks to server
  function saveBookmarksToServer (bmarks) {
    $.post(baum_bookmarks.ajax_url, {
      action: 'save_baum_bookmarks',
      my_bookmarks: bmarks,
      save_nonce: baum_bookmarks.save_nonce
    }).done(function (response) {
      console.log('Bookmarks saved to server:', response);
    }).fail(function (error) {
      console.log('Error saving bookmarks:', error, bmarks);
    });
  }

  $('.infinite-content').on('append.infiniteScroll', function () {
    updateBookmarks(); 
  });

  // $('body').on('DOMSubtreeModified', '.infinite-content', function () {
  //   updateBookmarks(); 
  // }); 

  /* 
  * 
  * Bookmarks 
  * 
  */ 

  function addBookmark (post_id, callback) { 
    var bmarks = localStorage.getItem('baum_bookmarks'); 
    if (bmarks) { 
      bmarks = bmarks.split(','); 
    } else { 
      bmarks = []; 
    } 
    bmarks = bmarks.filter(onlyUnique); 
    bmarks.unshift(post_id); 
    bmarks = bmarks.join(','); 
    localStorage.setItem('baum_bookmarks', bmarks); 
    saveBookmarksToServer(bmarks); 
    callback(true, false); 
  } 

  function removeBookmark (post_id, callback) { 
    var bmarks = localStorage.getItem('baum_bookmarks'); 
    if (bmarks) { 
      bmarks = bmarks.split(','); 
    } else { 
      bmarks = []; 
    } 
    bmarks = bmarks.filter(onlyUnique); 
    var value_index = bmarks.indexOf(post_id); 
    if (value_index !== -1) { 
      bmarks.splice(value_index, 1); 
    } 
    bmarks = bmarks.join(','); 
    localStorage.setItem('baum_bookmarks', bmarks); 
    saveBookmarksToServer(bmarks); 
    callback(true, false); 
  } 

  function updateBookmarks () { 
    var bookmarks = localStorage.getItem('baum_bookmarks'); 
    if (bookmarks) { 
      bookmarks = bookmarks.split(','); 
    } else { 
      bookmarks = []; 
    } 

    $('.baum-bookmark').each(function (index, element) { 
      var $this = $(this); 
      var type = $(element).get(0).tagName.toLowerCase(); 
      var tooltipstered = $this.hasClass('tooltipstered'); 
      console.log('tooltipstered', tooltipstered); 
      if (tooltipstered) $this.tooltipster('destroy'); 

      var title = $this.attr('data-title'); 
      var reverse_title = $this.attr('data-reverse-title'); 
      var post_id = $this.attr('data-baum-id'); 
      var value_index = bookmarks.indexOf(post_id); 
      var tips = $.tooltipster.instances('.baum-bookmark'); 

      // console.log('tips', tips, title, reverse_title); 


      $this.off(); 
      // console.log('type', type); 
      if (type !== 'button') { 
        $this.tooltipster({ 
          delay: 0, 
          speed: 0, 
          animation: 'grow', 
          restoration: 'previous', 
          theme: 'tooltipster-borderless', 
          interactive: true, 
          functionReady: function () { 
            // $this.tooltipster('content', ''); 

          } 
        }); 
      } 
      if (value_index === -1) { 
        $this.removeClass('baum-bookmarked'); 
        $this.addClass('baum-unbookmarked'); 
        if (type === 'button') { 
          if (title) $this.find('span').text(title); 
        } else {
          if (title) $this.tooltipster('content', title); 
        }
      } else { 
        $this.addClass('baum-bookmarked'); 
        $this.removeClass('baum-unbookmarked'); 
        if (type === 'button') {
          if (reverse_title) $this.find('span').text(reverse_title); 
        } else {
          if (reverse_title) $this.tooltipster('content', reverse_title); 
        }
      } 
    }); 

    // $('.baum-bookmark').off(); 
    // $('.baum-bookmarked').off(); 
    // $('.baum-unbookmarked').off(); 
    // $this.tooltipster('option', 'side', 'left'); 
    // $(this).addClass('baum-bookmarked'); 
    // $(this).removeClass('baum-unbookmarked'); 

    $('.baum-unbookmarked').on('click', function (event) { 
      // var title = $(this).attr('data-title'); 
      var reverse_title = $(this).attr('data-reverse-title'); 
      event.preventDefault(); 
      event.stopPropagation(); 
      event.stopImmediatePropagation(); 
      var post_id = $(this).attr('data-baum-id'); 
      var $this = $(this); 
      var tooltipstered = $this.hasClass('tooltipstered'); 
      if (tooltipstered) {
        // $this.tooltipster('hide'); 
        $this.tooltipster('content', 'Saving'); 
        addBookmark(post_id, function (success, error) {
          $this.tooltipster('show'); 
          setTimeout(function () { 
            // console.log('hide', 'Saving'); 
            $this.tooltipster('hide'); 
            updateBookmarks(); 
            // if (reverse_title) {
            //   $this.tooltipster('content', reverse_title); 
            //   $this.find('span').text(reverse_title); 
            // }
          }, 1000); 
        }); 
      } else {
        addBookmark(post_id, function (success, error) {
          $this.find('span').text(reverse_title); 
          updateBookmarks(); 
        }); 
      }
    }); 
    
    $('.baum-bookmarked').on('click', function (event) { 
      var title = $(this).attr('data-title'); 
      // var reverse_title = $(this).attr('data-reverse-title'); 
      event.preventDefault(); 
      event.stopPropagation(); 
      event.stopImmediatePropagation(); 
      var post_id = $(this).attr('data-baum-id'); 
      var $this = $(this); 
      var tooltipstered = $this.hasClass('tooltipstered'); 
      if (tooltipstered) {
        // $this.tooltipster('hide'); 
        $this.tooltipster('content', 'Saving'); 
        removeBookmark(post_id, function (success, error) {
          $this.tooltipster('show'); 
          setTimeout(function () { 
            // console.log('hide', 'Saving'); 
            $this.tooltipster('hide'); 
            updateBookmarks(); 
            // if (title) {
            //   $this.tooltipster('content', title); 
            //   $this.find('span').text(title); 
            // }
          }, 1000); 
        }); 
      } else {
        removeBookmark(post_id, function (success, error) {
          $this.find('span').text(title); 
          updateBookmarks(); 
        }); 
      }
    }); 

    // if (baum_bookmarks.is_page) { 
    //   $('.baum-unbookmarked').parents('.baum-bookmark-container').remove(); 
    // } 
  } 

  updateBookmarks(); 

  $('.baum-meme-bookmark-button').on('mousedown', function (event) {
    event.preventDefault(); 
    event.stopPropagation(); 
    event.stopImmediatePropagation(); 
    $(this).children('.baum-unbookmarked').click(); 
  }); 

}
  if (baum_bookmarks.is_page) { 
    // bookmarks = localStorage.getItem('baum_bookmarks'); 
    // if (!bookmarks) { 
    //   $('.baum_bookmarks_loading').hide(); 
    //   $('.baum_bookmarks_empty').show(); 
    // } else { 
      $.post(baum_bookmarks.ajax_url, { 
        action: 'action_get_baum_bookmarks', 
        // bookmarks: bookmarks, 
        author_id: Number(baum_bookmarks.author_id) ? baum_bookmarks.author_id : baum_bookmarks.current_user_id, 
        nonce: baum_bookmarks.nonce, 
      }).fail(function (error) { 
        console.log('baum_bookmarks error:', error); 
        $('.baum_bookmarks_loading').hide(); 
        $('.baum_bookmarks').html(error); 
      }).done(function (response) { 
        $('.baum_bookmarks_loading').hide(); 
        if (response) {
          $('.baum_bookmarks').html(response); 
          // updateBookmarks(); 
        } else {
          $('.baum_bookmarks_empty').show(); 
        }
        // console.log('baum_bookmarks response:', response); 
      }); 
    // } 

  }

}); 

/* 
 * 
 * Utilities 
 * 
 */ 

function onlyUnique (value, index, array) {
  return array.indexOf(value) === index;
}
