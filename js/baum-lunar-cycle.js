initMoonVisual = function () {}; 

jQuery(document).ready(function ($) {
  
  // initMoonVisual = initMoonVisual; 

  initMoonVisual = function initMoonVisual (date, elementId) { 
    var container = document.getElementById(elementId); 

    // Use SunCalc to get moon phase information for the specified date
    var moonData = SunCalc.getMoonIllumination(new Date(date)); 
    var moonPhase = moonData.phase;  // Moon phase is between 0 (New Moon) and 1 (Full Moon)
    
    // Calculate which image to show based on the phase
    var moonImageIndex = getMoonImageIndex(moonPhase)[0]; 

    // Calculate which color to show based on the phase
    var moonColor = getMoonImageIndex(moonPhase)[2]; 

    var moonDistance = (SunCalc.getMoonPosition(new Date(date), 0, 0).distance / 1000).toFixed(0) + ' km'; 

    var moonAge = (moonPhase * 29.53).toFixed(1) + ' days'; 

    var moonIlluminati = (moonData.fraction * 100).toFixed(0) + '%'; 

    var moonImage = document.createElement('img');
    moonImage.src = plugin_url + 'images/' + moonImageIndex + '.png';
    moonImage.alt = 'Moon phase';

    container.innerHTML = '';  // Clear previous content
    container.appendChild(moonImage);

    $('.baum-lunar-cycle img').addClass(moonColor); 

    $('#phase-' + elementId).text(getMoonPhaseDescription(moonPhase)); 
    // $('#phase-' + elementId).addClass(moonColor); 
    
    $('#illumination-' + elementId).text(moonIlluminati); 
    
    $('#distance-' + elementId).text(moonDistance); 

    $('#age-' + elementId).text(moonAge); 




    // // Update the DOM with moon info 
    // document.getElementById('phase-' + elementId).classList.add(moonColorIndex);
    // document.getElementById('phase-' + elementId).innerText = getMoonPhaseDescription(moonPhase); 
    // document.getElementById('illumination-' + elementId).innerText = (moonData.fraction * 100).toFixed(0) + "%"; 
    // document.getElementById('distance-' + elementId).innerText = (SunCalc.getMoonPosition(new Date(date), 0, 0).distance / 1000).toFixed(0) + " km"; 
    // document.getElementById('age-' + elementId).innerText = (moonPhase * 29.53).toFixed(1) + " days"; 
  }

  function getMoonImageIndex(phase) {
    // Map the phase (0 to 1) to an index (0 to 7) 
    if (phase < 0.0625 || phase >= 0.9375) return [ 
      0, 
      'New Moon', 
      'white-bg', 
      'blue-bg' 
    ];  
    if (phase >= 0.0625 && phase < 0.1875) return [ 
      1, 
      'Waxing Crescent Moon', 
      'white-bg', 
      'purple-bg' 
    ]; 
    if (phase >= 0.1875 && phase < 0.3125) return [ 
      2, 
      'First Quarter Moon', 
      'white-bg', 
      'yellow-bg' 
    ]; 
    if (phase >= 0.3125 && phase < 0.4375) return [ 
      3, 
      'Waxing Gibbous Moon', 
      'white-bg', 
      'orange-bg' 
    ]; 
    if (phase >= 0.4375 && phase < 0.5625) return [ 
      4, 
      'Full Moon', 
      'white-bg', 
      'red-bg' 
    ]; 
    if (phase >= 0.5625 && phase < 0.6875) return [ 
      5, 
      'Waning Gibbous Moon', 
      'white-bg', 
      'orange-bg' 
    ]; 
    if (phase >= 0.6875 && phase < 0.8125) return [ 
      6, 
      'Last Quarter Moon', 
      'white-bg', 
      'yellow-bg' 
    ]; 
    if (phase >= 0.8125 && phase < 0.9375) return [ 
      7, 
      'Waning Crescent Moon', 
      'white-bg', 
      'purple-bg' 
    ]; 
  }

  function getMoonPhaseDescription(phase) {
    if (phase < 0.0625 || phase >= 0.9375) return 'New Moon';
    if (phase >= 0.0625 && phase < 0.1875) return 'Waxing Crescent Moon';
    if (phase >= 0.1875 && phase < 0.3125) return 'First Quarter Moon';
    if (phase >= 0.3125 && phase < 0.4375) return 'Waxing Gibbous Moon';
    if (phase >= 0.4375 && phase < 0.5625) return 'Full Moon';
    if (phase >= 0.5625 && phase < 0.6875) return 'Waning Gibbous Moon';
    if (phase >= 0.6875 && phase < 0.8125) return 'Last Quarter Moon';
    if (phase >= 0.8125 && phase < 0.9375) return 'Waning Crescent Moon';
  }

// function getMoonImageIndex(phase) {
//   // Map the phase (0 to 1) to an index (0 to 7) 
//   if (phase < 0.0625 || phase >= 0.9375) return 0;  // New Moon 
//   if (phase >= 0.0625 && phase < 0.1875) return 1;  // Waxing Crescent 
//   if (phase >= 0.1875 && phase < 0.3125) return 2;  // First Quarter 
//   if (phase >= 0.3125 && phase < 0.4375) return 3;  // Waxing Gibbous 
//   if (phase >= 0.4375 && phase < 0.5625) return 4;  // Full Moon 
//   if (phase >= 0.5625 && phase < 0.6875) return 5;  // Waning Gibbous 
//   if (phase >= 0.6875 && phase < 0.8125) return 6;  // Last Quarter 
//   if (phase >= 0.8125 && phase < 0.9375) return 7;  // Waning Crescent 
// }

});