<?php
/**
 * Activity Content Types Helper Functions
 * 
 * Provides easy-to-use functions for rendering content type selectors
 * and managing activity content types throughout the theme.
 * 
 * @package BaumPress
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Render activity content type selector
 * 
 * @param array $args Configuration arguments
 * @return string HTML output
 */
function baum_activity_content_type_selector($args = array()) {
  if (!class_exists('Baum_Activity_Content_Types')) {
    return '<p>Activity Content Types not available.</p>';
  }
  
  $defaults = array(
    'selected' => 'post',
    'name' => 'activity_content_type',
    'id' => 'activity-content-type-selector',
    'class' => 'baum-content-type-selector',
    'layout' => 'grid',
    'columns' => 4,
    'show_descriptions' => true,
    'post_id' => 0,
    'include_fields' => true,
    'field_container_class' => 'baum-content-type-fields'
  );
  
  $args = wp_parse_args($args, $defaults);
  
  // Get current selection from post meta if post_id provided
  if ($args['post_id']) {
    $current_type = get_post_meta($args['post_id'], '_baum_activity_type', true);
    if ($current_type) {
      $args['selected'] = $current_type;
    }
  }
  
  $output = Baum_Activity_Content_Types::render_content_type_selector($args['selected'], $args);
  
  // Add fields container if requested
  if ($args['include_fields']) {
    $output .= '<div class="' . esc_attr($args['field_container_class']) . '" data-post-id="' . intval($args['post_id']) . '"></div>';
  }
  
  return $output;
}

/**
 * Get activity content type for a post
 * 
 * @param int $post_id Post ID
 * @return string Content type key
 */
function baum_get_activity_content_type($post_id = null) {
  if (!$post_id) {
    $post_id = get_the_ID();
  }
  
  if (!$post_id) {
    return 'post';
  }
  
  $type = get_post_meta($post_id, '_baum_activity_type', true);
  return $type ?: 'post';
}

/**
 * Get activity content type data
 * 
 * @param string $type Content type key
 * @return array|false Content type data or false if not found
 */
function baum_get_activity_content_type_data($type = null) {
  if (!class_exists('Baum_Activity_Content_Types')) {
    return false;
  }
  
  if (!$type) {
    $type = baum_get_activity_content_type();
  }
  
  return Baum_Activity_Content_Types::get_content_type($type);
}

/**
 * Get activity content type field value
 * 
 * @param string $field Field name
 * @param int $post_id Post ID
 * @return mixed Field value
 */
function baum_get_activity_field($field, $post_id = null) {
  if (!$post_id) {
    $post_id = get_the_ID();
  }
  
  if (!$post_id) {
    return '';
  }
  
  return get_post_meta($post_id, '_baum_' . $field, true);
}

/**
 * Display activity content type icon
 * 
 * @param string $type Content type key
 * @param array $args Icon arguments
 * @return string HTML output
 */
function baum_activity_content_type_icon($type = null, $args = array()) {
  if (!$type) {
    $type = baum_get_activity_content_type();
  }
  
  $type_data = baum_get_activity_content_type_data($type);
  if (!$type_data) {
    return '';
  }
  
  $defaults = array(
    'size' => '16px',
    'color' => $type_data['color'],
    'class' => 'baum-activity-type-icon',
    'title' => $type_data['label']
  );
  
  $args = wp_parse_args($args, $defaults);
  
  return sprintf(
    '<i class="%s %s" style="font-size: %s; color: %s;" title="%s"></i>',
    esc_attr($type_data['icon']),
    esc_attr($args['class']),
    esc_attr($args['size']),
    esc_attr($args['color']),
    esc_attr($args['title'])
  );
}

/**
 * Display activity content type label
 * 
 * @param string $type Content type key
 * @param array $args Label arguments
 * @return string HTML output
 */
function baum_activity_content_type_label($type = null, $args = array()) {
  if (!$type) {
    $type = baum_get_activity_content_type();
  }
  
  $type_data = baum_get_activity_content_type_data($type);
  if (!$type_data) {
    return '';
  }
  
  $defaults = array(
    'show_icon' => true,
    'icon_size' => '14px',
    'class' => 'baum-activity-type-label',
    'color' => $type_data['color']
  );
  
  $args = wp_parse_args($args, $defaults);
  
  $output = '';
  
  if ($args['show_icon']) {
    $output .= baum_activity_content_type_icon($type, array(
      'size' => $args['icon_size'],
      'color' => $args['color']
    )) . ' ';
  }
  
  $output .= '<span class="' . esc_attr($args['class']) . '" style="color: ' . esc_attr($args['color']) . ';">';
  $output .= esc_html($type_data['label']);
  $output .= '</span>';
  
  return $output;
}

/**
 * Check if activity has specific content type
 * 
 * @param string $type Content type to check
 * @param int $post_id Post ID
 * @return bool True if matches
 */
function baum_activity_is_type($type, $post_id = null) {
  return baum_get_activity_content_type($post_id) === $type;
}

/**
 * Get activities by content type
 * 
 * @param string $type Content type
 * @param array $args Query arguments
 * @return WP_Query Query object
 */
function baum_get_activities_by_type($type, $args = array()) {
  $defaults = array(
    'post_type' => 'activity',
    'posts_per_page' => 10,
    'meta_query' => array(
      array(
        'key' => '_baum_activity_type',
        'value' => $type,
        'compare' => '='
      )
    )
  );
  
  $args = wp_parse_args($args, $defaults);
  
  return new WP_Query($args);
}

/**
 * Render activity content based on type
 * 
 * @param int $post_id Post ID
 * @param array $args Rendering arguments
 * @return string HTML output
 */
function baum_render_activity_content($post_id = null, $args = array()) {
  if (!$post_id) {
    $post_id = get_the_ID();
  }
  
  if (!$post_id) {
    return '';
  }
  
  $type = baum_get_activity_content_type($post_id);
  $type_data = baum_get_activity_content_type_data($type);
  
  if (!$type_data) {
    return get_the_content();
  }
  
  $defaults = array(
    'show_type_label' => true,
    'show_meta' => true,
    'class' => 'baum-activity-content'
  );
  
  $args = wp_parse_args($args, $defaults);
  
  ob_start();
  
  echo '<div class="' . esc_attr($args['class']) . ' baum-activity-type-' . esc_attr($type) . '">';
  
  // Type label
  if ($args['show_type_label']) {
    echo '<div class="baum-activity-type-header">';
    echo baum_activity_content_type_label($type);
    echo '</div>';
  }
  
  // Content based on type
  switch ($type) {
    case 'poll':
      echo '<div class="baum-poll-content">';
      echo '<div class="baum-poll-question">' . get_the_content() . '</div>';
      echo '<p><em>Poll functionality will be implemented here.</em></p>';
      echo '</div>';
      break;
      
    case 'event':
      echo '<div class="baum-event-content">';
      echo '<div class="baum-event-description">' . get_the_content() . '</div>';
      echo '<p><em>Event details will be displayed here.</em></p>';
      echo '</div>';
      break;
      
    case 'video':
    case 'audio':
      echo '<div class="baum-media-content baum-' . $type . '-content">';
      echo '<div class="baum-media-description">' . get_the_content() . '</div>';
      echo '<p><em>' . ucfirst($type) . ' player will be embedded here.</em></p>';
      echo '</div>';
      break;
      
    case 'link':
      echo '<div class="baum-link-content">';
      echo '<div class="baum-link-text">' . get_the_content() . '</div>';
      echo '<p><em>Link preview will be displayed here.</em></p>';
      echo '</div>';
      break;
      
    case 'location':
      echo '<div class="baum-location-content">';
      echo '<div class="baum-location-text">' . get_the_content() . '</div>';
      echo '<p><em>Location details and map will be shown here.</em></p>';
      echo '</div>';
      break;
      
    case 'quote':
      echo '<div class="baum-quote-content">';
      echo '<blockquote class="baum-quote-text">';
      echo '<i class="fas fa-quote-left"></i>';
      echo '<p>' . get_the_content() . '</p>';
      echo '</blockquote>';
      echo '</div>';
      break;
      
    default:
      echo '<div class="baum-activity-content-body">';
      echo apply_filters('the_content', get_post_field('post_content', $post_id));
      echo '</div>';
      break;
  }
  
  // Meta information
  if ($args['show_meta']) {
    echo '<div class="baum-activity-meta">';
    echo '<div class="baum-activity-date">' . get_the_date('M j, Y g:i A', $post_id) . '</div>';
    echo '<div class="baum-activity-author">by ' . get_the_author_meta('display_name', get_post_field('post_author', $post_id)) . '</div>';
    echo '</div>';
  }
  
  echo '</div>';
  
  return ob_get_clean();
}
