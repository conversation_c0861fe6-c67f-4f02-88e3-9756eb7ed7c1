<?php
/**
 * Activity Content Type Selection System
 * 
 * Provides a comprehensive content type selection interface for Activity CPTs
 * with icons, descriptions, and dynamic field management.
 * 
 * @package BaumPress
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class Baum_Activity_Content_Types {
  
  /**
   * Available content types for activities
   */
  const CONTENT_TYPES = array(
    'post' => array(
      'label' => 'Text Post',
      'icon' => 'fas fa-align-left',
      'color' => '#3498db',
      'description' => 'Standard text-based post with optional media',
      'fields' => array('content', 'tags', 'visibility'),
      'supports' => array('editor', 'thumbnail', 'excerpt')
    ),
    'announcement' => array(
      'label' => 'Announcement',
      'icon' => 'fas fa-bullhorn',
      'color' => '#e74c3c',
      'description' => 'Important announcement or notice',
      'fields' => array('content', 'priority', 'expiry_date'),
      'supports' => array('editor', 'thumbnail')
    ),
    'question' => array(
      'label' => 'Question',
      'icon' => 'fas fa-question-circle',
      'color' => '#f39c12',
      'description' => 'Ask a question to the community',
      'fields' => array('content', 'tags', 'best_answer'),
      'supports' => array('editor', 'comments')
    ),
    'poll' => array(
      'label' => 'Poll',
      'icon' => 'fas fa-poll',
      'color' => '#9b59b6',
      'description' => 'Create a poll with multiple choice options',
      'fields' => array('content', 'poll_options', 'poll_end_date', 'multiple_choice'),
      'supports' => array('editor')
    ),
    'attending' => array(
      'label' => 'Attending/RSVP',
      'icon' => 'fas fa-calendar-check',
      'color' => '#2ecc71',
      'description' => 'RSVP or show attendance to an event',
      'fields' => array('content', 'event_selection', 'rsvp_status'),
      'supports' => array('editor', 'thumbnail')
    ),
    'image' => array(
      'label' => 'Image/Photo',
      'icon' => 'fas fa-image',
      'color' => '#1abc9c',
      'description' => 'Share photos or images',
      'fields' => array('content', 'image_gallery', 'alt_text'),
      'supports' => array('editor', 'thumbnail')
    ),
    'video' => array(
      'label' => 'Video',
      'icon' => 'fas fa-video',
      'color' => '#e67e22',
      'description' => 'Upload or embed video content',
      'fields' => array('content', 'video_source', 'duration'),
      'supports' => array('editor', 'thumbnail')
    ),
    'audio' => array(
      'label' => 'Audio/Podcast',
      'icon' => 'fas fa-headphones',
      'color' => '#8e44ad',
      'description' => 'Share audio content or podcasts',
      'fields' => array('content', 'audio_url', 'audio_file', 'duration', 'transcript'),
      'supports' => array('editor', 'thumbnail')
    ),
    'live_video' => array(
      'label' => 'Live Video',
      'icon' => 'fas fa-broadcast-tower',
      'color' => '#c0392b',
      'description' => 'Live video stream or broadcast',
      'fields' => array('content', 'stream_url', 'stream_key', 'scheduled_time'),
      'supports' => array('editor')
    ),
    'live_audio' => array(
      'label' => 'Live Audio',
      'icon' => 'fas fa-microphone',
      'color' => '#d35400',
      'description' => 'Live audio stream or radio',
      'fields' => array('content', 'stream_url', 'stream_key', 'scheduled_time'),
      'supports' => array('editor')
    ),
    'link' => array(
      'label' => 'Link Share',
      'icon' => 'fas fa-link',
      'color' => '#34495e',
      'description' => 'Share a link with preview',
      'fields' => array('content', 'link_url', 'link_title', 'link_description'),
      'supports' => array('editor')
    ),
    'location' => array(
      'label' => 'Location Check-in',
      'icon' => 'fas fa-map-marker-alt',
      'color' => '#16a085',
      'description' => 'Share your location or check-in',
      'fields' => array('content', 'location_name', 'coordinates', 'address'),
      'supports' => array('editor', 'thumbnail')
    ),
    'quote' => array(
      'label' => 'Quote',
      'icon' => 'fas fa-quote-left',
      'color' => '#7f8c8d',
      'description' => 'Share an inspirational or notable quote',
      'fields' => array('quote_text', 'quote_author', 'quote_source'),
      'supports' => array('editor')
    ),
    'file_share' => array(
      'label' => 'File Share',
      'icon' => 'fas fa-file-upload',
      'color' => '#95a5a6',
      'description' => 'Share documents or files',
      'fields' => array('content', 'file_url', 'file_name', 'file_size', 'file_type'),
      'supports' => array('editor')
    ),
    'life_event' => array(
      'label' => 'Life Event',
      'icon' => 'fas fa-heart',
      'color' => '#e91e63',
      'description' => 'Share important life milestones',
      'fields' => array('content', 'event_type', 'event_date', 'privacy_level'),
      'supports' => array('editor', 'thumbnail')
    )
  );

  /**
   * Constructor
   */
  public function __construct() {
    add_action('init', array($this, 'init'));
    add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    
    // AJAX handlers
    add_action('wp_ajax_baum_get_content_type_fields', array($this, 'ajax_get_content_type_fields'));
    add_action('wp_ajax_nopriv_baum_get_content_type_fields', array($this, 'ajax_get_content_type_fields'));
    add_action('wp_ajax_baum_search_events', array($this, 'ajax_search_events'));
    add_action('wp_ajax_baum_search_youtube_videos', array($this, 'ajax_search_youtube_videos'));
  }

  /**
   * Initialize
   */
  public function init() {
    // Add meta boxes for activity content types
    add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
    add_action('save_post', array($this, 'save_activity_meta'));

    // Add content type taxonomy if it doesn't exist
    $this->register_content_type_taxonomy();
  }

  /**
   * Register content type taxonomy
   */
  public function register_content_type_taxonomy() {
    if (!taxonomy_exists('activity_content_type')) {
      register_taxonomy('activity_content_type', 'activity', array(
        'labels' => array(
          'name' => __('Content Types', 'baumpress'),
          'singular_name' => __('Content Type', 'baumpress'),
          'search_items' => __('Search Content Types', 'baumpress'),
          'all_items' => __('All Content Types', 'baumpress'),
          'edit_item' => __('Edit Content Type', 'baumpress'),
          'update_item' => __('Update Content Type', 'baumpress'),
          'add_new_item' => __('Add New Content Type', 'baumpress'),
          'new_item_name' => __('New Content Type Name', 'baumpress')
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'activity-type'),
        'show_in_rest' => true
      ));
    }
  }

  /**
   * Add meta boxes for activity content types
   */
  public function add_meta_boxes() {
    add_meta_box(
      'activity_content_type_meta_box',
      '<span class="dashicons dashicons-admin-post"></span> ' . __('Activity Content Type', 'baumpress'),
      array($this, 'activity_content_type_meta_box_callback'),
      'activity',
      'normal',
      'high'
    );

    add_meta_box(
      'activity_content_fields_meta_box',
      '<span class="dashicons dashicons-admin-settings"></span> ' . __('Content Type Fields', 'baumpress'),
      array($this, 'activity_content_fields_meta_box_callback'),
      'activity',
      'normal',
      'high'
    );
  }

  /**
   * Activity content type meta box callback
   */
  public function activity_content_type_meta_box_callback($post) {
    wp_nonce_field('activity_content_type_meta_box', 'activity_content_type_meta_box_nonce');

    $current_type = get_post_meta($post->ID, '_activity_content_type', true) ?: 'post';

    echo '<div class="baum-activity-content-type-selector">';
    echo self::render_content_type_selector($current_type, array(
      'name' => 'activity_content_type',
      'layout' => 'grid',
      'columns' => 4,
      'show_descriptions' => true
    ));
    echo '</div>';

    echo '<style>
      .baum-activity-content-type-selector {
        margin: 15px 0;
      }
      .baum-activity-content-type-selector .baum-content-type-grid {
        max-height: 400px;
        overflow-y: auto;
      }
    </style>';
  }

  /**
   * Activity content fields meta box callback
   */
  public function activity_content_fields_meta_box_callback($post) {
    wp_nonce_field('activity_content_fields_meta_box', 'activity_content_fields_meta_box_nonce');

    $current_type = get_post_meta($post->ID, '_activity_content_type', true) ?: 'post';
    $type_data = self::get_content_type($current_type);

    echo '<div id="activity-content-fields-container">';

    if ($type_data && !empty($type_data['fields'])) {
      echo '<p><strong>' . sprintf(__('Fields for %s:', 'baumpress'), $type_data['label']) . '</strong></p>';

      foreach ($type_data['fields'] as $field) {
        $field_value = get_post_meta($post->ID, '_activity_' . $field, true);
        $this->render_field($field, $field_value, $post->ID);
      }
    } else {
      echo '<p>' . __('No additional fields for this content type.', 'baumpress') . '</p>';
    }

    echo '</div>';

    echo '<script>
      jQuery(document).ready(function($) {
        $(document).on("change", "input[name=\"activity_content_type\"]", function() {
          var selectedType = $(this).val();

          $.ajax({
            url: ajaxurl,
            type: "POST",
            data: {
              action: "baum_get_content_type_fields",
              type: selectedType,
              post_id: ' . intval($post->ID) . ',
              nonce: "' . wp_create_nonce('baum_activity_types_nonce') . '"
            },
            success: function(response) {
              if (response.success) {
                $("#activity-content-fields-container").html(response.data.html);
              }
            }
          });
        });
      });
    </script>';
  }

  /**
   * Render individual field based on field type
   */
  private function render_field($field, $value = '', $post_id = 0) {
    $field_name = 'activity_' . $field;
    $field_id = 'activity-' . str_replace('_', '-', $field);

    echo '<div class="baum-field-group" style="margin-bottom: 15px;">';
    echo '<label for="' . esc_attr($field_id) . '" style="display: block; font-weight: 600; margin-bottom: 5px;">';
    echo esc_html(ucwords(str_replace('_', ' ', $field))) . ':';
    echo '</label>';

    switch ($field) {
      case 'content':
        echo '<textarea id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" rows="4" style="width: 100%;" placeholder="' . esc_attr__('Enter content...', 'baumpress') . '">' . esc_textarea($value) . '</textarea>';
        break;

      case 'tags':
        echo '<input type="text" id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '" style="width: 100%;" placeholder="' . esc_attr__('Enter tags separated by commas...', 'baumpress') . '">';
        break;

      case 'priority':
        $priorities = array('low' => __('Low', 'baumpress'), 'medium' => __('Medium', 'baumpress'), 'high' => __('High', 'baumpress'), 'urgent' => __('Urgent', 'baumpress'));
        echo '<select id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" style="width: 100%;">';
        foreach ($priorities as $key => $label) {
          echo '<option value="' . esc_attr($key) . '"' . selected($value, $key, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        break;

      case 'visibility':
        $visibility_options = array('public' => __('Public', 'baumpress'), 'private' => __('Private', 'baumpress'), 'friends' => __('Friends Only', 'baumpress'));
        echo '<select id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" style="width: 100%;">';
        foreach ($visibility_options as $key => $label) {
          echo '<option value="' . esc_attr($key) . '"' . selected($value, $key, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        break;

      case 'expiry_date':
      case 'event_date':
      case 'poll_end_date':
        echo '<input type="date" id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '" style="width: 100%;">';
        break;

      case 'event_time':
        echo '<input type="time" id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '" style="width: 100%;">';
        break;

      case 'multiple_choice':
      case 'rsvp':
        echo '<label style="display: flex; align-items: center;">';
        echo '<input type="checkbox" id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" value="1"' . checked($value, '1', false) . ' style="margin-right: 8px;">';
        echo esc_html(ucwords(str_replace('_', ' ', $field)));
        echo '</label>';
        break;

      case 'link_url':
      case 'video_url':
      case 'audio_url':
      case 'stream_url':
        echo '<input type="url" id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '" style="width: 100%;" placeholder="' . esc_attr__('https://...', 'baumpress') . '">';
        break;

      case 'poll_options':
        $options = is_array($value) ? $value : explode("\n", $value);
        echo '<textarea id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" rows="4" style="width: 100%;" placeholder="' . esc_attr__('Enter each option on a new line...', 'baumpress') . '">' . esc_textarea(implode("\n", $options)) . '</textarea>';
        break;

      case 'event_selection':
        $this->render_event_selection_field($field_id, $field_name, $value);
        break;

      case 'rsvp_status':
        $rsvp_options = array(
          'going' => __('Going', 'baumpress'),
          'maybe' => __('Maybe', 'baumpress'),
          'not_going' => __('Not Going', 'baumpress')
        );
        echo '<select id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" style="width: 100%;">';
        echo '<option value="">' . __('Select RSVP Status', 'baumpress') . '</option>';
        foreach ($rsvp_options as $key => $label) {
          echo '<option value="' . esc_attr($key) . '"' . selected($value, $key, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        break;

      case 'video_source':
        $this->render_video_source_field($field_id, $field_name, $value, $post_id);
        break;

      default:
        echo '<input type="text" id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '" style="width: 100%;">';
        break;
    }

    echo '</div>';
  }

  /**
   * Render event selection field with search and infinite scroll
   */
  private function render_event_selection_field($field_id, $field_name, $value) {
    // Get events (pages with a specific template or meta)
    $events = get_posts(array(
      'post_type' => 'page',
      'posts_per_page' => 20,
      'post_status' => 'publish',
      'meta_query' => array(
        array(
          'key' => '_wp_page_template',
          'value' => 'page-event.php',
          'compare' => 'LIKE'
        )
      )
    ));

    echo '<div class="baum-event-selection-wrapper">';
    echo '<select id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" style="width: 100%;" class="baum-event-select">';
    echo '<option value="">' . __('Select an Event', 'baumpress') . '</option>';

    foreach ($events as $event) {
      $selected = selected($value, $event->ID, false);
      echo '<option value="' . esc_attr($event->ID) . '"' . $selected . '>' . esc_html($event->post_title) . '</option>';
    }

    echo '</select>';
    echo '<div class="baum-event-search" style="margin-top: 10px;">';
    echo '<input type="text" placeholder="' . esc_attr__('Search events...', 'baumpress') . '" class="baum-event-search-input" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">';
    echo '<div class="baum-event-results" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; margin-top: 5px; display: none;"></div>';
    echo '</div>';
    echo '</div>';

    // Add JavaScript for search functionality
    echo '<script>
      jQuery(document).ready(function($) {
        var searchTimeout;
        $(".baum-event-search-input").on("input", function() {
          var query = $(this).val();
          var resultsContainer = $(".baum-event-results");

          clearTimeout(searchTimeout);

          if (query.length < 2) {
            resultsContainer.hide();
            return;
          }

          searchTimeout = setTimeout(function() {
            $.ajax({
              url: ajaxurl,
              type: "POST",
              data: {
                action: "baum_search_events",
                query: query,
                nonce: "' . wp_create_nonce('baum_search_events_nonce') . '"
              },
              success: function(response) {
                if (response.success) {
                  resultsContainer.html(response.data.html).show();
                }
              }
            });
          }, 300);
        });

        $(document).on("click", ".baum-event-result", function() {
          var eventId = $(this).data("event-id");
          var eventTitle = $(this).data("event-title");

          $("#' . esc_js($field_id) . '").val(eventId);
          $(".baum-event-search-input").val(eventTitle);
          $(".baum-event-results").hide();
        });
      });
    </script>';
  }

  /**
   * Render video source field with tabbed interface
   */
  private function render_video_source_field($field_id, $field_name, $value, $post_id) {
    $current_tab = 'upload'; // Default tab

    echo '<div class="baum-video-source-wrapper" style="display: flex; border: 1px solid #ddd; border-radius: 4px; min-height: 300px;">';

    // Left sidebar with tabs
    echo '<div class="baum-video-tabs" style="width: 150px; background: #f8f9fa; border-right: 1px solid #ddd; padding: 10px 0;">';

    $tabs = array(
      'upload' => array('label' => __('Upload', 'baumpress'), 'icon' => 'fas fa-upload'),
      'youtube' => array('label' => __('YouTube', 'baumpress'), 'icon' => 'fab fa-youtube'),
      'vimeo' => array('label' => __('Vimeo', 'baumpress'), 'icon' => 'fab fa-vimeo'),
      'dailymotion' => array('label' => __('Dailymotion', 'baumpress'), 'icon' => 'fas fa-video'),
      'twitch' => array('label' => __('Twitch', 'baumpress'), 'icon' => 'fab fa-twitch')
    );

    foreach ($tabs as $tab_key => $tab_data) {
      $active_class = $tab_key === $current_tab ? 'active' : '';
      echo '<div class="baum-video-tab ' . $active_class . '" data-tab="' . esc_attr($tab_key) . '" style="padding: 10px 15px; cursor: pointer; border-radius: 4px; margin-bottom: 5px; ' . ($tab_key === $current_tab ? 'background: #007cba; color: white;' : 'background: transparent; color: #333;') . '">';
      echo '<i class="' . esc_attr($tab_data['icon']) . '" style="margin-right: 8px;"></i>';
      echo esc_html($tab_data['label']);
      echo '</div>';
    }

    echo '</div>';

    // Right content area
    echo '<div class="baum-video-content" style="flex: 1; padding: 20px;">';

    // Upload tab content
    echo '<div class="baum-video-tab-content" data-tab="upload" style="' . ($current_tab === 'upload' ? '' : 'display: none;') . '">';
    echo '<h4>' . __('Upload Video', 'baumpress') . '</h4>';
    echo '<div class="baum-video-upload-area" style="border: 2px dashed #ddd; border-radius: 4px; padding: 40px; text-align: center; background: #fafafa;">';
    echo '<i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #ccc; margin-bottom: 15px;"></i>';
    echo '<p>' . __('Drag and drop a video file here, or click to select', 'baumpress') . '</p>';
    echo '<input type="file" accept="video/*" style="display: none;" class="baum-video-file-input">';
    echo '<button type="button" class="button" onclick="jQuery(\'.baum-video-file-input\').click()">' . __('Select Video File', 'baumpress') . '</button>';
    echo '</div>';
    echo '</div>';

    // YouTube tab content
    echo '<div class="baum-video-tab-content" data-tab="youtube" style="' . ($current_tab === 'youtube' ? '' : 'display: none;') . '">';
    echo '<h4>' . __('YouTube Videos', 'baumpress') . '</h4>';
    echo '<input type="text" placeholder="' . esc_attr__('Search YouTube videos...', 'baumpress') . '" class="baum-youtube-search" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 15px;">';
    echo '<div class="baum-youtube-results" style="max-height: 200px; overflow-y: auto;"></div>';
    echo '</div>';

    // Other platform tabs
    foreach (array('vimeo', 'dailymotion', 'twitch') as $platform) {
      echo '<div class="baum-video-tab-content" data-tab="' . esc_attr($platform) . '" style="' . ($current_tab === $platform ? '' : 'display: none;') . '">';
      echo '<h4>' . esc_html(ucfirst($platform)) . ' ' . __('Videos', 'baumpress') . '</h4>';
      echo '<input type="text" placeholder="' . sprintf(esc_attr__('Search %s videos...', 'baumpress'), ucfirst($platform)) . '" class="baum-' . esc_attr($platform) . '-search" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 15px;">';
      echo '<div class="baum-' . esc_attr($platform) . '-results" style="max-height: 200px; overflow-y: auto;"></div>';
      echo '</div>';
    }

    echo '</div>';
    echo '</div>';

    // Hidden input to store the selected video data
    echo '<input type="hidden" id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '">';

    // Add JavaScript for tab functionality
    echo '<script>
      jQuery(document).ready(function($) {
        $(".baum-video-tab").click(function() {
          var tab = $(this).data("tab");

          // Update tab appearance
          $(".baum-video-tab").removeClass("active").css({
            "background": "transparent",
            "color": "#333"
          });
          $(this).addClass("active").css({
            "background": "#007cba",
            "color": "white"
          });

          // Show/hide content
          $(".baum-video-tab-content").hide();
          $(".baum-video-tab-content[data-tab=\"" + tab + "\"]").show();

          // Focus search input if applicable
          if (tab !== "upload") {
            $(".baum-" + tab + "-search").focus();
          }
        });

        // YouTube search functionality
        var youtubeTimeout;
        $(".baum-youtube-search").on("input", function() {
          var query = $(this).val();
          var resultsContainer = $(".baum-youtube-results");

          clearTimeout(youtubeTimeout);

          if (query.length < 2) {
            resultsContainer.empty();
            return;
          }

          youtubeTimeout = setTimeout(function() {
            $.ajax({
              url: ajaxurl,
              type: "POST",
              data: {
                action: "baum_search_youtube_videos",
                query: query,
                nonce: "' . wp_create_nonce('baum_youtube_search_nonce') . '"
              },
              success: function(response) {
                if (response.success) {
                  resultsContainer.html(response.data.html);
                }
              }
            });
          }, 500);
        });
      });
    </script>';
  }

  /**
   * Save activity meta data
   */
  public function save_activity_meta($post_id) {
    // Check if this is an activity post
    if (get_post_type($post_id) !== 'activity') {
      return;
    }

    // Skip autosaves and revisions
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
      return;
    }

    // Check user permissions
    if (!current_user_can('edit_post', $post_id)) {
      return;
    }

    // Verify nonce for content type meta box
    if (isset($_POST['activity_content_type_meta_box_nonce']) &&
        wp_verify_nonce($_POST['activity_content_type_meta_box_nonce'], 'activity_content_type_meta_box')) {

      // Save content type
      if (isset($_POST['activity_content_type'])) {
        $content_type = sanitize_text_field($_POST['activity_content_type']);
        update_post_meta($post_id, '_activity_content_type', $content_type);

        // Set taxonomy term
        wp_set_object_terms($post_id, $content_type, 'activity_content_type');
      }
    }

    // Verify nonce for content fields meta box
    if (isset($_POST['activity_content_fields_meta_box_nonce']) &&
        wp_verify_nonce($_POST['activity_content_fields_meta_box_nonce'], 'activity_content_fields_meta_box')) {

      // Get current content type to determine which fields to save
      $content_type = get_post_meta($post_id, '_activity_content_type', true) ?: 'post';
      $type_data = self::get_content_type($content_type);

      if ($type_data && !empty($type_data['fields'])) {
        foreach ($type_data['fields'] as $field) {
          $field_name = 'activity_' . $field;

          if (isset($_POST[$field_name])) {
            $value = $_POST[$field_name];

            // Sanitize based on field type
            switch ($field) {
              case 'content':
              case 'quote_text':
              case 'link_description':
              case 'transcript':
                $value = wp_kses_post($value);
                break;

              case 'link_url':
              case 'video_url':
              case 'audio_url':
              case 'stream_url':
                $value = esc_url_raw($value);
                break;

              case 'tags':
                $value = sanitize_text_field($value);
                break;

              case 'poll_options':
                if (is_array($value)) {
                  $value = array_map('sanitize_text_field', $value);
                } else {
                  $value = array_map('sanitize_text_field', explode("\n", $value));
                }
                break;

              case 'multiple_choice':
              case 'rsvp':
                $value = $value ? '1' : '0';
                break;

              case 'expiry_date':
              case 'event_date':
              case 'poll_end_date':
                $value = sanitize_text_field($value);
                // Validate date format
                if ($value && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
                  $value = '';
                }
                break;

              case 'event_time':
                $value = sanitize_text_field($value);
                // Validate time format
                if ($value && !preg_match('/^\d{2}:\d{2}$/', $value)) {
                  $value = '';
                }
                break;

              case 'event_selection':
                $value = intval($value); // Event ID should be an integer
                break;

              case 'rsvp_status':
                $allowed_statuses = array('going', 'maybe', 'not_going');
                $value = in_array($value, $allowed_statuses) ? $value : '';
                break;

              case 'video_source':
                // Video source can be JSON data containing platform, video ID, etc.
                if (is_string($value)) {
                  $decoded = json_decode($value, true);
                  if (json_last_error() === JSON_ERROR_NONE) {
                    // Sanitize the decoded data
                    $sanitized = array();
                    if (isset($decoded['platform'])) {
                      $sanitized['platform'] = sanitize_text_field($decoded['platform']);
                    }
                    if (isset($decoded['video_id'])) {
                      $sanitized['video_id'] = sanitize_text_field($decoded['video_id']);
                    }
                    if (isset($decoded['title'])) {
                      $sanitized['title'] = sanitize_text_field($decoded['title']);
                    }
                    if (isset($decoded['thumbnail'])) {
                      $sanitized['thumbnail'] = esc_url_raw($decoded['thumbnail']);
                    }
                    $value = json_encode($sanitized);
                  } else {
                    $value = sanitize_text_field($value);
                  }
                }
                break;

              default:
                $value = sanitize_text_field($value);
                break;
            }

            update_post_meta($post_id, '_activity_' . $field, $value);
          }
        }
      }
    }
  }

  /**
   * Get available content types
   * 
   * @return array Content types with metadata
   */
  public static function get_content_types() {
    return apply_filters('baum_activity_content_types', self::CONTENT_TYPES);
  }

  /**
   * Get content type data
   * 
   * @param string $type Content type key
   * @return array|false Content type data or false if not found
   */
  public static function get_content_type($type) {
    $types = self::get_content_types();
    return isset($types[$type]) ? $types[$type] : false;
  }

  /**
   * Render content type selector
   * 
   * @param string $selected Currently selected type
   * @param array $args Additional arguments
   * @return string HTML output
   */
  public static function render_content_type_selector($selected = 'post', $args = array()) {
    $defaults = array(
      'name' => 'activity_content_type',
      'id' => 'activity-content-type-selector',
      'class' => 'baum-content-type-selector',
      'show_descriptions' => true,
      'layout' => 'grid', // 'grid', 'list', 'dropdown'
      'columns' => 4
    );
    
    $args = wp_parse_args($args, $defaults);
    $types = self::get_content_types();
    
    ob_start();
    ?>
    <div class="<?php echo esc_attr($args['class']); ?>" data-layout="<?php echo esc_attr($args['layout']); ?>">
      <?php if ($args['layout'] === 'grid'): ?>
        <div class="baum-content-type-grid" style="display: grid; grid-template-columns: repeat(<?php echo intval($args['columns']); ?>, 1fr); gap: 10px;">
          <?php foreach ($types as $key => $type): ?>
            <div class="baum-content-type-option <?php echo $selected === $key ? 'selected' : ''; ?>"
                 data-type="<?php echo esc_attr($key); ?>"
                 style="border: 2px solid <?php echo $selected === $key ? $type['color'] : '#ddd'; ?>; border-radius: 6px; padding: 10px; cursor: pointer; text-align: center; transition: all 0.3s ease; background: #fff;">

              <input type="radio"
                     name="<?php echo esc_attr($args['name']); ?>"
                     value="<?php echo esc_attr($key); ?>"
                     id="content-type-<?php echo esc_attr($key); ?>"
                     <?php checked($selected, $key); ?>
                     style="display: none;">

              <div class="baum-content-type-icon" style="font-size: 18px; color: <?php echo esc_attr($type['color']); ?>; margin-bottom: 6px;">
                <i class="<?php echo esc_attr($type['icon']); ?>"></i>
              </div>

              <div class="baum-content-type-label" style="font-weight: 600; margin-bottom: 4px; color: #333; font-size: 12px;">
                <?php echo esc_html($type['label']); ?>
              </div>

              <?php if ($args['show_descriptions']): ?>
                <div class="baum-content-type-description" style="font-size: 10px; color: #666; line-height: 1.2;">
                  <?php echo esc_html($type['description']); ?>
                </div>
              <?php endif; ?>
            </div>
          <?php endforeach; ?>
        </div>
        
      <?php elseif ($args['layout'] === 'dropdown'): ?>
        <select name="<?php echo esc_attr($args['name']); ?>" id="<?php echo esc_attr($args['id']); ?>" class="baum-content-type-dropdown">
          <?php foreach ($types as $key => $type): ?>
            <option value="<?php echo esc_attr($key); ?>" <?php selected($selected, $key); ?>>
              <?php echo esc_html($type['label']); ?>
            </option>
          <?php endforeach; ?>
        </select>
        
      <?php else: // list layout ?>
        <div class="baum-content-type-list">
          <?php foreach ($types as $key => $type): ?>
            <label class="baum-content-type-option <?php echo $selected === $key ? 'selected' : ''; ?>" 
                   style="display: flex; align-items: center; padding: 10px; border: 1px solid #ddd; border-radius: 5px; margin-bottom: 8px; cursor: pointer;">
              
              <input type="radio" 
                     name="<?php echo esc_attr($args['name']); ?>" 
                     value="<?php echo esc_attr($key); ?>" 
                     <?php checked($selected, $key); ?>
                     style="margin-right: 10px;">
              
              <div class="baum-content-type-icon" style="font-size: 18px; color: <?php echo esc_attr($type['color']); ?>; margin-right: 10px;">
                <i class="<?php echo esc_attr($type['icon']); ?>"></i>
              </div>
              
              <div class="baum-content-type-info">
                <div class="baum-content-type-label" style="font-weight: 600;">
                  <?php echo esc_html($type['label']); ?>
                </div>
                <?php if ($args['show_descriptions']): ?>
                  <div class="baum-content-type-description" style="font-size: 12px; color: #666;">
                    <?php echo esc_html($type['description']); ?>
                  </div>
                <?php endif; ?>
              </div>
            </label>
          <?php endforeach; ?>
        </div>
      <?php endif; ?>
    </div>
    
    <style>
      .baum-content-type-option {
        background: #fff !important;
      }

      .baum-content-type-option:hover {
        border-color: #007cba !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        background: #f8f9fa !important;
      }

      .baum-content-type-option.selected {
        background: #f0f8ff !important;
      }

      .baum-content-type-dropdown {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
      }
    </style>
    <?php
    
    return ob_get_clean();
  }

  /**
   * Enqueue frontend scripts
   */
  public function enqueue_scripts() {
    wp_enqueue_script(
      'baum-activity-content-types',
      get_template_directory_uri() . '/assets/js/activity-content-types.js',
      array('jquery'),
      '1.0.0',
      true
    );
    
    wp_localize_script('baum-activity-content-types', 'baumActivityTypes', array(
      'ajaxUrl' => admin_url('admin-ajax.php'),
      'nonce' => wp_create_nonce('baum_activity_types_nonce'),
      'contentTypes' => self::get_content_types()
    ));
  }

  /**
   * Enqueue admin scripts
   */
  public function enqueue_admin_scripts($hook) {
    global $post_type;
    
    if ($post_type === 'activity') {
      wp_enqueue_script(
        'baum-activity-content-types-admin',
        BAUM_GROUPS_PLUGIN_URL . '/assets/js/activity-content-types-admin.js',
        array('jquery'),
        '1.0.0',
        true
      );
      
      wp_localize_script('baum-activity-content-types-admin', 'baumActivityTypesAdmin', array(
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('baum_activity_types_nonce'),
        'contentTypes' => self::get_content_types()
      ));
    }
  }

  /**
   * AJAX handler for getting content type fields
   */
  public function ajax_get_content_type_fields() {
    check_ajax_referer('baum_activity_types_nonce', 'nonce');

    $type = sanitize_text_field($_POST['type']);
    $post_id = intval($_POST['post_id']) ?: 0;

    $type_data = self::get_content_type($type);

    if (!$type_data) {
      wp_send_json_error(array('message' => __('Invalid content type', 'baumpress')));
    }

    ob_start();

    if (!empty($type_data['fields'])) {
      echo '<p><strong>' . sprintf(__('Fields for %s:', 'baumpress'), $type_data['label']) . '</strong></p>';

      foreach ($type_data['fields'] as $field) {
        $field_value = get_post_meta($post_id, '_activity_' . $field, true);
        $this->render_field($field, $field_value, $post_id);
      }
    } else {
      echo '<p>' . __('No additional fields for this content type.', 'baumpress') . '</p>';
    }

    $html = ob_get_clean();

    wp_send_json_success(array(
      'html' => $html,
      'type_data' => $type_data
    ));
  }

  /**
   * AJAX handler for searching events
   */
  public function ajax_search_events() {
    check_ajax_referer('baum_search_events_nonce', 'nonce');

    $query = sanitize_text_field($_POST['query']);

    if (strlen($query) < 2) {
      wp_send_json_error(array('message' => __('Query too short', 'baumpress')));
    }

    $events = get_posts(array(
      'post_type' => 'page',
      'posts_per_page' => 10,
      'post_status' => 'publish',
      's' => $query,
      'meta_query' => array(
        array(
          'key' => '_wp_page_template',
          'value' => 'page-event.php',
          'compare' => 'LIKE'
        )
      )
    ));

    ob_start();

    if (!empty($events)) {
      foreach ($events as $event) {
        echo '<div class="baum-event-result" data-event-id="' . esc_attr($event->ID) . '" data-event-title="' . esc_attr($event->post_title) . '" style="padding: 10px; border-bottom: 1px solid #eee; cursor: pointer;">';
        echo '<strong>' . esc_html($event->post_title) . '</strong>';
        if ($event->post_excerpt) {
          echo '<br><small>' . esc_html($event->post_excerpt) . '</small>';
        }
        echo '</div>';
      }
    } else {
      echo '<div style="padding: 10px; text-align: center; color: #666;">' . __('No events found', 'baumpress') . '</div>';
    }

    $html = ob_get_clean();

    wp_send_json_success(array('html' => $html));
  }

  /**
   * AJAX handler for searching YouTube videos
   */
  public function ajax_search_youtube_videos() {
    check_ajax_referer('baum_youtube_search_nonce', 'nonce');

    $query = sanitize_text_field($_POST['query']);

    if (strlen($query) < 2) {
      wp_send_json_error(array('message' => __('Query too short', 'baumpress')));
    }

    // Get YouTube API key from options or constants
    $api_key = defined('YOUTUBE_API_KEY') ? YOUTUBE_API_KEY : get_option('baum_youtube_api_key');

    if (!$api_key) {
      wp_send_json_error(array('message' => __('YouTube API key not configured', 'baumpress')));
    }

    $api_url = 'https://www.googleapis.com/youtube/v3/search?' . http_build_query(array(
      'part' => 'snippet',
      'q' => $query,
      'type' => 'video',
      'maxResults' => 10,
      'key' => $api_key
    ));

    $response = wp_remote_get($api_url);

    if (is_wp_error($response)) {
      wp_send_json_error(array('message' => __('Failed to search YouTube', 'baumpress')));
    }

    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);

    ob_start();

    if (!empty($data['items'])) {
      foreach ($data['items'] as $video) {
        $video_id = $video['id']['videoId'];
        $title = $video['snippet']['title'];
        $thumbnail = $video['snippet']['thumbnails']['default']['url'];
        $channel = $video['snippet']['channelTitle'];

        echo '<div class="baum-youtube-result" data-video-id="' . esc_attr($video_id) . '" data-video-title="' . esc_attr($title) . '" style="display: flex; padding: 10px; border-bottom: 1px solid #eee; cursor: pointer;">';
        echo '<img src="' . esc_url($thumbnail) . '" style="width: 60px; height: 45px; margin-right: 10px; border-radius: 4px;">';
        echo '<div>';
        echo '<strong style="display: block; margin-bottom: 4px;">' . esc_html($title) . '</strong>';
        echo '<small style="color: #666;">' . esc_html($channel) . '</small>';
        echo '</div>';
        echo '</div>';
      }
    } else {
      echo '<div style="padding: 10px; text-align: center; color: #666;">' . __('No videos found', 'baumpress') . '</div>';
    }

    $html = ob_get_clean();

    wp_send_json_success(array('html' => $html));
  }
}

// Initialize the class
new Baum_Activity_Content_Types();
