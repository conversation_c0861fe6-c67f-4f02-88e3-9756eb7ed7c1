<?php
/**
 * Reusable Activity Content Template Part
 * Uses BaumPress theme comment structure for consistency
 *
 * Expected variables:
 * $activity_post - The activity post object
 * $show_context - Whether to show group/user context (default: true)
 * $show_actions - Whether to show like/comment actions (default: true)
 * $compact_mode - Whether to use compact display (default: false)
 */

if (!isset($activity_post) || !$activity_post) {
  return;
}

// Set defaults
$show_context = isset($show_context) ? $show_context : true;
$show_actions = isset($show_actions) ? $show_actions : true;
$compact_mode = isset($compact_mode) ? $compact_mode : false;

// Get activity metadata
$activity_id = $activity_post->ID;
$activity_type = get_post_meta($activity_id, '_baum_activity_type', true) ?: 'post';
$group_id = get_post_meta($activity_id, '_baum_group_id', true);
$target_user_id = get_post_meta($activity_id, '_baum_target_user', true);
$visibility = get_post_meta($activity_id, '_baum_visibility', true) ?: 'public';
$media_url = get_post_meta($activity_id, '_activity_media_url', true);
$media_type = get_post_meta($activity_id, '_activity_media_type', true);

// Get author info
$author_id = $activity_post->post_author;
$author_name = get_the_author_meta('display_name', $author_id);
$author_avatar = get_avatar($author_id, 48); // Use WordPress get_avatar for consistency

// Get group info if this is a group post
$group_name = '';
$group_url = '';
if ($group_id && $show_context) {
  $group_name = get_the_title($group_id);
  $group_url = get_permalink($group_id);
}

// Get activity URL
$activity_url = get_permalink($activity_id);

// Get like count and user like status (if available)
$like_count = 0;
$user_liked = false;
if (class_exists('Baum_Activity')) {
  $activity_class = new Baum_Activity();
  $like_count = $activity_class->get_activity_like_count($activity_id);
  if (is_user_logged_in()) {
    $user_liked = $activity_class->has_user_liked_activity($activity_id, get_current_user_id());
  }
}

// Get comment count
$comment_count = get_comments_number($activity_id);
?>

<!-- Using BaumPress comment structure -->
<div class="comment baum-activity-item" id="activity-<?php echo esc_attr($activity_id); ?>" data-activity-id="<?php echo esc_attr($activity_id); ?>">

  <!-- Avatar (BaumPress style) -->
  <div class="baum-comment-avatar">
    <?php echo $author_avatar; ?>
  </div>

  <!-- Comment Body (BaumPress style) -->
  <div class="baum-comment-body">
    <div class="baum-title-width">

      <!-- Activity Header with Author and Date -->
      <h5>
        <a href="<?php echo esc_url(get_author_posts_url($author_id)); ?>">
          <?php echo esc_html($author_name); ?>
        </a>

        <?php if ($group_id && $show_context): ?>
          <span class="baum-activity-context">
            <?php _e('in', 'baum-groups'); ?>
            <a href="<?php echo esc_url($group_url); ?>" class="baum-group-link"><?php echo esc_html($group_name); ?></a>
          </span>
        <?php endif; ?>

        <span class="baum-activity-date">
          <a href="<?php echo esc_url($activity_url); ?>" class="baum-activity-permalink">
            <?php echo human_time_diff(get_the_time('U', $activity_id), current_time('timestamp')) . ' ' . __('ago', 'baum-groups'); ?>
          </a>
        </span>

        <!-- Activity Type Badge -->
        <?php if ($activity_type !== 'post'): ?>
          <span class="baum-activity-type-badge baum-activity-type-<?php echo esc_attr($activity_type); ?>">
            <?php
            switch ($activity_type) {
              case 'announcement':
                echo '<i class="fas fa-bullhorn"></i> ' . __('Announcement', 'baum-groups');
                break;
              case 'question':
                echo '<i class="fas fa-question-circle"></i> ' . __('Question', 'baum-groups');
                break;
              case 'poll':
                echo '<i class="fas fa-poll"></i> ' . __('Poll', 'baum-groups');
                break;
              case 'attending':
                echo '<i class="fas fa-calendar-check"></i> ' . __('Attending / RSVP', 'baum-groups');
                break;
              default:
                echo '<i class="fas fa-file-text"></i> ' . ucfirst($activity_type);
            }
            ?>
          </span>
        <?php endif; ?>
      </h5>

      <!-- Activity Content -->
      <div class="baum-activity-content">
        <?php if ($compact_mode): ?>
          <p><?php echo wp_trim_words($activity_post->post_content, 20, '...'); ?></p>
        <?php else: ?>
          <?php echo wpautop($activity_post->post_content); ?>
        <?php endif; ?>
      </div>

      <!-- Activity Media -->
      <?php if ($media_url): ?>
        <div class="baum-activity-media">
          <?php if (strpos($media_type, 'image/') === 0): ?>
            <img src="<?php echo esc_url($media_url); ?>" alt="<?php echo esc_attr($activity_post->post_title); ?>" class="baum-activity-image">
          <?php elseif (strpos($media_type, 'video/') === 0): ?>
            <video src="<?php echo esc_url($media_url); ?>" controls class="baum-activity-video"></video>
          <?php endif; ?>
        </div>
      <?php endif; ?>

      <!-- Activity Actions -->
      <?php if ($show_actions): ?>
        <div class="baum-activity-actions">
          <a href="#" class="baum-activity-action baum-activity-like" data-activity-id="<?php echo esc_attr($activity_id); ?>">
            <i class="<?php echo $user_liked ? 'fas' : 'far'; ?> fa-heart"></i>
            <?php printf(_n('%d Like', '%d Likes', $like_count, 'baum-groups'), $like_count); ?>
          </a>

          <a href="<?php echo esc_url($activity_url); ?>#comments" class="baum-activity-action baum-activity-comment">
            <i class="fas fa-comment"></i>
            <?php printf(_n('%d Comment', '%d Comments', $comment_count, 'baum-groups'), $comment_count); ?>
          </a>

          <a href="#" class="baum-activity-action baum-activity-share" data-activity-id="<?php echo esc_attr($activity_id); ?>" data-url="<?php echo esc_url($activity_url); ?>">
            <i class="fas fa-share"></i>
            <?php _e('Share', 'baum-groups'); ?>
          </a>
        </div>
      <?php endif; ?>

      <!-- Activity Menu (for author/admins) -->
      <?php if (is_user_logged_in() && (get_current_user_id() == $author_id || current_user_can('manage_groups'))): ?>
        <div class="baum-activity-dropdown">
          <button type="button" class="baum-activity-menu-trigger" data-activity-id="<?php echo esc_attr($activity_id); ?>" title="<?php _e('More options', 'baum-groups'); ?>">
            <i class="fas fa-ellipsis-h"></i>
          </button>

          <div class="baum-activity-dropdown-menu" id="activity-menu-<?php echo esc_attr($activity_id); ?>">
            <a href="<?php echo esc_url($activity_url); ?>" class="baum-dropdown-item">
              <i class="fas fa-external-link-alt"></i>
              <?php _e('View Post', 'baum-groups'); ?>
            </a>

            <?php if (get_current_user_id() == $author_id): ?>
              <button type="button" class="baum-dropdown-item baum-activity-edit" data-activity-id="<?php echo esc_attr($activity_id); ?>">
                <i class="fas fa-edit"></i>
                <?php _e('Edit', 'baum-groups'); ?>
              </button>
            <?php endif; ?>

            <button type="button" class="baum-dropdown-item baum-activity-delete danger" data-activity-id="<?php echo esc_attr($activity_id); ?>">
              <i class="fas fa-trash"></i>
              <?php _e('Delete', 'baum-groups'); ?>
            </button>
          </div>
        </div>
      <?php endif; ?>

    </div>
  </div>

</div>
