<?php
/**
 * Update Card (DRY partial)
 *
 * Renders a single user update/activity in the BaumPress comment-style card.
 * Falls back to the existing activity-content.php variables to ease adoption.
 *
 * Expected context variables:
 * - $activity_post   WP_Post  The post to render (user_update or activity)
 * - $show_context    bool     Show context (group link) if available
 * - $show_actions    bool     Show like/comment/share actions
 * - $compact_mode    bool     If true, trims content
 *
 * @package BaumGroups
 */

if (!defined('ABSPATH')) { exit; }

if (!isset($activity_post) || !$activity_post instanceof WP_Post) {
  $activity_post = get_post();
}

$show_context = isset($show_context) ? (bool) $show_context : true;
$show_actions = isset($show_actions) ? (bool) $show_actions : true;
$compact_mode = isset($compact_mode) ? (bool) $compact_mode : false;

$activity_id   = $activity_post->ID;
$post_type     = get_post_type($activity_id);

// Normalize meta keys (support legacy and new)
$activity_type = get_post_meta($activity_id, '_activity_content_type', true)
  ?: get_post_meta($activity_id, '_baum_activity_type', true)
  ?: 'post';
$group_id      = get_post_meta($activity_id, '_baum_group_id', true);
$target_user   = get_post_meta($activity_id, '_baum_target_user', true);
$media_url     = get_post_meta($activity_id, '_activity_media_url', true);
$media_type    = get_post_meta($activity_id, '_activity_media_type', true);

$author_id   = (int) $activity_post->post_author;
$author_name = get_the_author_meta('display_name', $author_id);
$author_ava  = get_avatar($author_id, 48);

$group_name = '';
$group_url  = '';
if ($group_id && $show_context) {
  $group_name = get_the_title($group_id);
  $group_url  = get_permalink($group_id);
}

$activity_url = get_permalink($activity_id);

// Optional like counts if class exists (kept simple)
$like_count = 0;
if (class_exists('Baum_Activity')) {
  $act = new Baum_Activity();
  if (method_exists($act, 'get_activity_like_count')) {
    $like_count = (int) $act->get_activity_like_count($activity_id);
  }
}
$comment_count = get_comments_number($activity_id);

?>
<div class="comment baum-activity-item" id="activity-<?php echo esc_attr($activity_id); ?>" data-activity-id="<?php echo esc_attr($activity_id); ?>">
  <div class="baum-comment-avatar"><?php echo $author_ava; ?></div>
  <div class="baum-comment-body">
    <div class="baum-title-width">
      <h5>
        <a href="<?php echo esc_url(get_author_posts_url($author_id)); ?>"><?php echo esc_html($author_name); ?></a>
        <?php if ($group_id && $show_context): ?>
          <span class="baum-activity-context">
            <?php _e('in', 'baum-groups'); ?>
            <a href="<?php echo esc_url($group_url); ?>" class="baum-group-link"><?php echo esc_html($group_name); ?></a>
          </span>
        <?php endif; ?>
        <span class="baum-activity-date">
          <a href="<?php echo esc_url($activity_url); ?>" class="baum-activity-permalink">
            <?php echo human_time_diff(get_post_time('U', true, $activity_post), current_time('timestamp')) . ' ' . __('ago', 'baum-groups'); ?>
          </a>
        </span>
        <?php if ($activity_type !== 'post'): ?>
          <span class="baum-activity-type-badge baum-activity-type-<?php echo esc_attr($activity_type); ?>">
            <?php echo esc_html(ucfirst($activity_type)); ?>
          </span>
        <?php endif; ?>
      </h5>

      <div class="baum-activity-content">
        <?php if ($compact_mode): ?>
          <p><?php echo esc_html(wp_trim_words(wp_strip_all_tags($activity_post->post_content), 20, '...')); ?></p>
        <?php else: ?>
          <?php echo wp_kses_post(wpautop($activity_post->post_content)); ?>
        <?php endif; ?>
      </div>

      <?php if ($media_url): ?>
        <div class="baum-activity-media">
          <?php if (strpos($media_type, 'image/') === 0): ?>
            <img src="<?php echo esc_url($media_url); ?>" alt="" class="baum-activity-image" />
          <?php elseif (strpos($media_type, 'video/') === 0): ?>
            <video src="<?php echo esc_url($media_url); ?>" controls class="baum-activity-video"></video>
          <?php endif; ?>
        </div>
      <?php endif; ?>

      <?php if ($show_actions): ?>
        <div class="baum-activity-actions">
          <a href="#" class="baum-activity-action baum-activity-like" data-activity-id="<?php echo esc_attr($activity_id); ?>">
            <i class="far fa-heart"></i>
            <?php printf(_n('%d Like', '%d Likes', $like_count, 'baum-groups'), $like_count); ?>
          </a>
          <a href="<?php echo esc_url($activity_url); ?>#comments" class="baum-activity-action baum-activity-comment">
            <i class="fas fa-comment"></i>
            <?php printf(_n('%d Comment', '%d Comments', $comment_count, 'baum-groups'), $comment_count); ?>
          </a>
          <a href="#" class="baum-activity-action baum-activity-share" data-activity-id="<?php echo esc_attr($activity_id); ?>" data-url="<?php echo esc_url($activity_url); ?>">
            <i class="fas fa-share"></i>
            <?php _e('Share', 'baum-groups'); ?>
          </a>
        </div>
      <?php endif; ?>

    </div>
  </div>
</div>

