#solar-system {
  margin: 0px 10px; 
}

.tooltip-planet {
  pointer-events: auto; /* Ensure tooltips work */
  cursor: help;
}

/* .baum-lunar-cycle {
  max-width: 370px;
  margin: 0px 5px;
  margin-top: 10px;
  text-align: center;
  padding: 15px;
  border-radius: 10px;
  background: var(--color-octonary, #f4f4f4);
  display: grid;
  grid-template-columns: 84px auto;
  text-align: left;
  background: var(--color-title-bg);
  border-radius: var(--border-radius);
  color: var(--color-black);
  border: var(--card-border);
  outline: var(--card-outline); 
  letter-spacing: -0.5px;
}  */

.baum-lunar-cycle {
  /* max-width: 355px; */
  margin: 0px 5px;
  /* float: right; */
  width: 100%;
  margin-top: 5px;
  /* margin-left: 10px; */
  /* margin-right: 10px; */
  text-align: center;
  padding: 15px;
  /* border-radius: 10px; */
  /* background: var(--color-octonary, #f4f4f4); */
  /* box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); */
  display: inline-grid;
  grid-template-columns: 78px auto;
  text-align: left;
  /* background: var(--color-title-bg); */
  /* display: inline-flex; */
  border-radius: var(--border-radius);
  color: var(--color-black);
  /* border: var(--card-border); */
  /* outline: var(--card-outline); */
  /* letter-spacing: 0.5px; */
  letter-spacing: -0.5px;
}

.grid-2 { 
  display: grid; 
  grid-template-columns: 1fr 1fr; 
  grid-auto-rows: max-content; 
  grid-column-gap: 5px; 
} 

/* The moon image */
.moon-visual img {
  width: 64px; 
  height: 64px; 
  transition: transform 0.3s ease; 
  background: none; 
  /* border-radius: 50%; */
  border-radius: 50% !important;
  background: var(--color-white, #fff);
}

.moon-visual img:hover {
   /* Slight zoom on hover */
  transform: scale(1.05);
}

/* .moon-info {
  margin-top: 15px; 
} */

.moon-info span {
  font-size: 14px; /* Keep the font size small and clean */
  color: #333; /* Dark gray text color */
  margin: 5px 0;
  /* display: block; */
  /* clear: both; */
}

.moon-info .illumination {
  /* display: block; */
  /* clear: both; */
}

/* Phase description styling */
.moon-info #phase {
  font-weight: bold;
  font-size: 16px;
  color: #000; /* Make the phase description stand out */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .baum-lunar-cycle {
      max-width: 100%; /* Full width on smaller devices */
      padding: 15px;
  }
}

.moon-post-link {
  margin-top: 10px;
  text-align: center;
}

.moon-post-link a {
  color: #0073aa;
  text-decoration: none;
}

.moon-post-link a:hover {
  text-decoration: underline;
}

.white-bg {
  background: #fff; 
}