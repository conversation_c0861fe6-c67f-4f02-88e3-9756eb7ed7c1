<?php

function baum_bookmarks_customize_register($wp_customize) {
    $wp_customize->add_setting('baum_bookmark_icon', ['default' => 'bookmark']);
    $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_bookmark_icon', [
        'label'    => 'Choose the icon for the bookmarks',
        'section'  => 'baum_theme_style',
        'settings' => 'baum_bookmark_icon',
        'type'     => 'radio',
        'choices'  => [
            'bookmark'  => 'Bookmark icon',
            'gem'       => 'Gem icon',
            'heart'     => 'Heart icon',
            'fire'      => 'Fire icon',
            'star'      => 'Star icon',
            'thumbs-up' => 'Thumbs-up icon',
        ]
    ]));
}

add_action('customize_register', 'baum_bookmarks_customize_register');
