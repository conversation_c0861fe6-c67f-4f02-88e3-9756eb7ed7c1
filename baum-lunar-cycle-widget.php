<?php 

class Baum_Lunar_Cycle_Widget extends WP_Widget {

  public function __construct() {
      parent::__construct(
          'baum_lunar_cycle_widget',
          '<PERSON>um’s Lunar Cycle',
          array('description' => 'Displays multiple moon phases with selectable dates or posts.')
      );
  }

  // Output the widget content
  public function widget($args, $instance) {
      echo $args['before_widget'];

      // Get the widget title
      $title = !empty($instance['title']) ? $instance['title'] : 'Current Moon Phases';
      echo $args['before_title'] . apply_filters('widget_title', $title) . $args['after_title'];

      // Get all the dates and posts stored as serialized data
      $moon_phases = !empty($instance['moon_phases']) ? unserialize($instance['moon_phases']) : array();

      foreach ($moon_phases as $moon) {
          $date = isset($moon['date']) ? $moon['date'] : current_time('Y-m-d');
          $post_id = isset($moon['post_id']) ? $moon['post_id'] : false;

          if ($post_id) {
              // If a post is selected, use the post publish date
              $post = get_post($post_id);
              $date = get_the_date('Y-m-d', $post_id);
              echo do_shortcode('[baum_lunar_cycle date="' . esc_attr($date) . '"]');
              echo '<div class="moon-post-link"><a href="' . get_permalink($post->ID) . '">' . esc_html($post->post_title) . '</a></div>';
          } else {
              // Output the moon phase using the selected date
              echo do_shortcode('[baum_lunar_cycle date="' . esc_attr($date) . '"]');
          }
      }

      echo $args['after_widget'];
  }

  // Admin form to add multiple moons
  public function form($instance) {
      $title = !empty($instance['title']) ? $instance['title'] : 'Current Moon Phases';
      $moon_phases = !empty($instance['moon_phases']) ? unserialize($instance['moon_phases']) : array();

      ?>
      <p>
          <label for="<?php echo esc_attr($this->get_field_id('title')); ?>">Title:</label>
          <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" 
                 name="<?php echo esc_attr($this->get_field_name('title')); ?>" 
                 type="text" value="<?php echo esc_attr($title); ?>">
      </p>
      
      <div class="moon-phases-wrapper">
          <?php foreach ($moon_phases as $index => $moon) { ?>
              <div class="moon-phase-group">
                  <h4>Moon Phase <?php echo $index + 1; ?></h4>
                  <p>
                      <label for="<?php echo esc_attr($this->get_field_id('moon_phases') . '[' . $index . '][date]'); ?>">Date:</label>
                      <input class="widefat" id="<?php echo esc_attr($this->get_field_id('moon_phases') . '[' . $index . '][date]'); ?>" 
                             name="<?php echo esc_attr($this->get_field_name('moon_phases') . '[' . $index . '][date]'); ?>" 
                             type="date" value="<?php echo esc_attr($moon['date']); ?>">
                  </p>
                  <p>
                      <label for="<?php echo esc_attr($this->get_field_id('moon_phases') . '[' . $index . '][post_id]'); ?>">Post:</label>
                      <select class="widefat" id="<?php echo esc_attr($this->get_field_id('moon_phases') . '[' . $index . '][post_id]'); ?>" 
                              name="<?php echo esc_attr($this->get_field_name('moon_phases') . '[' . $index . '][post_id]'); ?>">
                          <option value="">None</option>
                          <?php
                          $posts = get_posts(array('post_type' => 'post', 'numberposts' => -1));
                          foreach ($posts as $post) {
                              $selected = ($moon['post_id'] == $post->ID) ? 'selected' : '';
                              echo '<option value="' . esc_attr($post->ID) . '" ' . $selected . '>' . esc_html($post->post_title) . '</option>';
                          }
                          ?>
                      </select>
                  </p>
                  <p><a href="#" class="remove-moon-phase">Remove</a></p>
              </div>
          <?php } ?>
      </div>
      
      <p><button class="button add-moon-phase">Add Moon Phase</button></p>

      <script>
          (function($) {
              $(document).on('click', '.add-moon-phase', function(e) {
                  e.preventDefault();
                  var wrapper = $('.moon-phases-wrapper');
                  var index = wrapper.find('.moon-phase-group').length;
                  var newField = `
                      <div class="moon-phase-group">
                          <h4>Moon Phase ` + (index + 1) + `</h4>
                          <p>
                              <label>Date:</label>
                              <input class="widefat" name="<?php echo esc_attr($this->get_field_name('moon_phases')); ?>[` + index + `][date]" type="date">
                          </p>
                          <p>
                              <label>Post:</label>
                              <select class="widefat" name="<?php echo esc_attr($this->get_field_name('moon_phases')); ?>[` + index + `][post_id]">
                                  <option value="">None</option>
                                  <?php foreach ($posts as $post) { ?>
                                      <option value="<?php echo esc_attr($post->ID); ?>"><?php echo esc_html($post->post_title); ?></option>
                                  <?php } ?>
                              </select>
                          </p>
                          <p><a href="#" class="remove-moon-phase">Remove</a></p>
                      </div>`;
                  wrapper.append(newField);
              });

              $(document).on('click', '.remove-moon-phase', function(e) {
                  e.preventDefault();
                  $(this).closest('.moon-phase-group').remove();
              });
          })(jQuery);
      </script>
      <?php
  }

  // Save the widget form data
  public function update($new_instance, $old_instance) {
      $instance = array();
      $instance['title'] = (!empty($new_instance['title'])) ? strip_tags($new_instance['title']) : '';

      // Serialize moon phases data to store in the widget settings
      $moon_phases = array();
      if (!empty($new_instance['moon_phases'])) {
          foreach ($new_instance['moon_phases'] as $moon) {
              if (!empty($moon['date'])) {
                  $moon_phases[] = array(
                      'date' => sanitize_text_field($moon['date']),
                      'post_id' => !empty($moon['post_id']) ? intval($moon['post_id']) : false,
                  );
              }
          }
      }
      $instance['moon_phases'] = serialize($moon_phases);

      return $instance;
  }
}
